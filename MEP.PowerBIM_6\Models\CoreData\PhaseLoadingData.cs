using CommunityToolkit.Mvvm.ComponentModel;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model for phase loading data display in circuit edit window
    /// Used for both diversified and un-diversified phase loading summaries
    /// </summary>
    public partial class PhaseLoadingData : ObservableObject
    {
        /// <summary>
        /// Phase R loading value
        /// </summary>
        [ObservableProperty]
        private double _phaseR;

        /// <summary>
        /// Phase W (or Y) loading value
        /// </summary>
        [ObservableProperty]
        private double _phaseW;

        /// <summary>
        /// Phase B loading value
        /// </summary>
        [ObservableProperty]
        private double _phaseB;

        /// <summary>
        /// Revit Phase R value (for comparison)
        /// </summary>
        [ObservableProperty]
        private double _revitPhaseR;

        /// <summary>
        /// Revit Phase W value (for comparison)
        /// </summary>
        [ObservableProperty]
        private double _revitPhaseW;

        /// <summary>
        /// Revit Phase B value (for comparison)
        /// </summary>
        [ObservableProperty]
        private double _revitPhaseB;

        /// <summary>
        /// Constructor
        /// </summary>
        public PhaseLoadingData()
        {
        }

        /// <summary>
        /// Constructor with initial values
        /// </summary>
        /// <param name="phaseR">Phase R loading</param>
        /// <param name="phaseW">Phase W loading</param>
        /// <param name="phaseB">Phase B loading</param>
        /// <param name="revitPhaseR">Revit Phase R value</param>
        /// <param name="revitPhaseW">Revit Phase W value</param>
        /// <param name="revitPhaseB">Revit Phase B value</param>
        public PhaseLoadingData(double phaseR, double phaseW, double phaseB, 
                               double revitPhaseR, double revitPhaseW, double revitPhaseB)
        {
            PhaseR = phaseR;
            PhaseW = phaseW;
            PhaseB = phaseB;
            RevitPhaseR = revitPhaseR;
            RevitPhaseW = revitPhaseW;
            RevitPhaseB = revitPhaseB;
        }
    }
}
