﻿<Window
    x:Class="MEP.PowerBIM_6.Views.PowerBIMMessageDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="{Binding Title}"
    Width="500"
    Height="300"
    MinWidth="400"
    MinHeight="200"
    Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}"
    ResizeMode="CanResize"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>

    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <Border
            Grid.Row="0"
            Padding="16,12"
            Background="{DynamicResource PrimaryHueMidBrush}">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon
                    x:Name="HeaderIcon"
                    Width="24"
                    Height="24"
                    Margin="0,0,8,0"
                    VerticalAlignment="Center"
                    Foreground="White" />
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="16"
                    FontWeight="SemiBold"
                    Foreground="White"
                    Text="{Binding Title}" />
            </StackPanel>
        </Border>

        <!--  Content  -->
        <ScrollViewer
            Grid.Row="1"
            Padding="16"
            VerticalScrollBarVisibility="Auto">
            <TextBlock
                x:Name="MessageText"
                FontSize="12"
                LineHeight="18"
                Text="{Binding Message}"
                TextWrapping="Wrap" />
        </ScrollViewer>

        <!--  Button Panel  -->
        <Border
            Grid.Row="2"
            Padding="16"
            Background="{DynamicResource MaterialDesignDivider}">
            <StackPanel
                x:Name="ButtonPanel"
                HorizontalAlignment="Right"
                Orientation="Horizontal">
                <!--  Buttons will be added dynamically based on MessageBoxButton  -->
            </StackPanel>
        </Border>
    </Grid>
</Window>
