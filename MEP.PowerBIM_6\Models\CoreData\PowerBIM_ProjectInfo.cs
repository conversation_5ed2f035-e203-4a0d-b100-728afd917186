using System;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Independent PowerBIM Project Information class for MEP.PowerBIM_6
    /// Contains project-level data and settings for electrical calculations
    /// </summary>
    public class PowerBIM_ProjectInfo
    {
        #region Properties

        /// <summary>
        /// Revit document reference
        /// </summary>
        public Document Document { get; set; }

        /// <summary>
        /// Revit UI document reference
        /// </summary>
        public UIDocument UIDocument { get; set; }

        /// <summary>
        /// Project name from Revit
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// Project number from Revit
        /// </summary>
        public string ProjectNumber { get; set; }

        /// <summary>
        /// Project address
        /// </summary>
        public string ProjectAddress { get; set; }

        /// <summary>
        /// Client name
        /// </summary>
        public string ClientName { get; set; }

        /// <summary>
        /// Project status
        /// </summary>
        public string ProjectStatus { get; set; }

        /// <summary>
        /// Issue date
        /// </summary>
        public string IssueDate { get; set; }

        /// <summary>
        /// Author information
        /// </summary>
        public string Author { get; set; }

        /// <summary>
        /// Organization name
        /// </summary>
        public string Organization { get; set; }

        /// <summary>
        /// Building type
        /// </summary>
        public string BuildingType { get; set; }

        /// <summary>
        /// Electrical design standards
        /// </summary>
        public string ElectricalStandards { get; set; }

        /// <summary>
        /// Voltage level
        /// </summary>
        public double VoltageLevel { get; set; }

        /// <summary>
        /// Frequency
        /// </summary>
        public double Frequency { get; set; }

        /// <summary>
        /// Default cable installation method
        /// </summary>
        public string DefaultInstallationMethod { get; set; }

        /// <summary>
        /// Default derating factor
        /// </summary>
        public double DefaultDeratingFactor { get; set; }

        /// <summary>
        /// Default diversity factor
        /// </summary>
        public double DefaultDiversityFactor { get; set; }

        /// <summary>
        /// Maximum voltage drop percentage
        /// </summary>
        public double MaxVoltageDropPercent { get; set; }

        /// <summary>
        /// Indicates if project data is valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Job name (alias for ProjectName)
        /// </summary>
        public string JobName
        {
            get => ProjectName;
            set => ProjectName = value;
        }

        /// <summary>
        /// Job number (alias for ProjectNumber)
        /// </summary>
        public string JobNumber
        {
            get => ProjectNumber;
            set => ProjectNumber = value;
        }

        /// <summary>
        /// Engineer name
        /// </summary>
        public string Engineer { get; set; }

        /// <summary>
        /// Verifier name
        /// </summary>
        public string Verifier { get; set; }

        /// <summary>
        /// System voltage
        /// </summary>
        public double SystemVoltage { get; set; }

        /// <summary>
        /// Maximum system voltage drop percentage
        /// </summary>
        public double SystemVoltageDropMax { get; set; }

        /// <summary>
        /// Ambient temperature
        /// </summary>
        public int AmbientTemperature { get; set; }

        /// <summary>
        /// Clearing time for power circuits
        /// </summary>
        public double ClearingTimePower { get; set; }

        /// <summary>
        /// Clearing time for lighting circuits
        /// </summary>
        public double ClearingTimeLighting { get; set; }

        /// <summary>
        /// Clearing time for other circuits
        /// </summary>
        public double ClearingTimeOther { get; set; }

        /// <summary>
        /// Voltage drop calculation for power circuits
        /// </summary>
        public bool VoltageDropCalcPower { get; set; }

        /// <summary>
        /// Voltage drop calculation for lighting circuits
        /// </summary>
        public bool VoltageDropCalcLighting { get; set; }

        /// <summary>
        /// Voltage drop calculation for other circuits
        /// </summary>
        public bool VoltageDropCalcOther { get; set; }

        /// <summary>
        /// GPO calculation flag
        /// </summary>
        public bool GPOCalc { get; set; }

        /// <summary>
        /// GPO calculation integer setting
        /// Based on legacy GPO_Calc_Integer property
        /// Values: 0-100 = percentage of breaker rating, -1 = 1000W+100W rule, -2 = actual load
        /// </summary>
        public int GPO_Calc_Integer { get; set; }

        /// <summary>
        /// Clearing time for power circuits in milliseconds
        /// Alias for ClearingTimePower (legacy compatibility)
        /// </summary>
        public double Clearing_Time_Power => ClearingTimePower * 1000;

        /// <summary>
        /// Clearing time for lighting circuits in milliseconds
        /// Alias for ClearingTimeLighting (legacy compatibility)
        /// </summary>
        public double Clearing_Time_Lighting => ClearingTimeLighting * 1000;

        /// <summary>
        /// Extra length per circuit for power
        /// </summary>
        public double Length_ExtraPerCCT_Power { get; set; }

        /// <summary>
        /// Extra length per element for power
        /// </summary>
        public double Length_ExtraPerElement_Power { get; set; }

        /// <summary>
        /// Extra length per circuit for lighting
        /// </summary>
        public double Length_ExtraPerCCT_Lighting { get; set; }

        /// <summary>
        /// Extra length per element for lighting
        /// </summary>
        public double Length_ExtraPerElement_Lighting { get; set; }

        /// <summary>
        /// Extra length per circuit for other
        /// </summary>
        public double Length_ExtraPerCCT_Other { get; set; }

        /// <summary>
        /// Extra length per element for other
        /// </summary>
        public double Length_ExtraPerElement_Other { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor
        /// </summary>
        public PowerBIM_ProjectInfo()
        {
            InitializeDefaults();
        }

        /// <summary>
        /// Constructor with UIDocument
        /// </summary>
        /// <param name="uidoc">Revit UI document</param>
        public PowerBIM_ProjectInfo(UIDocument uidoc)
        {
            UIDocument = uidoc;
            Document = uidoc?.Document;
            
            InitializeDefaults();
            LoadProjectData();
        }

        /// <summary>
        /// Constructor with Document
        /// </summary>
        /// <param name="doc">Revit document</param>
        public PowerBIM_ProjectInfo(Document doc)
        {
            Document = doc;
            
            InitializeDefaults();
            LoadProjectData();
        }

        #endregion

        #region Methods

        /// <summary>
        /// Initialize default values
        /// </summary>
        private void InitializeDefaults()
        {
            VoltageLevel = 230.0; // Default voltage
            Frequency = 50.0; // Default frequency
            DefaultInstallationMethod = "Clipped Direct";
            DefaultDeratingFactor = 1.0;
            DefaultDiversityFactor = 1.0;
            MaxVoltageDropPercent = 5.0;
            ElectricalStandards = "IEC";
            IsValid = false;

            // Initialize PowerBIM-specific properties
            Engineer = string.Empty;
            Verifier = string.Empty;
            SystemVoltage = 415.0; // Default 3-phase voltage
            SystemVoltageDropMax = 5.0;
            AmbientTemperature = 30;
            ClearingTimePower = 0.4;
            ClearingTimeLighting = 5.0;
            ClearingTimeOther = 0.4;
            VoltageDropCalcPower = true;
            VoltageDropCalcLighting = true;
            VoltageDropCalcOther = true;
            GPOCalc = true;
            GPO_Calc_Integer = 80; // Default to 80% of breaker rating
            Length_ExtraPerCCT_Power = 0.0;
            Length_ExtraPerElement_Power = 0.0;
            Length_ExtraPerCCT_Lighting = 0.0;
            Length_ExtraPerElement_Lighting = 0.0;
            Length_ExtraPerCCT_Other = 0.0;
            Length_ExtraPerElement_Other = 0.0;
        }

        /// <summary>
        /// Load project data from Revit document
        /// </summary>
        private void LoadProjectData()
        {
            if (Document == null) return;

            try
            {
                // Get project information from Revit
                var projInfo = Document.ProjectInformation;
                
                if (projInfo != null)
                {
                    ProjectName = GetParameterValue(projInfo, BuiltInParameter.PROJECT_NAME) ?? "Unknown Project";
                    ProjectNumber = GetParameterValue(projInfo, BuiltInParameter.PROJECT_NUMBER) ?? "000000";
                    ProjectAddress = GetParameterValue(projInfo, BuiltInParameter.PROJECT_ADDRESS) ?? "";
                    ClientName = GetParameterValue(projInfo, BuiltInParameter.CLIENT_NAME) ?? "";
                    ProjectStatus = GetParameterValue(projInfo, BuiltInParameter.PROJECT_STATUS) ?? "";
                    IssueDate = GetParameterValue(projInfo, BuiltInParameter.PROJECT_ISSUE_DATE) ?? "";
                    Author = GetParameterValue(projInfo, BuiltInParameter.PROJECT_AUTHOR) ?? "";
                    Organization = GetParameterValue(projInfo, BuiltInParameter.PROJECT_ORGANIZATION_NAME) ?? "";
                    BuildingType = GetParameterValue(projInfo, BuiltInParameter.PROJECT_BUILDING_TYPE) ?? "";
                }

                // Load electrical-specific parameters if they exist
                LoadElectricalParameters();

                // Load PowerBIM-specific parameters
                LoadPowerBIMParameters();

                IsValid = true;
            }
            catch (Exception ex)
            {
                // Log error but don't throw - use defaults
                System.Diagnostics.Debug.WriteLine($"Error loading project data: {ex.Message}");
                IsValid = false;
            }
        }

        /// <summary>
        /// Load electrical-specific parameters
        /// </summary>
        private void LoadElectricalParameters()
        {
            // Try to load custom electrical parameters if they exist
            // These would be project parameters set up for PowerBIM

            // For now, use defaults - in a real implementation, these would be
            // loaded from project parameters or a configuration file
        }

        /// <summary>
        /// Load PowerBIM-specific parameters from project
        /// </summary>
        private void LoadPowerBIMParameters()
        {
            try
            {
                // Try to load PowerBIM-specific project parameters
                var projInfo = Document?.ProjectInformation;
                if (projInfo != null)
                {
                    // Load engineer and verifier information
                    Engineer = GetParameterValue(projInfo, "Engineer") ?? Author ?? string.Empty;
                    Verifier = GetParameterValue(projInfo, "Verifier") ?? string.Empty;

                    // Load electrical system parameters
                    if (double.TryParse(GetParameterValue(projInfo, "System Voltage"), out double sysVoltage))
                    {
                        SystemVoltage = sysVoltage;
                    }

                    if (double.TryParse(GetParameterValue(projInfo, "Max Voltage Drop"), out double maxVD))
                    {
                        SystemVoltageDropMax = maxVD;
                    }

                    if (int.TryParse(GetParameterValue(projInfo, "Ambient Temperature"), out int ambTemp))
                    {
                        AmbientTemperature = ambTemp;
                    }

                    // Load clearing times
                    if (double.TryParse(GetParameterValue(projInfo, "Clearing Time Power"), out double ctPower))
                    {
                        ClearingTimePower = ctPower;
                    }

                    if (double.TryParse(GetParameterValue(projInfo, "Clearing Time Lighting"), out double ctLighting))
                    {
                        ClearingTimeLighting = ctLighting;
                    }

                    if (double.TryParse(GetParameterValue(projInfo, "Clearing Time Other"), out double ctOther))
                    {
                        ClearingTimeOther = ctOther;
                    }

                    // Load calculation flags
                    string vdPower = GetParameterValue(projInfo, "VD Calc Power");
                    VoltageDropCalcPower = vdPower != "0" && vdPower?.ToLower() != "false";

                    string vdLighting = GetParameterValue(projInfo, "VD Calc Lighting");
                    VoltageDropCalcLighting = vdLighting != "0" && vdLighting?.ToLower() != "false";

                    string vdOther = GetParameterValue(projInfo, "VD Calc Other");
                    VoltageDropCalcOther = vdOther != "0" && vdOther?.ToLower() != "false";

                    string gpoCalc = GetParameterValue(projInfo, "GPO Calc");
                    GPOCalc = gpoCalc != "0" && gpoCalc?.ToLower() != "false";

                    // Load extra length parameters
                    if (double.TryParse(GetParameterValue(projInfo, "Extra Length Per CCT Power"), out double elCctPower))
                    {
                        Length_ExtraPerCCT_Power = elCctPower;
                    }

                    if (double.TryParse(GetParameterValue(projInfo, "Extra Length Per Element Power"), out double elElemPower))
                    {
                        Length_ExtraPerElement_Power = elElemPower;
                    }

                    if (double.TryParse(GetParameterValue(projInfo, "Extra Length Per CCT Lighting"), out double elCctLighting))
                    {
                        Length_ExtraPerCCT_Lighting = elCctLighting;
                    }

                    if (double.TryParse(GetParameterValue(projInfo, "Extra Length Per Element Lighting"), out double elElemLighting))
                    {
                        Length_ExtraPerElement_Lighting = elElemLighting;
                    }

                    if (double.TryParse(GetParameterValue(projInfo, "Extra Length Per CCT Other"), out double elCctOther))
                    {
                        Length_ExtraPerCCT_Other = elCctOther;
                    }

                    if (double.TryParse(GetParameterValue(projInfo, "Extra Length Per Element Other"), out double elElemOther))
                    {
                        Length_ExtraPerElement_Other = elElemOther;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading PowerBIM parameters: {ex.Message}");
                // Continue with defaults
            }
        }

        /// <summary>
        /// Get parameter value as string
        /// </summary>
        /// <param name="element">Element to get parameter from</param>
        /// <param name="paramId">Built-in parameter ID</param>
        /// <returns>Parameter value as string</returns>
        private string GetParameterValue(Element element, BuiltInParameter paramId)
        {
            try
            {
                Parameter param = element.get_Parameter(paramId);
                if (param != null && param.HasValue)
                {
                    switch (param.StorageType)
                    {
                        case StorageType.String:
                            return param.AsString();
                        case StorageType.Integer:
                            return param.AsInteger().ToString();
                        case StorageType.Double:
                            return param.AsDouble().ToString();
                        default:
                            return param.AsValueString();
                    }
                }
            }
            catch
            {
                // Ignore parameter read errors
            }
            
            return null;
        }

        /// <summary>
        /// Get parameter value by name from element
        /// </summary>
        /// <param name="element">Element to get parameter from</param>
        /// <param name="parameterName">Parameter name</param>
        /// <returns>Parameter value as string</returns>
        private string GetParameterValue(Element element, string parameterName)
        {
            try
            {
                Parameter param = element?.LookupParameter(parameterName);
                if (param != null && param.HasValue)
                {
                    switch (param.StorageType)
                    {
                        case StorageType.String:
                            return param.AsString();
                        case StorageType.Integer:
                            return param.AsInteger().ToString();
                        case StorageType.Double:
                            return param.AsDouble().ToString();
                        default:
                            return param.AsValueString();
                    }
                }
            }
            catch
            {
                // Ignore parameter read errors
            }

            return null;
        }

        /// <summary>
        /// Commit project information changes back to Revit
        /// </summary>
        public void CommitProjectInfo()
        {
            CommitToRevit();
        }

        /// <summary>
        /// Commit all changes to Revit (alias for CommitProjectInfo)
        /// </summary>
        public void CommitToRevit()
        {
            if (Document == null) return;

            try
            {
                using (Transaction trans = new Transaction(Document, "Update PowerBIM Project Info"))
                {
                    trans.Start();
                    
                    // Update project information parameters
                    var projInfo = Document.ProjectInformation;
                    if (projInfo != null)
                    {
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_NAME, ProjectName);
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_NUMBER, ProjectNumber);
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_ADDRESS, ProjectAddress);
                        SetParameterValue(projInfo, BuiltInParameter.CLIENT_NAME, ClientName);
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_STATUS, ProjectStatus);
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_AUTHOR, Author);
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_ORGANIZATION_NAME, Organization);
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_BUILDING_TYPE, BuildingType);
                    }
                    
                    trans.Commit();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error committing project info: {ex.Message}");
            }
        }

        /// <summary>
        /// Set parameter value
        /// </summary>
        /// <param name="element">Element to set parameter on</param>
        /// <param name="paramId">Built-in parameter ID</param>
        /// <param name="value">Value to set</param>
        private void SetParameterValue(Element element, BuiltInParameter paramId, string value)
        {
            try
            {
                Parameter param = element.get_Parameter(paramId);
                if (param != null && !param.IsReadOnly && !string.IsNullOrEmpty(value))
                {
                    param.Set(value);
                }
            }
            catch
            {
                // Ignore parameter write errors
            }
        }

        #endregion
    }
}
