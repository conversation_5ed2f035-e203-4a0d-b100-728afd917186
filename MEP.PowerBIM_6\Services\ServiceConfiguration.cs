using System;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Models.Enums;
using MEP.PowerBIM_6.Services.Interfaces;
using MEP.PowerBIM_6.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Service configuration for dependency injection container
    /// Configures all services and ViewModels for PowerBIM 6 WPF application
    /// </summary>
    public static class ServiceConfiguration
    {
        /// <summary>
        /// Configure all services for the application
        /// </summary>
        /// <param name="services">Service collection to configure</param>
        /// <param name="uiDocument">Current UI document</param>
        /// <param name="logger">Activity logger</param>
        /// <returns>Configured service collection</returns>
        public static IServiceCollection ConfigureServices(this IServiceCollection services, UIDocument uiDocument, BecaActivityLoggerData logger)
        {
            // Register core dependencies
            services.AddSingleton(uiDocument);
            services.AddSingleton(uiDocument.Document);
            services.AddSingleton(uiDocument.Application);
            services.AddSingleton(logger);

            // Register business services
            RegisterBusinessServices(services);

            // Register ViewModels
            RegisterViewModels(services);

            return services;
        }

        /// <summary>
        /// Register all business services
        /// </summary>
        /// <param name="services">Service collection</param>
        private static void RegisterBusinessServices(IServiceCollection services)
        {
            // Core services
            services.AddTransient<IRevitService, RevitService>();
            services.AddTransient<IDataService, DataService>();
            services.AddTransient<IExportService, ExportService>();
            services.AddTransient<IImportService, ImportService>();
            services.AddTransient<ICalculationService, CalculationService>();
            services.AddTransient<IPathEditingService, PathEditingService>();
            services.AddTransient<IDialogService, DialogService>();
            services.AddSingleton<INavigationService, NavigationService>();

            // Utility services
            services.AddSingleton<ICacheService, CacheService>();
            services.AddTransient<IValidationService, ValidationService>();
            services.AddTransient<ISettingsService, SettingsService>();
        }

        /// <summary>
        /// Register all ViewModels
        /// </summary>
        /// <param name="services">Service collection</param>
        private static void RegisterViewModels(IServiceCollection services)
        {
            // Main ViewModels - Singleton to share data across pages
            services.AddSingleton<MainViewModel>();

            services.AddTransient<CircuitEditViewModel>();
            services.AddTransient<DbEditViewModel>();
            services.AddTransient<AdvancedSettingsViewModel>();
            services.AddTransient<DbSettingsViewModel>();
            services.AddTransient<ExportViewModel>();
            services.AddTransient<ImportSettingsViewModel>();

            // Item ViewModels
            services.AddTransient<DistributionBoardItemViewModel>();
            services.AddTransient<CircuitItemViewModel>();
            services.AddTransient<ProjectInfoViewModel>();
        }

        /// <summary>
        /// Build and configure the service provider
        /// </summary>
        /// <param name="uiDocument">Current UI document</param>
        /// <param name="logger">Activity logger</param>
        /// <returns>Configured service provider</returns>
        public static IServiceProvider BuildServiceProvider(UIDocument uiDocument, BecaActivityLoggerData logger)
        {
            var services = new ServiceCollection();
            services.ConfigureServices(uiDocument, logger);
            return services.BuildServiceProvider();
        }
    }

    /// <summary>
    /// Additional service interfaces that need to be implemented
    /// </summary>
    
    /// <summary>
    /// Service for import operations (REVIT-SAFE: All synchronous)
    /// </summary>
    public interface IImportService
    {
        bool ImportFromCsv(string filePath, ImportSettings settings);
        List<ImportMatchResult> ProcessImportMatching(string filePath, List<string> existingItems);
        ValidationResult ValidateImportFile(string filePath);
    }

    /// <summary>
    /// Service for electrical calculation operations (REVIT-SAFE: All synchronous)
    /// Based on PowerBIM_Calculations from legacy PowerBIM_1.5
    /// </summary>
    public interface ICalculationService
    {
        // Distribution Board Calculations
        bool RecalculateDistributionBoard(PowerBIM_DBData distributionBoard);
        bool CalculateDiversifiedLoads(PowerBIM_DBData distributionBoard);
        bool UpdateDistributionBoardSummary(PowerBIM_DBData distributionBoard);

        // Circuit Calculations
        bool RunCircuitCalculations(PowerBIM_CircuitData circuit);
        bool UpdateCircuitProperties(PowerBIM_CircuitData circuit);
        double CalculatePowerBIMCurrent(PowerBIM_CircuitData circuit);

        // Voltage Drop Calculations
        double CalculateFinalCircuitVoltDrop(PowerBIM_CircuitData circuit, VoltDropCalculation vdRule);
        double CalculateFinalCircuitVoltDropPercentage(double voltDrop, int numberOfPoles);
        bool ValidateCircuitVoltDrop(double calculatedVD, double maxVD);
        bool ValidateSystemVoltDrop(double circuitVD, double dbVD, double systemMaxVD);

        // EFLI (Earth Fault Loop Impedance) Calculations
        bool CalculateAndValidateEFLI(PowerBIM_CircuitData circuit);
        double CalculateEFLI(PowerBIM_CircuitData circuit, double dbEFLI_R, double dbEFLI_X);
        double CalculateEFLI_ToFirst(PowerBIM_CircuitData circuit, double dbEFLI_R, double dbEFLI_X);

        // Circuit Validation Checks
        bool RunAllCircuitChecks(PowerBIM_CircuitData circuit);
        int CountPassingChecks(PowerBIM_CircuitData circuit);

        // Cable and Breaker Calculations
        bool UpdateCableCalculations(PowerBIM_CableData cable, double current, double length);
        bool UpdateBreakerCalculations(PowerBIM_BreakerData breaker);

        // Clearing Time Calculations
        double DetermineClearingTime(PowerBIM_CircuitData circuit);

        // Diversity Calculations
        double CalculateDiversifiedCircuitLoad(PowerBIM_CircuitData circuit);
    }

    /// <summary>
    /// Service for path editing operations (REVIT-SAFE: All synchronous)
    /// </summary>
    public interface IPathEditingService
    {
        bool OpenPathCustomizingView();
        bool ActivatePathEditView();
        bool SetManualLength(object circuit, double length);
    }

    /// <summary>
    /// Service for dialog operations (REVIT-SAFE: All synchronous)
    /// </summary>
    public interface IDialogService
    {
        bool ShowMessage(string title, string message);
        bool ShowConfirmation(string title, string message);
        string ShowSaveFileDialog(string filter, string defaultFileName = null);
        string ShowOpenFileDialog(string filter);
        void ShowWindow<TViewModel>(TViewModel viewModel) where TViewModel : BaseViewModel;
    }

    /// <summary>
    /// Service for caching operations
    /// </summary>
    public interface ICacheService
    {
        T Get<T>(string key) where T : class;
        void Set<T>(string key, T value, TimeSpan? expiration = null) where T : class;
        void Remove(string key);
        void Clear();
    }

    /// <summary>
    /// Service for validation operations
    /// </summary>
    public interface IValidationService
    {
        ValidationResult ValidateProjectInfo(object projectInfo);
        ValidationResult ValidateDistributionBoard(object distributionBoard);
        ValidationResult ValidateCircuit(object circuit);
        ValidationResult ValidateSettings(object settings);
    }

    /// <summary>
    /// Service for settings management (REVIT-SAFE: All synchronous)
    /// </summary>
    public interface ISettingsService
    {
        T LoadSettings<T>() where T : class, new();
        bool SaveSettings<T>(T settings) where T : class;
        T GetDefaultSettings<T>() where T : class, new();
        void ResetToDefaults<T>() where T : class, new();
    }

    /// <summary>
    /// Import settings for CSV import operations
    /// </summary>
    public class ImportSettings
    {
        public bool HasHeaders { get; set; } = true;
        public string Delimiter { get; set; } = ",";
        public Dictionary<string, int> ColumnMapping { get; set; } = new Dictionary<string, int>();
        public bool OverwriteExisting { get; set; } = false;
    }

    /// <summary>
    /// Result of import matching operation
    /// </summary>
    public class ImportMatchResult
    {
        public string ImportItem { get; set; }
        public string MatchedItem { get; set; }
        public double MatchConfidence { get; set; }
        public bool IsExactMatch { get; set; }
        public List<string> PossibleMatches { get; set; } = new List<string>();
    }
}
