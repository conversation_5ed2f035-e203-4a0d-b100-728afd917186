using System;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Independent PowerBIM Cable Data class for MEP.PowerBIM_6
    /// Contains cable information and electrical calculations
    /// </summary>
    public class PowerBIM_CableData
    {
        #region Properties

        /// <summary>
        /// Cable name/designation
        /// </summary>
        public string Cable_Name { get; set; }

        /// <summary>
        /// Cable index in distribution board
        /// </summary>
        public int Cable_Index { get; set; }

        /// <summary>
        /// Cable cross-sectional area in mm²
        /// </summary>
        public double Cable_CSA_mm2 { get; set; }

        /// <summary>
        /// Cable material (Copper/Aluminium)
        /// </summary>
        public string Cable_Material { get; set; }

        /// <summary>
        /// Cable insulation type
        /// </summary>
        public string Cable_Insulation { get; set; }

        /// <summary>
        /// Cable installation method
        /// </summary>
        public string Installation_Method { get; set; }

        /// <summary>
        /// Installation method index
        /// </summary>
        public int Installation_Method_Index { get; set; }

        /// <summary>
        /// Current carrying capacity in amps
        /// </summary>
        public double Current_Carrying_Capacity { get; set; }

        /// <summary>
        /// Derating factor applied
        /// </summary>
        public double Derating_Factor { get; set; }

        /// <summary>
        /// Derated current carrying capacity
        /// </summary>
        public double Derated_Current_Capacity { get; set; }

        /// <summary>
        /// Cable resistance per km (R1)
        /// </summary>
        public double Cable_R1_Per_km { get; set; }

        /// <summary>
        /// Cable reactance per km (X1)
        /// </summary>
        public double Cable_X1_Per_km { get; set; }

        /// <summary>
        /// Cable impedance per km (Z1)
        /// </summary>
        public double Cable_Z1_Per_km { get; set; }

        /// <summary>
        /// Cable length in meters
        /// </summary>
        public double Cable_Length_m { get; set; }

        /// <summary>
        /// Total cable resistance (R1 * length)
        /// </summary>
        public double Total_Cable_R1 { get; set; }

        /// <summary>
        /// Total cable reactance (X1 * length)
        /// </summary>
        public double Total_Cable_X1 { get; set; }

        /// <summary>
        /// Total cable impedance (Z1 * length)
        /// </summary>
        public double Total_Cable_Z1 { get; set; }

        /// <summary>
        /// Voltage drop in volts
        /// </summary>
        public double Voltage_Drop_V { get; set; }

        /// <summary>
        /// Voltage drop percentage
        /// </summary>
        public double Voltage_Drop_Percent { get; set; }

        /// <summary>
        /// Short circuit withstand capability
        /// </summary>
        public double SC_Withstand_kA { get; set; }

        /// <summary>
        /// Short circuit withstand time
        /// </summary>
        public double SC_Withstand_Time_s { get; set; }

        /// <summary>
        /// Cable validation result
        /// </summary>
        public string Cable_Valid { get; set; }

        /// <summary>
        /// Current capacity check result
        /// </summary>
        public string Current_Capacity_Check { get; set; }

        /// <summary>
        /// Voltage drop check result
        /// </summary>
        public string Voltage_Drop_Check { get; set; }

        /// <summary>
        /// Short circuit withstand check result
        /// </summary>
        public string SC_Withstand_Check { get; set; }

        /// <summary>
        /// Indicates if data is good
        /// </summary>
        public bool Data_Good { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        public string Error_Message { get; set; }

        /// <summary>
        /// Indicates if this is cable to first element
        /// </summary>
        public bool is_CableToFirst { get; set; }

        /// <summary>
        /// Sp active value
        /// </summary>
        public double Sp_Active { get; set; }

        /// <summary>
        /// Sp earth value
        /// </summary>
        public double Sp_Earth { get; set; }

        /// <summary>
        /// R rated active resistance
        /// </summary>
        public double R_Rated_Active { get; set; }

        /// <summary>
        /// R rated earth resistance
        /// </summary>
        public double R_Rated_Earth { get; set; }

        /// <summary>
        /// R operating active resistance
        /// </summary>
        public double R_Operating_Active { get; set; }

        /// <summary>
        /// R operating earth resistance
        /// </summary>
        public double R_Operating_Earth { get; set; }

        /// <summary>
        /// X max active reactance
        /// </summary>
        public double X_Max_Active { get; set; }

        /// <summary>
        /// X max earth reactance
        /// </summary>
        public double X_Max_Earth { get; set; }

        /// <summary>
        /// Z operating active impedance
        /// </summary>
        public double Z_Operating_Active { get; set; }

        /// <summary>
        /// Z operating earth impedance
        /// </summary>
        public double Z_Operating_Earth { get; set; }

        /// <summary>
        /// I rated current
        /// </summary>
        public double I_Rated { get; set; }

        /// <summary>
        /// Temperature
        /// </summary>
        public double Temperature { get; set; }

        /// <summary>
        /// K value for short circuit calculations
        /// </summary>
        public double K_Value { get; set; }

        /// <summary>
        /// I²t maximum value
        /// </summary>
        public double I2t_Max { get; set; }

        /// <summary>
        /// Conductor material
        /// </summary>
        public string Conductor_Material { get; set; }

        /// <summary>
        /// Insulation material
        /// </summary>
        public string Insulation_Material { get; set; }

        /// <summary>
        /// Cable temperature limit
        /// </summary>
        public double Cable_Temperature_Limit { get; set; }

        /// <summary>
        /// Warning for user defined cable selected
        /// </summary>
        public bool Warning_UserDefCableSelected { get; set; }

        /// <summary>
        /// Warning for emergency lighting present
        /// </summary>
        public bool Warning_EmLightingPresent { get; set; }

        #endregion

        #region Static Properties

        /// <summary>
        /// Invalid cable name constant
        /// </summary>
        public static string InvalidCableName => "INVALID_CABLE";

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor
        /// </summary>
        public PowerBIM_CableData()
        {
            InitializeDefaults();
        }

        /// <summary>
        /// Constructor with cable designation
        /// </summary>
        /// <param name="cableDesignation">Cable designation (e.g., "2.5mm²")</param>
        public PowerBIM_CableData(string cableDesignation)
        {
            InitializeDefaults();
            LoadFromCableDesignation(cableDesignation);
        }

        #endregion

        #region Methods

        /// <summary>
        /// Initialize default values
        /// </summary>
        private void InitializeDefaults()
        {
            Cable_Name = "2.5mm²";
            Cable_Index = 0;
            Cable_CSA_mm2 = 2.5;
            Cable_Material = "Copper";
            Cable_Insulation = "PVC";
            Installation_Method = "Clipped Direct";
            Installation_Method_Index = 1;
            
            Current_Carrying_Capacity = 27.0; // Typical for 2.5mm² copper
            Derating_Factor = 1.0;
            Derated_Current_Capacity = 27.0;
            
            Cable_R1_Per_km = 7.41; // Typical for 2.5mm² copper
            Cable_X1_Per_km = 0.0;
            Cable_Z1_Per_km = 7.41;
            Cable_Length_m = 0.0;
            
            Total_Cable_R1 = 0.0;
            Total_Cable_X1 = 0.0;
            Total_Cable_Z1 = 0.0;
            
            Voltage_Drop_V = 0.0;
            Voltage_Drop_Percent = 0.0;
            
            SC_Withstand_kA = 1.0;
            SC_Withstand_Time_s = 1.0;
            
            Cable_Valid = "PENDING";
            Current_Capacity_Check = "PENDING";
            Voltage_Drop_Check = "PENDING";
            SC_Withstand_Check = "PENDING";
            Data_Good = false;
            Error_Message = string.Empty;

            // Initialize additional properties
            is_CableToFirst = true;
            Sp_Active = 0.0;
            Sp_Earth = 0.0;
            R_Rated_Active = Cable_R1_Per_km;
            R_Rated_Earth = Cable_R1_Per_km;
            R_Operating_Active = Cable_R1_Per_km;
            R_Operating_Earth = Cable_R1_Per_km;
            X_Max_Active = Cable_X1_Per_km;
            X_Max_Earth = Cable_X1_Per_km;
            Z_Operating_Active = Cable_Z1_Per_km;
            Z_Operating_Earth = Cable_Z1_Per_km;
            I_Rated = Current_Carrying_Capacity;
            Temperature = 70.0; // Typical operating temperature
            K_Value = 115.0; // Typical K value for PVC copper
            I2t_Max = 0.0;
            Conductor_Material = Cable_Material;
            Insulation_Material = Cable_Insulation;
            Cable_Temperature_Limit = 70.0;
            Warning_UserDefCableSelected = false;
            Warning_EmLightingPresent = false;
        }

        /// <summary>
        /// Load cable data from designation string
        /// </summary>
        /// <param name="cableDesignation">Cable designation (e.g., "2.5mm²", "4.0mm²")</param>
        public void LoadFromCableDesignation(string cableDesignation)
        {
            try
            {
                Cable_Name = cableDesignation ?? "2.5mm²";
                
                // Extract CSA from designation
                string numericPart = cableDesignation?.Replace("mm²", "").Replace("mm", "").Trim() ?? "2.5";
                if (double.TryParse(numericPart, out double csa))
                {
                    Cable_CSA_mm2 = csa;
                    
                    // Set typical values based on CSA (simplified lookup)
                    SetTypicalCableProperties(csa);
                }
                
                Data_Good = true;
                Cable_Valid = "VALID";
            }
            catch (Exception ex)
            {
                Data_Good = false;
                Error_Message = ex.Message;
                Cable_Valid = "ERROR";
            }
        }

        /// <summary>
        /// Set typical cable properties based on CSA
        /// </summary>
        /// <param name="csa">Cable cross-sectional area</param>
        private void SetTypicalCableProperties(double csa)
        {
            // Simplified lookup table for common cable sizes
            switch (csa)
            {
                case 1.0:
                    Current_Carrying_Capacity = 17.0;
                    Cable_R1_Per_km = 18.1;
                    break;
                case 1.5:
                    Current_Carrying_Capacity = 22.0;
                    Cable_R1_Per_km = 12.1;
                    break;
                case 2.5:
                    Current_Carrying_Capacity = 27.0;
                    Cable_R1_Per_km = 7.41;
                    break;
                case 4.0:
                    Current_Carrying_Capacity = 37.0;
                    Cable_R1_Per_km = 4.61;
                    break;
                case 6.0:
                    Current_Carrying_Capacity = 47.0;
                    Cable_R1_Per_km = 3.08;
                    break;
                case 10.0:
                    Current_Carrying_Capacity = 64.0;
                    Cable_R1_Per_km = 1.83;
                    break;
                case 16.0:
                    Current_Carrying_Capacity = 85.0;
                    Cable_R1_Per_km = 1.15;
                    break;
                case 25.0:
                    Current_Carrying_Capacity = 112.0;
                    Cable_R1_Per_km = 0.727;
                    break;
                default:
                    // Linear interpolation for other sizes
                    Current_Carrying_Capacity = Math.Max(17.0, csa * 10.0);
                    Cable_R1_Per_km = Math.Max(0.5, 18.1 / csa);
                    break;
            }
            
            Cable_Z1_Per_km = Cable_R1_Per_km; // Simplified - ignoring reactance
            Derated_Current_Capacity = Current_Carrying_Capacity * Derating_Factor;
        }

        /// <summary>
        /// Calculate voltage drop for given current and length
        /// </summary>
        /// <param name="current">Circuit current in amps</param>
        /// <param name="length">Cable length in meters</param>
        /// <param name="systemVoltage">System voltage</param>
        public void CalculateVoltageDrop(double current, double length, double systemVoltage = 230.0)
        {
            try
            {
                Cable_Length_m = length;
                Total_Cable_R1 = Cable_R1_Per_km * length / 1000.0;
                Total_Cable_Z1 = Cable_Z1_Per_km * length / 1000.0;
                
                // Voltage drop = I * Z * length (simplified single phase)
                Voltage_Drop_V = current * Total_Cable_Z1;
                Voltage_Drop_Percent = (Voltage_Drop_V / systemVoltage) * 100.0;
                
                // Check voltage drop
                if (Voltage_Drop_Percent <= 5.0)
                {
                    Voltage_Drop_Check = "PASS";
                }
                else
                {
                    Voltage_Drop_Check = "FAIL - Exceeds 5%";
                }
            }
            catch (Exception ex)
            {
                Error_Message = ex.Message;
                Voltage_Drop_Check = "ERROR";
            }
        }

        /// <summary>
        /// Check current carrying capacity
        /// </summary>
        /// <param name="circuitCurrent">Circuit current in amps</param>
        public void CheckCurrentCapacity(double circuitCurrent)
        {
            try
            {
                if (circuitCurrent <= Derated_Current_Capacity)
                {
                    Current_Capacity_Check = "PASS";
                }
                else
                {
                    Current_Capacity_Check = $"FAIL - {circuitCurrent:F1}A > {Derated_Current_Capacity:F1}A";
                }
            }
            catch (Exception ex)
            {
                Error_Message = ex.Message;
                Current_Capacity_Check = "ERROR";
            }
        }

        /// <summary>
        /// Validate cable data
        /// </summary>
        public void ValidateCable()
        {
            try
            {
                if (Cable_CSA_mm2 <= 0)
                {
                    Cable_Valid = "INVALID - No CSA";
                    Data_Good = false;
                    return;
                }

                if (Current_Carrying_Capacity <= 0)
                {
                    Cable_Valid = "INVALID - No capacity";
                    Data_Good = false;
                    return;
                }

                Cable_Valid = "VALID";
                Data_Good = true;
                Error_Message = string.Empty;
            }
            catch (Exception ex)
            {
                Cable_Valid = "ERROR";
                Data_Good = false;
                Error_Message = ex.Message;
            }
        }

        /// <summary>
        /// Update cable data with latest calculations
        /// </summary>
        public void UpdateCableData()
        {
            try
            {
                // Recalculate derived properties
                SetTypicalCableProperties(Cable_CSA_mm2);

                // Update resistance/reactance/impedance values
                R_Rated_Active = Cable_R1_Per_km;
                R_Rated_Earth = Cable_R1_Per_km;
                R_Operating_Active = Cable_R1_Per_km;
                R_Operating_Earth = Cable_R1_Per_km;
                X_Max_Active = Cable_X1_Per_km;
                X_Max_Earth = Cable_X1_Per_km;
                Z_Operating_Active = Cable_Z1_Per_km;
                Z_Operating_Earth = Cable_Z1_Per_km;

                // Update current capacity
                I_Rated = Current_Carrying_Capacity;
                Derated_Current_Capacity = Current_Carrying_Capacity * Derating_Factor;

                // Recalculate voltage drop if length is available
                if (Cable_Length_m > 0)
                {
                    CalculateVoltageDrop(I_Rated, Cable_Length_m);
                }

                // Validate updated data
                ValidateCable();

                Data_Good = true;
                Error_Message = string.Empty;
            }
            catch (Exception ex)
            {
                Data_Good = false;
                Error_Message = ex.Message;
                Cable_Valid = "ERROR";
            }
        }

        /// <summary>
        /// Create null entry for invalid cable
        /// </summary>
        public void CreateNullEntry()
        {
            try
            {
                // Set to invalid cable state
                Cable_Name = InvalidCableName;
                Cable_Index = -1;
                Cable_CSA_mm2 = 0.0;
                Current_Carrying_Capacity = 0.0;
                Derated_Current_Capacity = 0.0;
                Cable_R1_Per_km = 0.0;
                Cable_X1_Per_km = 0.0;
                Cable_Z1_Per_km = 0.0;

                // Reset all electrical values
                R_Rated_Active = 0.0;
                R_Rated_Earth = 0.0;
                R_Operating_Active = 0.0;
                R_Operating_Earth = 0.0;
                X_Max_Active = 0.0;
                X_Max_Earth = 0.0;
                Z_Operating_Active = 0.0;
                Z_Operating_Earth = 0.0;
                I_Rated = 0.0;

                // Set validation results
                Cable_Valid = "INVALID";
                Current_Capacity_Check = "INVALID";
                Voltage_Drop_Check = "INVALID";
                SC_Withstand_Check = "INVALID";

                Data_Good = false;
                Error_Message = "Invalid cable - null entry created";
            }
            catch (Exception ex)
            {
                Error_Message = $"Error creating null entry: {ex.Message}";
            }
        }

        #endregion
    }
}
