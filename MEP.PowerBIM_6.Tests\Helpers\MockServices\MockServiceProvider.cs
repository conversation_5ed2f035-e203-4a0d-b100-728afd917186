using System;
using System.Collections.Generic;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_6.Services;
using MEP.PowerBIM_6.Services.Interfaces;
using MEP.PowerBIM_6.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Moq;

namespace MEP.PowerBIM_6.Tests.Helpers.MockServices
{
    /// <summary>
    /// Mock service provider for testing
    /// Provides pre-configured mock services for unit testing
    /// </summary>
    public static class MockServiceProvider
    {
        /// <summary>
        /// Create a service provider with all mock services configured
        /// </summary>
        public static IServiceProvider CreateWithMockServices()
        {
            var services = new ServiceCollection();
            
            // Add mock logger
            var mockLogger = new Mock<BecaActivityLoggerData>();
            services.AddSingleton(mockLogger.Object);
            
            // Add mock services
            services.AddSingleton(CreateMockNavigationService());
            services.AddSingleton(CreateMockDataService());
            services.AddSingleton(CreateMockRevitService());
            services.AddSingleton(CreateMockExportService());
            services.AddSingleton(CreateMockImportService());
            services.AddSingleton(CreateMockCalculationService());
            services.AddSingleton(CreateMockDialogService());
            services.AddSingleton(CreateMockCacheService());
            services.AddSingleton(CreateMockValidationService());
            services.AddSingleton(CreateMockSettingsService());
            
            // Add ViewModels (using real implementations with mock services)
            services.AddSingleton<MainViewModel>();
            services.AddTransient<CircuitEditViewModel>();
            
            return services.BuildServiceProvider();
        }

        /// <summary>
        /// Create a service provider with specific mock configurations
        /// </summary>
        public static IServiceProvider CreateWithCustomMocks(Action<ServiceCollection> configureMocks)
        {
            var services = new ServiceCollection();
            configureMocks(services);
            return services.BuildServiceProvider();
        }

        #region Mock Service Factories

        public static INavigationService CreateMockNavigationService()
        {
            var mock = new Mock<INavigationService>();
            
            // Setup common navigation behaviors
            mock.Setup(x => x.NavigateTo(It.IsAny<string>(), It.IsAny<object>()))
                .Returns(true);
            
            mock.Setup(x => x.GoBack())
                .Returns(true);
            
            mock.Setup(x => x.CanGoBack)
                .Returns(true);
            
            mock.Setup(x => x.CurrentPageKey)
                .Returns("Home");
            
            return mock.Object;
        }

        public static IDataService CreateMockDataService()
        {
            var mock = new Mock<IDataService>();
            
            // Setup data transformation methods
            mock.Setup(x => x.ConvertToModel(It.IsAny<PowerBIM_ProjectInfo>()))
                .Returns((PowerBIM_ProjectInfo pi) => new ProjectInfoModel(pi));
            
            mock.Setup(x => x.ConvertToModel(It.IsAny<PowerBIM_DBData>()))
                .Returns((PowerBIM_DBData db) => new DistributionBoardModel(db));
            
            mock.Setup(x => x.ValidateProjectInfo(It.IsAny<ProjectInfoModel>()))
                .Returns(new ValidationResult());
            
            mock.Setup(x => x.ValidateDistributionBoard(It.IsAny<DistributionBoardModel>()))
                .Returns(new ValidationResult());
            
            mock.Setup(x => x.LoadSettings())
                .Returns(new SettingsModel());
            
            mock.Setup(x => x.SaveSettings(It.IsAny<SettingsModel>()))
                .Returns(true);
            
            return mock.Object;
        }

        public static IRevitService CreateMockRevitService()
        {
            var mock = new Mock<IRevitService>();
            
            // Setup Revit API operations
            mock.Setup(x => x.LoadProjectInfo())
                .Returns(new ProjectInfoModel(new PowerBIM_ProjectInfo()));
            
            mock.Setup(x => x.SaveProjectInfo(It.IsAny<ProjectInfoModel>()))
                .Returns(true);
            
            mock.Setup(x => x.LoadDistributionBoards())
                .Returns(new List<DistributionBoardModel>());
            
            mock.Setup(x => x.SaveDistributionBoard(It.IsAny<DistributionBoardModel>()))
                .Returns(true);
            
            mock.Setup(x => x.UpdateCircuits(It.IsAny<List<DistributionBoardModel>>()))
                .Returns(true);
            
            mock.Setup(x => x.ExecuteInTransaction(It.IsAny<string>(), It.IsAny<Func<bool>>()))
                .Returns((string name, Func<bool> operation) => operation());
            
            return mock.Object;
        }

        public static IExportService CreateMockExportService()
        {
            var mock = new Mock<IExportService>();
            
            // Setup export operations
            mock.Setup(x => x.ExportToExcel(It.IsAny<ExportSettingsModel>(), It.IsAny<IProgress<ExportProgress>>()))
                .Returns(true);
            
            mock.Setup(x => x.ExportToCsv(It.IsAny<ExportSettingsModel>(), It.IsAny<IProgress<ExportProgress>>()))
                .Returns(true);
            
            mock.Setup(x => x.ValidateExportSettings(It.IsAny<ExportSettingsModel>()))
                .Returns(new ValidationResult());
            
            mock.Setup(x => x.GetAvailableTemplates())
                .Returns(new List<ExportTemplate>
                {
                    new ExportTemplate { Name = "Standard", Description = "Standard export template" },
                    new ExportTemplate { Name = "Detailed", Description = "Detailed export template" }
                });
            
            return mock.Object;
        }

        public static IImportService CreateMockImportService()
        {
            var mock = new Mock<IImportService>();
            
            mock.Setup(x => x.ImportFromCsv(It.IsAny<string>(), It.IsAny<ImportSettings>()))
                .Returns(true);
            
            mock.Setup(x => x.ValidateImportFile(It.IsAny<string>()))
                .Returns(new ValidationResult());
            
            mock.Setup(x => x.ProcessImportMatching(It.IsAny<string>(), It.IsAny<List<string>>()))
                .Returns(new List<ImportMatchResult>());
            
            return mock.Object;
        }

        public static ICalculationService CreateMockCalculationService()
        {
            var mock = new Mock<ICalculationService>();
            
            // Setup calculation methods
            mock.Setup(x => x.RecalculateDistributionBoard(It.IsAny<PowerBIM_DBData>()))
                .Returns(true);
            
            mock.Setup(x => x.RunCircuitCalculations(It.IsAny<PowerBIM_CircuitData>()))
                .Returns(true);
            
            mock.Setup(x => x.CalculatePowerBIMCurrent(It.IsAny<PowerBIM_CircuitData>()))
                .Returns(10.5); // Mock current value
            
            mock.Setup(x => x.CalculateFinalCircuitVoltDrop(It.IsAny<PowerBIM_CircuitData>(), It.IsAny<VoltDropCalculation>()))
                .Returns(2.5); // Mock voltage drop
            
            mock.Setup(x => x.RunAllCircuitChecks(It.IsAny<PowerBIM_CircuitData>()))
                .Returns(true);
            
            return mock.Object;
        }

        public static IDialogService CreateMockDialogService()
        {
            var mock = new Mock<IDialogService>();
            
            mock.Setup(x => x.ShowMessage(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(true);
            
            mock.Setup(x => x.ShowConfirmation(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(true);
            
            mock.Setup(x => x.ShowSaveFileDialog(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(@"C:\Temp\TestFile.xlsx");
            
            mock.Setup(x => x.ShowOpenFileDialog(It.IsAny<string>()))
                .Returns(@"C:\Temp\TestFile.csv");
            
            return mock.Object;
        }

        public static ICacheService CreateMockCacheService()
        {
            var mock = new Mock<ICacheService>();
            var cache = new Dictionary<string, object>();
            
            mock.Setup(x => x.Get<It.IsAnyType>(It.IsAny<string>()))
                .Returns((string key) => cache.ContainsKey(key) ? (object)cache[key] : null);
            
            mock.Setup(x => x.Set(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<TimeSpan?>()))
                .Callback((string key, object value, TimeSpan? expiration) => cache[key] = value);
            
            mock.Setup(x => x.Remove(It.IsAny<string>()))
                .Callback((string key) => cache.Remove(key));
            
            mock.Setup(x => x.Clear())
                .Callback(() => cache.Clear());
            
            return mock.Object;
        }

        public static IValidationService CreateMockValidationService()
        {
            var mock = new Mock<IValidationService>();
            
            mock.Setup(x => x.ValidateProjectInfo(It.IsAny<object>()))
                .Returns(new ValidationResult());
            
            mock.Setup(x => x.ValidateDistributionBoard(It.IsAny<object>()))
                .Returns(new ValidationResult());
            
            mock.Setup(x => x.ValidateCircuit(It.IsAny<object>()))
                .Returns(new ValidationResult());
            
            mock.Setup(x => x.ValidateSettings(It.IsAny<object>()))
                .Returns(new ValidationResult());
            
            return mock.Object;
        }

        public static ISettingsService CreateMockSettingsService()
        {
            var mock = new Mock<ISettingsService>();
            
            mock.Setup(x => x.LoadSettings<It.IsAnyType>())
                .Returns(() => Activator.CreateInstance<object>());
            
            mock.Setup(x => x.SaveSettings(It.IsAny<object>()))
                .Returns(true);
            
            mock.Setup(x => x.GetDefaultSettings<It.IsAnyType>())
                .Returns(() => Activator.CreateInstance<object>());
            
            return mock.Object;
        }

        #endregion

        #region Specialized Mock Configurations

        /// <summary>
        /// Create a mock data service that returns validation errors
        /// </summary>
        public static IDataService CreateMockDataServiceWithValidationErrors()
        {
            var mock = new Mock<IDataService>();
            
            mock.Setup(x => x.ValidateProjectInfo(It.IsAny<ProjectInfoModel>()))
                .Returns(new ValidationResult(false, "Job name is required", "Invalid voltage drop"));
            
            mock.Setup(x => x.ValidateDistributionBoard(It.IsAny<DistributionBoardModel>()))
                .Returns(new ValidationResult(false, "Distribution board name is required"));
            
            return mock.Object;
        }

        /// <summary>
        /// Create a mock export service that simulates failures
        /// </summary>
        public static IExportService CreateMockExportServiceWithFailures()
        {
            var mock = new Mock<IExportService>();
            
            mock.Setup(x => x.ExportToExcel(It.IsAny<ExportSettingsModel>(), It.IsAny<IProgress<ExportProgress>>()))
                .Returns(false);
            
            mock.Setup(x => x.ValidateExportSettings(It.IsAny<ExportSettingsModel>()))
                .Returns(new ValidationResult(false, "Output path is invalid"));
            
            return mock.Object;
        }

        /// <summary>
        /// Create a mock navigation service that simulates navigation failures
        /// </summary>
        public static INavigationService CreateMockNavigationServiceWithFailures()
        {
            var mock = new Mock<INavigationService>();
            
            mock.Setup(x => x.NavigateTo(It.IsAny<string>(), It.IsAny<object>()))
                .Returns(false);
            
            mock.Setup(x => x.GoBack())
                .Returns(false);
            
            mock.Setup(x => x.CanGoBack)
                .Returns(false);
            
            return mock.Object;
        }

        #endregion
    }
}
