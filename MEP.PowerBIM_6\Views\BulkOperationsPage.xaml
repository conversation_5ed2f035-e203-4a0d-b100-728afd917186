<Page
    x:Class="MEP.PowerBIM_6.Views.BulkOperationsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Bulk Operations"
    Background="White" Width="600"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="16">
            <!--  Page Header  -->
            <StackPanel Margin="0,0,0,24">
                <TextBlock Style="{StaticResource MaterialDesignHeadline4TextBlock}" Text="Bulk Operations" />
                <TextBlock
                    Margin="0,4,0,0"
                    Foreground="{DynamicResource MaterialDesignBodyLight}"
                    Text="Perform bulk operations on multiple circuits and distribution boards" />
            </StackPanel>

            <!--  Bulk Circuit Operations  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Bulk Circuit Operations" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  Lighting Circuits  -->
                        <StackPanel Grid.Column="0" Margin="0,0,16,0">
                            <TextBlock
                                Margin="0,0,0,12"
                                FontWeight="SemiBold"
                                Text="Lighting Circuits" />

                            <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding BulkEditLightingCommand}"
                                Content="Bulk Edit Lighting Circuits" />

                            <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding AutoSizeLightingCablesCommand}"
                                Content="Auto Size Lighting Cables" />

                            <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                                HorizontalAlignment="Stretch"
                                Command="{Binding SetLightingDiversityCommand}"
                                Content="Set Lighting Diversity Factors" />
                        </StackPanel>

                        <!--  Power Circuits  -->
                        <StackPanel Grid.Column="1">
                            <TextBlock
                                Margin="0,0,0,12"
                                FontWeight="SemiBold"
                                Text="Power Circuits" />

                            <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding BulkEditPowerCommand}"
                                Content="Bulk Edit Power Circuits" />

                            <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding AutoSizePowerCablesCommand}"
                                Content="Auto Size Power Cables" />

                            <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                                HorizontalAlignment="Stretch"
                                Command="{Binding SetPowerDiversityCommand}"
                                Content="Set Power Diversity Factors" />
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Auto Sizing Operations  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Automatic Sizing" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  Cable Sizing  -->
                        <StackPanel Grid.Column="0" Margin="0,0,16,0">
                            <TextBlock
                                Margin="0,0,0,12"
                                FontWeight="SemiBold"
                                Text="Cable Sizing" />

                            <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding RunAutoSizerCommand}"
                                Content="Run Complete Auto Sizer" />

                            <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding SizeAllCablesCommand}"
                                Content="Size All Cables" />

                            <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                                HorizontalAlignment="Stretch"
                                Command="{Binding OptimizeCableSizingCommand}"
                                Content="Optimize Cable Sizing" />
                        </StackPanel>

                        <!--  Breaker Sizing  -->
                        <StackPanel Grid.Column="1">
                            <TextBlock
                                Margin="0,0,0,12"
                                FontWeight="SemiBold"
                                Text="Protection Sizing" />

                            <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding SizeAllBreakersCommand}"
                                Content="Size All Breakers" />

                            <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding ValidateProtectionCommand}"
                                Content="Validate Protection Coordination" />

                            <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                                HorizontalAlignment="Stretch"
                                Command="{Binding CheckDiscriminationCommand}"
                                Content="Check Discrimination" />
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Data Operations  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Data Operations" />

                    <UniformGrid Columns="3" Rows="2">
                        <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                            Margin="4"
                            Command="{Binding ImportFromCsvCommand}"
                            Content="Import from CSV" />

                        <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                            Margin="4"
                            Command="{Binding ExportToExcelCommand}"
                            Content="Export to Excel" />

                        <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                            Margin="4"
                            Command="{Binding GenerateReportCommand}"
                            Content="Generate Report" />

                        <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                            Margin="4"
                            Command="{Binding BackupDataCommand}"
                            Content="Backup Data" />

                        <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                            Margin="4"
                            Command="{Binding RestoreDataCommand}"
                            Content="Restore Data" />

                        <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                            Margin="4"
                            Command="{Binding ValidateAllDataCommand}"
                            Content="Validate All Data" />
                    </UniformGrid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Batch Processing Status  -->
            <materialDesign:Card Padding="24" Visibility="{Binding IsBatchProcessing, Converter={StaticResource BoolToVisConverter}}">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Batch Processing Status" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Margin="0,0,0,8" Text="{Binding BatchProcessingStatus}" />

                            <ProgressBar
                                Height="20"
                                Maximum="100"
                                Value="{Binding BatchProcessingProgress}" />

                            <TextBlock
                                Margin="0,8,0,0"
                                HorizontalAlignment="Center"
                                Text="{Binding BatchProcessingProgress, StringFormat='{}{0:F0}% Complete'}" />
                        </StackPanel>

                        <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                            Grid.Column="1"
                            Margin="16,0,0,0"
                            VerticalAlignment="Center"
                            Command="{Binding CancelBatchProcessingCommand}"
                            Content="Cancel" />
                    </Grid>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </ScrollViewer>
</Page>
