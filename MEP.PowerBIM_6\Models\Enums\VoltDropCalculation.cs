namespace MEP.PowerBIM_6.Models.Enums
{
    /// <summary>
    /// Voltage drop calculation methods
    /// Based on PowerBIM_1.5 VoltDropCalculation enum
    /// </summary>
    public enum VoltDropCalculation
    {
        /// <summary>
        /// Unknown calculation method
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Linear depreciating calculation (default)
        /// Uses half the voltage drop for the final cable section
        /// </summary>
        LinearDeprecating = 1,

        /// <summary>
        /// Lump load calculation
        /// Uses full voltage drop for entire cable length
        /// </summary>
        LumpLoad = 2
    }
}
