﻿using MaterialDesignThemes.Wpf;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using Application = System.Windows.Application;
using Button = System.Windows.Controls.Button;

namespace MEP.PowerBIM_6.Views
{
    /// <summary>
    /// Interaction logic for PowerBIMMessageDialog.xaml
    /// </summary>
    public partial class PowerBIMMessageDialog : Window, INotifyPropertyChanged
    {
        #region Private Fields

        private string _title;
        private string _message;
        private MessageBoxResult _result = MessageBoxResult.None;

        #endregion

        #region Public Properties

        public string Title
        {
            get => _title;
            set
            {
                _title = value;
                OnPropertyChanged();
            }
        }

        public string Message
        {
            get => _message;
            set
            {
                _message = value;
                OnPropertyChanged();
            }
        }

        public MessageBoxResult Result => _result;

        #endregion

        public PowerBIMMessageDialog(string message, string title = "PowerBIM", MessageBoxButton buttons = MessageBoxButton.OK, MessageBoxImage icon = MessageBoxImage.Information)
        {
            InitializeComponent();

            Title = title;
            Message = message;

            SetupIcon(icon);
            SetupButtons(buttons);

            DataContext = this;
        }

        #region Private Methods

        private void SetupIcon(MessageBoxImage icon)
        {
            PackIconKind iconKind = icon switch
            {
                MessageBoxImage.Error => PackIconKind.AlertCircle,
                MessageBoxImage.Warning => PackIconKind.Alert,
                MessageBoxImage.Question => PackIconKind.HelpCircle,
                MessageBoxImage.Information => PackIconKind.Information,
                _ => PackIconKind.Information
            };

            HeaderIcon.Kind = iconKind;
        }

        private void SetupButtons(MessageBoxButton buttons)
        {
            ButtonPanel.Children.Clear();

            switch (buttons)
            {
                case MessageBoxButton.OK:
                    AddButton("OK", MessageBoxResult.OK, true, true);
                    break;

                case MessageBoxButton.OKCancel:
                    AddButton("OK", MessageBoxResult.OK, true, false);
                    AddButton("Cancel", MessageBoxResult.Cancel, false, true);
                    break;

                case MessageBoxButton.YesNo:
                    AddButton("Yes", MessageBoxResult.Yes, true, false);
                    AddButton("No", MessageBoxResult.No, false, true);
                    break;

                case MessageBoxButton.YesNoCancel:
                    AddButton("Yes", MessageBoxResult.Yes, true, false);
                    AddButton("No", MessageBoxResult.No, false, false);
                    AddButton("Cancel", MessageBoxResult.Cancel, false, true);
                    break;
            }
        }

        private void AddButton(string content, MessageBoxResult result, bool isDefault, bool isCancel)
        {
            var button = new Button
            {
                Content = content,
                Margin = new Thickness(8, 0, 0, 0),
                MinWidth = 75,
                IsDefault = isDefault,
                IsCancel = isCancel
            };

            // Set button style based on type
            if (isDefault)
            {
                button.Style = (Style)FindResource("PrimaryButtonStyle");
            }
            else
            {
                button.Style = (Style)FindResource("SecondaryButtonStyle");
            }

            button.Click += (s, e) =>
            {
                _result = result;
                DialogResult = result != MessageBoxResult.Cancel && result != MessageBoxResult.No;
                Close();
            };

            ButtonPanel.Children.Add(button);
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region Static Methods

        /// <summary>
        /// Shows a message dialog
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The dialog title</param>
        /// <param name="buttons">The buttons to show</param>
        /// <param name="icon">The icon to display</param>
        /// <param name="owner">The owner window</param>
        /// <returns>The user's choice</returns>
        public static MessageBoxResult Show(string message, string title = "PowerBIM", MessageBoxButton buttons = MessageBoxButton.OK, MessageBoxImage icon = MessageBoxImage.Information, Window owner = null)
        {
            var dialog = new PowerBIMMessageDialog(message, title, buttons, icon);

            if (owner != null)
                dialog.Owner = owner;
            else if (Application.Current.MainWindow != null)
                dialog.Owner = Application.Current.MainWindow;

            dialog.ShowDialog();
            return dialog.Result;
        }

        /// <summary>
        /// Shows a simple information message
        /// </summary>
        public static void ShowInfo(string message, string title = "Information", Window owner = null)
        {
            Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information, owner);
        }

        /// <summary>
        /// Shows a warning message
        /// </summary>
        public static void ShowWarning(string message, string title = "Warning", Window owner = null)
        {
            Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning, owner);
        }

        /// <summary>
        /// Shows an error message
        /// </summary>
        public static void ShowError(string message, string title = "Error", Window owner = null)
        {
            Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error, owner);
        }

        /// <summary>
        /// Shows a confirmation dialog
        /// </summary>
        public static bool ShowConfirmation(string message, string title = "Confirm", Window owner = null)
        {
            var result = Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question, owner);
            return result == MessageBoxResult.Yes;
        }

        #endregion
    }
}
