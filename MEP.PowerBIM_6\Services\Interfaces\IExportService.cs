using System;
using System.Collections.Generic;
using MEP.PowerBIM_6.Models;

namespace MEP.PowerBIM_6.Services.Interfaces
{
    /// <summary>
    /// Service interface for export operations
    /// Handles various export formats and data export functionality
    /// </summary>
    public interface IExportService
    {
        #region Export Operations

        /// <summary>
        /// Export data to Excel format (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="settings">Export settings</param>
        /// <param name="progressCallback">Progress callback for UI updates</param>
        /// <returns>True if successful</returns>
        bool ExportToExcel(ExportSettingsModel settings, IProgress<ExportProgress> progressCallback = null);

        /// <summary>
        /// Export data to CSV format (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="settings">Export settings</param>
        /// <param name="progressCallback">Progress callback for UI updates</param>
        /// <returns>True if successful</returns>
        bool ExportToCsv(ExportSettingsModel settings, IProgress<ExportProgress> progressCallback = null);

        /// <summary>
        /// Export circuit path images (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="circuits">Circuits to export images for</param>
        /// <param name="outputPath">Output directory path</param>
        /// <param name="progressCallback">Progress callback for UI updates</param>
        /// <returns>True if successful</returns>
        bool ExportCircuitPathImages(List<CircuitModel> circuits, string outputPath, IProgress<ExportProgress> progressCallback = null);

        /// <summary>
        /// Export circuit path images using export settings (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="settings">Export settings including output path and options</param>
        /// <param name="progressCallback">Progress callback for UI updates</param>
        /// <returns>True if successful</returns>
        bool ExportCircuitPathImages(ExportSettingsModel settings, IProgress<ExportProgress> progressCallback = null);

        /// <summary>
        /// Export distribution board summary report (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="distributionBoards">Distribution boards to include in report</param>
        /// <param name="outputPath">Output file path</param>
        /// <param name="format">Export format (Excel, PDF, etc.)</param>
        /// <returns>True if successful</returns>
        bool ExportDistributionBoardsSummary(List<DistributionBoardModel> distributionBoards, string outputPath, ExportFormat format);

        /// <summary>
        /// Export verification report (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="distributionBoards">Distribution boards to include in verification</param>
        /// <param name="outputPath">Output file path</param>
        /// <param name="includeDetails">Whether to include detailed circuit information</param>
        /// <returns>True if successful</returns>
        bool ExportVerificationReport(List<DistributionBoardModel> distributionBoards, string outputPath, bool includeDetails = true);

        #endregion

        #region Export Templates

        /// <summary>
        /// Get available export templates
        /// </summary>
        /// <returns>List of available templates</returns>
        List<ExportTemplate> GetAvailableTemplates();

        /// <summary>
        /// Load export template
        /// </summary>
        /// <param name="templateName">Name of the template</param>
        /// <returns>Export template or null if not found</returns>
        ExportTemplate LoadTemplate(string templateName);

        /// <summary>
        /// Save export template (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="template">Template to save</param>
        /// <returns>True if successful</returns>
        bool SaveTemplate(ExportTemplate template);

        /// <summary>
        /// Delete export template (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="templateName">Name of the template to delete</param>
        /// <returns>True if successful</returns>
        bool DeleteTemplate(string templateName);

        #endregion

        #region Export Validation

        /// <summary>
        /// Validate export settings
        /// </summary>
        /// <param name="settings">Settings to validate</param>
        /// <returns>Validation result</returns>
        ValidationResult ValidateExportSettings(ExportSettingsModel settings);

        /// <summary>
        /// Check if output path is valid and writable
        /// </summary>
        /// <param name="outputPath">Path to check</param>
        /// <returns>True if path is valid and writable</returns>
        bool IsOutputPathValid(string outputPath);

        /// <summary>
        /// Get estimated export file size (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="settings">Export settings</param>
        /// <returns>Estimated file size in bytes</returns>
        long GetEstimatedFileSize(ExportSettingsModel settings);

        #endregion

        #region Export Preview

        /// <summary>
        /// Generate export preview (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="settings">Export settings</param>
        /// <param name="maxRows">Maximum number of rows to preview</param>
        /// <returns>Preview data</returns>
        ExportPreview GeneratePreview(ExportSettingsModel settings, int maxRows = 100);

        #endregion
    }

    /// <summary>
    /// Export format enumeration
    /// </summary>
    public enum ExportFormat
    {
        Excel,
        Csv,
        Pdf,
        Json,
        Xml
    }

    /// <summary>
    /// Export progress information
    /// </summary>
    public class ExportProgress
    {
        public int CurrentStep { get; set; }
        public int TotalSteps { get; set; }
        public string CurrentOperation { get; set; }
        public double PercentComplete => TotalSteps > 0 ? (double)CurrentStep / TotalSteps * 100 : 0;
        public TimeSpan ElapsedTime { get; set; }
        public TimeSpan EstimatedTimeRemaining { get; set; }
        public bool IsCompleted { get; set; }
        public bool HasErrors { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Export template definition
    /// </summary>
    public class ExportTemplate
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public ExportFormat Format { get; set; }
        public List<string> IncludedColumns { get; set; } = new List<string>();
        public Dictionary<string, object> Settings { get; set; } = new Dictionary<string, object>();
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public string CreatedBy { get; set; }
    }

    /// <summary>
    /// Export preview data
    /// </summary>
    public class ExportPreview
    {
        public List<string> ColumnHeaders { get; set; } = new List<string>();
        public List<List<object>> PreviewRows { get; set; } = new List<List<object>>();
        public int TotalRowCount { get; set; }
        public long EstimatedFileSize { get; set; }
        public string FormatDescription { get; set; }
    }
}
