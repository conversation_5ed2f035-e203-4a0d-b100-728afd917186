# MEP.PowerBIM_6 Testing Plan

## Overview

This testing plan covers comprehensive unit testing and regression testing for the MEP.PowerBIM_6 WPF application, designed to work within Revit API constraints and leverage the dependency injection architecture.

## Testing Strategy

### 1. Unit Testing Approach
- **Framework**: xUnit with <PERSON><PERSON> for mocking
- **Coverage Target**: 80%+ code coverage for business logic
- **Focus Areas**: Services, ViewModels, Data transformations
- **Constraints**: No Revit API dependencies in unit tests

### 2. Regression Testing Approach
- **Integration Tests**: Test complete workflows
- **Revit Integration**: Test with actual Revit documents
- **UI Automation**: Limited WPF UI testing
- **Data Validation**: Ensure calculations remain accurate

## Test Project Structure

```
MEP.PowerBIM_6.Tests/
├── Unit/
│   ├── Services/
│   ├── ViewModels/
│   ├── Models/
│   └── Converters/
├── Integration/
│   ├── DependencyInjection/
│   ├── Navigation/
│   └── DataFlow/
├── Regression/
│   ├── Calculations/
│   ├── Export/
│   └── Workflows/
├── TestData/
│   ├── SampleProjects/
│   ├── TestDocuments/
│   └── ExpectedResults/
└── Helpers/
    ├── MockServices/
    ├── TestFixtures/
    └── Utilities/
```

## Unit Testing Plan

### 1. Service Layer Testing

#### DataService Tests
```csharp
[Trait("Category", "Unit")]
[Trait("Category", "Services")]
public class DataServiceTests : IDisposable
{
    private readonly Mock<BecaActivityLoggerData> _mockLogger;
    private readonly DataService _dataService;

    public DataServiceTests()
    {
        _mockLogger = new Mock<BecaActivityLoggerData>();
        _dataService = new DataService(_mockLogger.Object);
    }

    public void Dispose()
    {
        // Cleanup handled automatically by xUnit
    }

    [Fact]
    public void ConvertToModel_ValidProjectInfo_ReturnsProjectInfoModel()
    {
        // Arrange
        var projectInfo = CreateTestProjectInfo();

        // Act
        var result = _dataService.ConvertToModel(projectInfo);

        // Assert
        result.Should().NotBeNull();
        result.JobName.Should().Be(projectInfo.JobName);
    }

    [Fact]
    public void ValidateProjectInfo_EmptyJobName_ReturnsValidationError()
    {
        // Arrange
        var projectInfo = new ProjectInfoModel { JobName = "" };

        // Act
        var result = _dataService.ValidateProjectInfo(projectInfo);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains("Job name"));
    }
}
```

#### NavigationService Tests
```csharp
[Trait("Category", "Unit")]
[Trait("Category", "Services")]
public class NavigationServiceTests : IDisposable
{
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<BecaActivityLoggerData> _mockLogger;
    private readonly NavigationService _navigationService;
    private readonly Mock<Frame> _mockFrame;

    public NavigationServiceTests()
    {
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockLogger = new Mock<BecaActivityLoggerData>();
        _mockFrame = new Mock<Frame>();

        _navigationService = new NavigationService(_mockServiceProvider.Object, _mockLogger.Object);
        _navigationService.Initialize(_mockFrame.Object);
    }

    public void Dispose()
    {
        _navigationService?.Dispose();
    }

    [Fact]
    public void NavigateTo_ValidPageKey_ReturnsTrue()
    {
        // Arrange
        var pageKey = PageKeys.Home;

        // Act
        var result = _navigationService.NavigateTo(pageKey);

        // Assert
        result.Should().BeTrue();
        _navigationService.CurrentPageKey.Should().Be(pageKey);
    }
}
```

### 2. ViewModel Testing

#### BaseViewModel Tests
```csharp
[Trait("Category", "Unit")]
[Trait("Category", "ViewModels")]
public class BaseViewModelTests : IDisposable
{
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly TestViewModel _viewModel;

    public BaseViewModelTests()
    {
        _mockServiceProvider = new Mock<IServiceProvider>();
        _viewModel = new TestViewModel(_mockServiceProvider.Object);
    }

    public void Dispose()
    {
        _viewModel?.Dispose();
    }

    [Fact]
    public void SetBusyState_True_UpdatesIsBusyProperty()
    {
        // Act
        _viewModel.TestSetBusyState(true, "Processing...");

        // Assert
        _viewModel.IsBusy.Should().BeTrue();
        _viewModel.StatusMessage.Should().Be("Processing...");
    }
}

// Test implementation of BaseViewModel
public class TestViewModel : BaseViewModel
{
    public TestViewModel(IServiceProvider serviceProvider) : base(serviceProvider) { }

    public void TestSetBusyState(bool isBusy, string message) => SetBusyState(isBusy, message);
}
```

#### MainViewModel Tests
```csharp
[Trait("Category", "Unit")]
[Trait("Category", "ViewModels")]
public class MainViewModelTests : IDisposable
{
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<IDataService> _mockDataService;
    private readonly MainViewModel _mainViewModel;

    public MainViewModelTests()
    {
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockDataService = new Mock<IDataService>();

        _mockServiceProvider.Setup(sp => sp.GetService(typeof(IDataService)))
                          .Returns(_mockDataService.Object);

        _mainViewModel = new MainViewModel(_mockServiceProvider.Object);
    }

    public void Dispose()
    {
        _mainViewModel?.Dispose();
    }

    [Fact]
    public void InitializeWithData_ValidData_PopulatesDistributionBoards()
    {
        // Arrange
        var testDBs = CreateTestDistributionBoards();
        var testProjectInfo = CreateTestProjectInfo();

        // Act
        _mainViewModel.InitializeWithData(testDBs, testProjectInfo, null, null);

        // Assert
        _mainViewModel.DistributionBoards.Count.Should().BeGreaterThan(0);
    }
}
```

### 3. Model Testing

#### Data Model Tests
```csharp
[Trait("Category", "Unit")]
[Trait("Category", "Models")]
public class DistributionBoardModelTests
{
    [Fact]
    public void Constructor_ValidPowerBIMData_InitializesCorrectly()
    {
        // Arrange
        var dbData = CreateTestPowerBIMDBData();

        // Act
        var model = new DistributionBoardModel(dbData);

        // Assert
        model.Name.Should().Be(dbData.Schedule_DB_Name);
        model.CircuitCount.Should().Be(dbData.CCTs.Count);
    }
}
```

### 4. Converter Testing

```csharp
[Trait("Category", "Unit")]
[Trait("Category", "Converters")]
public class StatusColorConverterTests : IDisposable
{
    private readonly StatusColorConverter _converter;

    public StatusColorConverterTests()
    {
        _converter = new StatusColorConverter();
    }

    public void Dispose()
    {
        // Cleanup if needed
    }

    [Fact]
    public void Convert_PassStatus_ReturnsGreenBrush()
    {
        // Act
        var result = _converter.Convert("Pass", typeof(Brush), null, null);

        // Assert
        result.Should().BeOfType<SolidColorBrush>();
        // Additional color validation
    }
}
```

## Integration Testing Plan

### 1. Dependency Injection Tests

```csharp
[Trait("Category", "Integration")]
[Trait("Category", "DependencyInjection")]
public class DependencyInjectionTests : IDisposable
{
    private IServiceProvider _serviceProvider;

    public void Dispose()
    {
        (_serviceProvider as IDisposable)?.Dispose();
    }

    [Fact]
    public void ServiceConfiguration_RegistersAllServices()
    {
        // Arrange
        var services = new ServiceCollection();
        var mockUIDocument = new Mock<UIDocument>();
        var mockLogger = new Mock<BecaActivityLoggerData>();

        // Act
        services.ConfigureServices(mockUIDocument.Object, mockLogger.Object);
        _serviceProvider = services.BuildServiceProvider();

        // Assert
        _serviceProvider.GetService<INavigationService>().Should().NotBeNull();
        _serviceProvider.GetService<IDataService>().Should().NotBeNull();
        _serviceProvider.GetService<MainViewModel>().Should().NotBeNull();
    }

    [Fact]
    public void ServiceProvider_SingletonServices_ReturnSameInstance()
    {
        // Test singleton lifetime behavior
    }
}
```

### 2. Navigation Integration Tests

```csharp
[Trait("Category", "Integration")]
[Trait("Category", "Navigation")]
public class NavigationIntegrationTests
{
    [Fact]
    public void NavigationFlow_CompleteWorkflow_WorksCorrectly()
    {
        // Test complete navigation scenarios
        // Home -> Distribution Boards -> Circuit Edit -> Back
    }
}
```

## Regression Testing Plan

### 1. Calculation Accuracy Tests

```csharp
[Trait("Category", "Regression")]
[Trait("Category", "Calculations")]
public class CalculationRegressionTests
{
    [Theory]
    [InlineData("TestProject1.rvt", "ExpectedResults1.json")]
    [InlineData("TestProject2.rvt", "ExpectedResults2.json")]
    public void ElectricalCalculations_KnownProjects_MatchExpectedResults(string projectFile, string expectedFile)
    {
        // Arrange
        var testProject = LoadTestProject(projectFile);
        var expectedResults = LoadExpectedResults(expectedFile);

        // Act
        var calculationService = GetCalculationService();
        var results = calculationService.RunAllCalculations(testProject);

        // Assert
        AssertCalculationResults(results, expectedResults);
    }
}
```

### 2. Data Transformation Regression Tests

```csharp
[Trait("Category", "Regression")]
[Trait("Category", "DataTransformation")]
public class DataTransformationRegressionTests
{
    [Fact]
    public void PowerBIMDataConversion_LegacyProjects_PreservesAllData()
    {
        // Test that conversion from legacy PowerBIM data doesn't lose information
    }
}
```

### 3. Export/Import Regression Tests

```csharp
[TestClass]
public class ExportImportRegressionTests
{
    [TestMethod]
    public void ExportImportRoundTrip_PreservesData()
    {
        // Test export -> import maintains data integrity
    }
}
```

## Test Data Management

### 1. Test Project Files
- **Small Projects**: 1-5 distribution boards for unit tests
- **Medium Projects**: 10-20 distribution boards for integration tests
- **Large Projects**: 50+ distribution boards for performance tests
- **Edge Cases**: Projects with unusual configurations

### 2. Expected Results
- **JSON Files**: Store expected calculation results
- **CSV Files**: Expected export outputs
- **Screenshots**: UI regression testing baselines

### 3. Mock Data Factories

```csharp
public static class TestDataFactory
{
    public static PowerBIM_ProjectInfo CreateTestProjectInfo()
    {
        return new PowerBIM_ProjectInfo
        {
            JobName = "Test Project",
            Engineer = "Test Engineer",
            SystemVoltageDropMax = 5.0,
            AmbientTemperature = 25.0
        };
    }

    public static List<PowerBIM_DBData> CreateTestDistributionBoards(int count = 3)
    {
        var dbs = new List<PowerBIM_DBData>();
        for (int i = 1; i <= count; i++)
        {
            dbs.Add(new PowerBIM_DBData
            {
                Schedule_DB_Name = $"DB-{i}",
                CCTs = CreateTestCircuits(5)
            });
        }
        return dbs;
    }
}
```

## Test Execution Strategy

### 1. Continuous Integration
- **Build Pipeline**: Run unit tests on every commit
- **Nightly Builds**: Run full regression suite
- **Release Testing**: Complete test suite before releases

### 2. Test Categories
```csharp
[TestCategory("Unit")]
[TestCategory("Integration")]
[TestCategory("Regression")]
[TestCategory("Performance")]
[TestCategory("UI")]
```

### 3. Test Environments
- **Development**: Local developer machines
- **CI/CD**: Automated build servers
- **Staging**: Pre-production environment with Revit
- **Production**: Final validation environment

## Performance Testing

### 1. Load Testing
```csharp
[TestMethod]
public void LargeProject_LoadTime_WithinAcceptableLimits()
{
    // Test loading projects with 100+ distribution boards
    var stopwatch = Stopwatch.StartNew();
    
    // Load large project
    
    stopwatch.Stop();
    Assert.IsTrue(stopwatch.ElapsedMilliseconds < 5000); // 5 second limit
}
```

### 2. Memory Testing
- Monitor memory usage during long operations
- Test for memory leaks in service resolution
- Validate proper disposal of resources

## Test Maintenance

### 1. Test Review Process
- Regular review of test coverage
- Update tests when requirements change
- Remove obsolete tests

### 2. Test Documentation
- Document test scenarios and expected outcomes
- Maintain test data documentation
- Keep regression test baselines updated

## Test Execution Commands

### PowerShell Test Runner
```powershell
# Run all tests
.\RunTests.ps1 -TestCategory All -Coverage

# Run only unit tests
.\RunTests.ps1 -TestCategory Unit -Verbose

# Run regression tests with coverage
.\RunTests.ps1 -TestCategory Regression -Coverage -Parallel

# Run smoke tests (Unit + Integration)
.\RunTests.ps1 -TestCategory Smoke
```

### Manual dotnet CLI Commands
```bash
# Unit tests only
dotnet test --filter "Category=Unit" --logger trx --logger html

# Integration tests with coverage
dotnet test --filter "Category=Integration" --collect:"XPlat Code Coverage"

# Regression tests
dotnet test --filter "Category=Regression" --settings runsettings.xml

# All tests with detailed output
dotnet test --verbosity detailed --results-directory ./TestResults
```

## Continuous Integration Setup

### Azure DevOps Pipeline
```yaml
trigger:
- main
- develop

pool:
  vmImage: 'windows-latest'

variables:
  buildConfiguration: 'Release'
  testConfiguration: 'Debug'

steps:
- task: DotNetCoreCLI@2
  displayName: 'Restore packages'
  inputs:
    command: 'restore'
    projects: '**/*.csproj'

- task: DotNetCoreCLI@2
  displayName: 'Build solution'
  inputs:
    command: 'build'
    projects: '**/*.csproj'
    arguments: '--configuration $(buildConfiguration)'

- task: DotNetCoreCLI@2
  displayName: 'Run unit tests'
  inputs:
    command: 'test'
    projects: '**/MEP.PowerBIM_6.Tests.csproj'
    arguments: '--configuration $(testConfiguration) --filter "Category=Unit" --collect:"XPlat Code Coverage" --logger trx'

- task: DotNetCoreCLI@2
  displayName: 'Run integration tests'
  inputs:
    command: 'test'
    projects: '**/MEP.PowerBIM_6.Tests.csproj'
    arguments: '--configuration $(testConfiguration) --filter "Category=Integration" --logger trx'

- task: PublishTestResults@2
  displayName: 'Publish test results'
  inputs:
    testResultsFormat: 'VSTest'
    testResultsFiles: '**/*.trx'
    mergeTestResults: true

- task: PublishCodeCoverageResults@1
  displayName: 'Publish code coverage'
  inputs:
    codeCoverageTool: 'Cobertura'
    summaryFileLocation: '**/coverage.cobertura.xml'
```

### GitHub Actions Workflow
```yaml
name: MEP.PowerBIM_6 Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '4.8.x'

    - name: Restore dependencies
      run: dotnet restore

    - name: Build
      run: dotnet build --no-restore --configuration Release

    - name: Run Unit Tests
      run: dotnet test --no-build --configuration Release --filter "Category=Unit" --collect:"XPlat Code Coverage" --logger trx

    - name: Run Integration Tests
      run: dotnet test --no-build --configuration Release --filter "Category=Integration" --logger trx

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: TestResults/
```

## Test Data Management Strategy

### Test Data Organization
```
TestData/
├── SampleProjects/
│   ├── SmallProject.json          # 3 DBs, 15 circuits
│   ├── MediumProject.json         # 10 DBs, 50 circuits
│   └── LargeProject.json          # 50 DBs, 1000 circuits
├── ExpectedResults/
│   ├── SmallProject_DBCalculations.json
│   ├── VoltageDropScenarios.json
│   └── EFLICalculations.json
├── TestDocuments/
│   ├── TestProject_Simple.rvt
│   ├── TestProject_Complex.rvt
│   └── EdgeCase_EmptyProject.rvt
└── Baselines/
    ├── UI_Screenshots/
    └── Performance_Benchmarks/
```

### Test Data Versioning
- Use Git LFS for large test files (.rvt files)
- Version control expected results JSON files
- Automated baseline updates for approved changes
- Test data validation before test execution

## Performance Testing Framework

### Performance Test Categories
1. **Load Tests**: Large projects (1000+ circuits)
2. **Stress Tests**: Memory and CPU intensive operations
3. **Endurance Tests**: Long-running calculations
4. **Scalability Tests**: Varying project sizes

### Performance Metrics
- **Calculation Speed**: Circuits per second
- **Memory Usage**: Peak and average memory consumption
- **UI Responsiveness**: Response time for user interactions
- **Startup Time**: Application initialization duration

### Performance Regression Detection
```csharp
[TestMethod]
[TestCategory("Performance")]
public void CalculationPerformance_RegressionTest()
{
    var baseline = LoadPerformanceBaseline("CalculationSpeed");
    var currentPerformance = MeasureCalculationPerformance();

    // Allow 10% performance degradation
    currentPerformance.Should().BeGreaterThan(baseline * 0.9);
}
```

## Test Reporting and Analytics

### Test Result Dashboard
- **Pass/Fail Trends**: Track test stability over time
- **Coverage Reports**: Code coverage by module
- **Performance Trends**: Performance metrics over builds
- **Flaky Test Detection**: Identify unstable tests

### Automated Reporting
- Daily test summary emails
- Slack/Teams notifications for failures
- Automated bug creation for consistent failures
- Performance regression alerts

## Best Practices Implementation

### Test Naming Convention
```csharp
[TestMethod]
public void MethodName_Scenario_ExpectedBehavior()
{
    // Example: CalculateVoltDrop_HighLoad_ReturnsAccurateResult
}
```

### Test Organization
- One test class per production class
- Logical grouping with nested test classes
- Shared setup in base test classes
- Clear test categories and attributes

### Mock Strategy
- Interface-based mocking for all dependencies
- Realistic mock data using TestDataFactory
- Behavior verification for critical interactions
- State-based testing for data transformations

This comprehensive testing plan ensures both the quality and reliability of the MEP.PowerBIM_6 application while maintaining compatibility with Revit API constraints and providing robust regression testing capabilities.
