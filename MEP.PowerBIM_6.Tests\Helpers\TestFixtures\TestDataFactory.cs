using System;
using System.Collections.Generic;
using MEP.PowerBIM_6.Models;

namespace MEP.PowerBIM_6.Tests.Helpers.TestFixtures
{
    /// <summary>
    /// Factory class for creating test data objects
    /// Provides consistent test data across all test classes
    /// </summary>
    public static class TestDataFactory
    {
        #region PowerBIM Core Data

        /// <summary>
        /// Create a test PowerBIM_ProjectInfo with default values
        /// </summary>
        public static PowerBIM_ProjectInfo CreateTestProjectInfo(string jobName = "Test Project")
        {
            return new PowerBIM_ProjectInfo
            {
                JobName = jobName,
                Engineer = "Test Engineer",
                ProjectNumber = "TEST-001",
                ProjectAddress = "123 Test Street, Test City",
                ClientName = "Test Client",
                ProjectStatus = "In Progress",
                SystemVoltageDropMax = 5.0,
                AmbientTemperature = 25.0,
                ConductorTemperature = 70.0,
                InstallationMethod = "Method C",
                CableDerating = 1.0,
                GroupingFactor = 1.0,
                ThermalInsulationFactor = 1.0,
                // Add other required properties with test values
            };
        }

        /// <summary>
        /// Create a test PowerBIM_DBData (Distribution Board)
        /// </summary>
        public static PowerBIM_DBData CreateTestDistributionBoard(string name = "Test DB", int circuitCount = 5)
        {
            var db = new PowerBIM_DBData
            {
                Schedule_DB_Name = name,
                DB_SupplyVoltage = 415.0,
                DB_Phases = 3,
                DB_MainBreakerRating = 100.0,
                DB_Location = "Test Location",
                DB_Panel = "Test Panel",
                IsManuallyLocked = false,
                CCTs = CreateTestCircuits(circuitCount)
            };

            return db;
        }

        /// <summary>
        /// Create a list of test circuits
        /// </summary>
        public static List<PowerBIM_CircuitData> CreateTestCircuits(int count = 5)
        {
            var circuits = new List<PowerBIM_CircuitData>();
            
            for (int i = 1; i <= count; i++)
            {
                circuits.Add(new PowerBIM_CircuitData
                {
                    CCT_Number = i.ToString(),
                    CCT_Description = $"Test Circuit {i}",
                    CCT_Load = 1000.0 + (i * 100), // Varying loads
                    CCT_Voltage = 230.0,
                    CCT_Phases = i % 2 == 0 ? 3 : 1, // Mix of single and three phase
                    CCT_PowerFactor = 0.85,
                    CCT_Length = 50.0 + (i * 10), // Varying lengths
                    CCT_CableType = "XLPE/SWA/PVC",
                    CCT_CableSize = "2.5mm²",
                    CCT_BreakerRating = 16.0,
                    CCT_BreakerType = "MCB",
                    // Add other required circuit properties
                });
            }

            return circuits;
        }

        /// <summary>
        /// Create a list of test distribution boards
        /// </summary>
        public static List<PowerBIM_DBData> CreateTestDistributionBoards(int count = 3)
        {
            var dbs = new List<PowerBIM_DBData>();
            
            for (int i = 1; i <= count; i++)
            {
                dbs.Add(CreateTestDistributionBoard($"DB-{i}", 5 + i)); // Varying circuit counts
            }

            return dbs;
        }

        #endregion

        #region WPF Models

        /// <summary>
        /// Create a test ProjectInfoModel
        /// </summary>
        public static ProjectInfoModel CreateTestProjectInfoModel(string jobName = "Test Project")
        {
            var coreProjectInfo = CreateTestProjectInfo(jobName);
            return new ProjectInfoModel(coreProjectInfo);
        }

        /// <summary>
        /// Create a test DistributionBoardModel
        /// </summary>
        public static DistributionBoardModel CreateTestDistributionBoardModel(string name = "Test DB")
        {
            var coreDB = CreateTestDistributionBoard(name);
            return new DistributionBoardModel(coreDB);
        }

        /// <summary>
        /// Create test export settings
        /// </summary>
        public static ExportSettingsModel CreateTestExportSettings()
        {
            return new ExportSettingsModel
            {
                OutputPath = @"C:\Temp\TestExport.xlsx",
                ExportFormat = "Excel",
                IncludeCircuits = true,
                IncludeDistributionBoards = true,
                IncludeCalculations = true,
                IncludeValidation = false,
                ExportTemplate = "Standard"
            };
        }

        /// <summary>
        /// Create test settings model
        /// </summary>
        public static SettingsModel CreateTestSettings()
        {
            return new SettingsModel
            {
                DefaultVoltageDropLimit = 5.0,
                DefaultAmbientTemperature = 25.0,
                DefaultConductorTemperature = 70.0,
                AutoSaveEnabled = true,
                AutoSaveInterval = 300, // 5 minutes
                ShowAdvancedFeatures = false,
                EnableLogging = true,
                LogLevel = "Information"
            };
        }

        #endregion

        #region Edge Cases and Special Scenarios

        /// <summary>
        /// Create a distribution board with no circuits (edge case)
        /// </summary>
        public static PowerBIM_DBData CreateEmptyDistributionBoard()
        {
            return new PowerBIM_DBData
            {
                Schedule_DB_Name = "Empty DB",
                CCTs = new List<PowerBIM_CircuitData>()
            };
        }

        /// <summary>
        /// Create a distribution board with locked status
        /// </summary>
        public static PowerBIM_DBData CreateLockedDistributionBoard()
        {
            var db = CreateTestDistributionBoard("Locked DB");
            db.IsManuallyLocked = true;
            return db;
        }

        /// <summary>
        /// Create a circuit with validation errors (for testing validation)
        /// </summary>
        public static PowerBIM_CircuitData CreateInvalidCircuit()
        {
            return new PowerBIM_CircuitData
            {
                CCT_Number = "", // Invalid: empty circuit number
                CCT_Description = "Invalid Circuit",
                CCT_Load = -100.0, // Invalid: negative load
                CCT_Voltage = 0.0, // Invalid: zero voltage
                CCT_Length = -50.0, // Invalid: negative length
                CCT_CableSize = "", // Invalid: empty cable size
                CCT_BreakerRating = 0.0 // Invalid: zero breaker rating
            };
        }

        /// <summary>
        /// Create a large project for performance testing
        /// </summary>
        public static List<PowerBIM_DBData> CreateLargeProject(int dbCount = 50, int circuitsPerDB = 20)
        {
            var dbs = new List<PowerBIM_DBData>();
            
            for (int i = 1; i <= dbCount; i++)
            {
                dbs.Add(CreateTestDistributionBoard($"DB-{i:D3}", circuitsPerDB));
            }

            return dbs;
        }

        #endregion

        #region Validation Test Data

        /// <summary>
        /// Create project info with validation errors
        /// </summary>
        public static ProjectInfoModel CreateInvalidProjectInfo()
        {
            return new ProjectInfoModel(new PowerBIM_ProjectInfo())
            {
                JobName = "", // Invalid: empty job name
                SystemVoltageDropMax = -5.0, // Invalid: negative voltage drop
                AmbientTemperature = 200.0 // Invalid: extreme temperature
            };
        }

        /// <summary>
        /// Create export settings with validation errors
        /// </summary>
        public static ExportSettingsModel CreateInvalidExportSettings()
        {
            return new ExportSettingsModel
            {
                OutputPath = "", // Invalid: empty path
                ExportFormat = "InvalidFormat", // Invalid: unsupported format
                IncludeCircuits = false,
                IncludeDistributionBoards = false // Invalid: nothing to export
            };
        }

        #endregion

        #region Mock Data for Services

        /// <summary>
        /// Create expected calculation results for regression testing
        /// </summary>
        public static Dictionary<string, object> CreateExpectedCalculationResults()
        {
            return new Dictionary<string, object>
            {
                ["TotalLoad"] = 15000.0,
                ["DiversifiedLoad"] = 12000.0,
                ["MaxVoltageDropPercentage"] = 3.2,
                ["CircuitsPassingValidation"] = 4,
                ["CircuitsFailingValidation"] = 1,
                ["AverageEFLI"] = 0.85
            };
        }

        /// <summary>
        /// Create test import settings
        /// </summary>
        public static ImportSettings CreateTestImportSettings()
        {
            return new ImportSettings
            {
                HasHeaders = true,
                Delimiter = ",",
                OverwriteExisting = false,
                ColumnMapping = new Dictionary<string, int>
                {
                    ["CircuitNumber"] = 0,
                    ["Description"] = 1,
                    ["Load"] = 2,
                    ["CableSize"] = 3
                }
            };
        }

        #endregion
    }
}
