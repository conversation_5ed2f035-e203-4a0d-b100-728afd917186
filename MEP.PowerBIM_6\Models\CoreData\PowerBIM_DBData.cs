using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Independent PowerBIM Distribution Board Data class for MEP.PowerBIM_6
    /// Contains all distribution board information and circuits
    /// </summary>
    public class PowerBIM_DBData
    {
        #region Properties

        /// <summary>
        /// Project information reference
        /// </summary>
        public PowerBIM_ProjectInfo Project_Info { get; set; }

        /// <summary>
        /// Revit distribution board element
        /// </summary>
        public Element DB_Element { get; set; }

        /// <summary>
        /// Electrical system associated with this distribution board
        /// </summary>
        public ElectricalSystem DB_ElectricalSystem { get; set; }

        /// <summary>
        /// List of circuits in this distribution board
        /// </summary>
        public List<PowerBIM_CircuitData> CCTs { get; set; }

        /// <summary>
        /// Distribution board name from schedule
        /// </summary>
        public string Schedule_DB_Name { get; set; }

        /// <summary>
        /// Distribution board description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Distribution board location
        /// </summary>
        public XYZ DB_Location { get; set; }

        /// <summary>
        /// Indicates if the distribution board is manually locked
        /// </summary>
        public bool IsManuallyLocked { get; set; }

        /// <summary>
        /// Number of circuits that pass validation
        /// </summary>
        public int PassCount { get; set; }

        /// <summary>
        /// Number of circuits with warnings
        /// </summary>
        public int WarningCount { get; set; }

        /// <summary>
        /// Number of circuits that fail validation
        /// </summary>
        public int FailCount { get; set; }

        /// <summary>
        /// Overall distribution board check result
        /// </summary>
        public string DB_Check_Result { get; set; }

        /// <summary>
        /// Distribution board warnings
        /// </summary>
        public string DB_Check_Warnings { get; set; }

        /// <summary>
        /// Distribution board result summary
        /// </summary>
        public string DB_Result_Summary { get; set; }

        /// <summary>
        /// Indicates if all circuits pass validation
        /// </summary>
        public bool DB_All_Circuits_Pass { get; set; }

        /// <summary>
        /// User notes for this distribution board
        /// </summary>
        public string User_Notes { get; set; }

        /// <summary>
        /// GUI notes for display
        /// </summary>
        public string GUI_Notes { get; set; }

        /// <summary>
        /// Indicates if update is required
        /// </summary>
        public bool Update_Required { get; set; }

        /// <summary>
        /// Indicates if data is valid
        /// </summary>
        public bool Data_Good { get; set; }

        /// <summary>
        /// Indicates if required parameters are missing
        /// </summary>
        public bool Parameters_Missing { get; set; }

        /// <summary>
        /// Indicates if there are unsaved changes
        /// </summary>
        public bool HasUnsavedChanges { get; set; }

        /// <summary>
        /// Diversified total phase current R
        /// </summary>
        public double Diversified_Total_Phase_Current_R { get; set; }

        /// <summary>
        /// Diversified total phase current W
        /// </summary>
        public double Diversified_Total_Phase_Current_W { get; set; }

        /// <summary>
        /// Diversified total phase current B
        /// </summary>
        public double Diversified_Total_Phase_Current_B { get; set; }

        /// <summary>
        /// List of locked circuits
        /// </summary>
        public List<PowerBIM_CircuitData> LockedCircuits { get; set; }

        /// <summary>
        /// Feeder cable information
        /// </summary>
        public string Schedule_Feeder_Cable { get; set; }

        /// <summary>
        /// Device fault rating
        /// </summary>
        public double Device_kA_Rating { get; set; }

        /// <summary>
        /// Prospective short circuit current
        /// </summary>
        public double PSCC { get; set; }

        /// <summary>
        /// Earth Fault Loop Impedance - Resistance component
        /// Based on legacy EFLI_R property
        /// </summary>
        public double EFLI_R { get; set; }

        /// <summary>
        /// Earth Fault Loop Impedance - Reactance component
        /// Based on legacy EFLI_X property
        /// </summary>
        public double EFLI_X { get; set; }

        /// <summary>
        /// Distribution Board Voltage Drop
        /// Based on legacy DBVD property
        /// </summary>
        public double DBVD { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor
        /// </summary>
        public PowerBIM_DBData()
        {
            InitializeDefaults();
        }

        /// <summary>
        /// Constructor with project info and distribution board element
        /// </summary>
        /// <param name="projectInfo">Project information</param>
        /// <param name="dbElement">Distribution board element</param>
        public PowerBIM_DBData(PowerBIM_ProjectInfo projectInfo, Element dbElement)
        {
            Project_Info = projectInfo;
            DB_Element = dbElement;
            
            InitializeDefaults();
            LoadDistributionBoardData();
        }

        #endregion

        #region Methods

        /// <summary>
        /// Initialize default values
        /// </summary>
        private void InitializeDefaults()
        {
            CCTs = new List<PowerBIM_CircuitData>();
            LockedCircuits = new List<PowerBIM_CircuitData>();
            
            Schedule_DB_Name = "Unknown Distribution Board";
            Description = string.Empty;
            DB_Check_Result = "OK";
            DB_Check_Warnings = "None";
            DB_Result_Summary = string.Empty;
            User_Notes = string.Empty;
            GUI_Notes = string.Empty;
            Schedule_Feeder_Cable = string.Empty;
            
            PassCount = 0;
            WarningCount = 0;
            FailCount = 0;
            Device_kA_Rating = 0;
            PSCC = 0;
            EFLI_R = 0.0;
            EFLI_X = 0.0;
            DBVD = 0.0;

            IsManuallyLocked = false;
            DB_All_Circuits_Pass = false;
            Update_Required = false;
            Data_Good = false;
            Parameters_Missing = false;
        }

        /// <summary>
        /// Load distribution board data from Revit element
        /// </summary>
        private void LoadDistributionBoardData()
        {
            if (DB_Element == null) return;

            try
            {
                // Get basic information
                Schedule_DB_Name = DB_Element.Name ?? "Unknown Distribution Board";
                
                // Get location
                if (DB_Element.Location is LocationPoint locationPoint)
                {
                    DB_Location = locationPoint.Point;
                }

                // Get electrical system if available
                if (DB_Element is FamilyInstance familyInstance)
                {
                    var electricalSystems = familyInstance.MEPModel?.GetElectricalSystems();
                    DB_ElectricalSystem = electricalSystems?.FirstOrDefault();
                }

                // Load parameters
                LoadDistributionBoardParameters();

                Data_Good = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading distribution board data: {ex.Message}");
                Data_Good = false;
            }
        }

        /// <summary>
        /// Load distribution board parameters
        /// </summary>
        private void LoadDistributionBoardParameters()
        {
            try
            {
                // Load standard parameters
                Description = GetParameterValue("Comments") ?? string.Empty;
                User_Notes = GetParameterValue("Beca Inst Use") ?? string.Empty;
                Schedule_Feeder_Cable = GetParameterValue("Feeder Cable") ?? string.Empty;
                
                // Load electrical parameters
                if (double.TryParse(GetParameterValue("Device Fault Rating"), out double faultRating))
                {
                    Device_kA_Rating = faultRating;
                }
                
                if (double.TryParse(GetParameterValue("PSCC"), out double pscc))
                {
                    PSCC = pscc;
                }

                // Check if manually locked
                string manualLock = GetParameterValue("Beca_Circuit_Length_Manual");
                IsManuallyLocked = manualLock == "1" || manualLock?.ToLower() == "true";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading DB parameters: {ex.Message}");
            }
        }

        /// <summary>
        /// Get parameter value from distribution board element
        /// </summary>
        /// <param name="parameterName">Parameter name</param>
        /// <returns>Parameter value as string</returns>
        private string GetParameterValue(string parameterName)
        {
            try
            {
                Parameter param = DB_Element?.LookupParameter(parameterName);
                if (param != null && param.HasValue)
                {
                    switch (param.StorageType)
                    {
                        case StorageType.String:
                            return param.AsString();
                        case StorageType.Integer:
                            return param.AsInteger().ToString();
                        case StorageType.Double:
                            return param.AsDouble().ToString();
                        default:
                            return param.AsValueString();
                    }
                }
            }
            catch
            {
                // Ignore parameter read errors
            }
            
            return null;
        }

        /// <summary>
        /// Initialize all circuits for this distribution board
        /// </summary>
        public void Initialise_AllCircuits()
        {
            if (Project_Info?.Document == null || string.IsNullOrEmpty(Schedule_DB_Name))
                return;

            try
            {
                CCTs.Clear();

                // Get all electrical systems connected to this panel
                var electricalSystems = GetElectricalSystemsForPanel();

                foreach (var electricalSystem in electricalSystems)
                {
                    // Create circuit data for each electrical system
                    var circuitData = new PowerBIM_CircuitData(Project_Info, this, electricalSystem);
                    CCTs.Add(circuitData);
                }

                // Update locked circuits list
                LockedCircuits = CCTs.Where(x => x.IsLocked).ToList();

                // Calculate diversified loads
                CalculateDiversifiedCircuitLoad();

                // Check pass/fail counts
                Check_PassFailWarningCount();

                Data_Good = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing circuits: {ex.Message}");
                Data_Good = false;
            }
        }

        /// <summary>
        /// Get electrical systems for this panel
        /// </summary>
        /// <returns>List of electrical systems</returns>
        private List<ElectricalSystem> GetElectricalSystemsForPanel()
        {
            var systems = new List<ElectricalSystem>();
            
            try
            {
                // Filter electrical systems by panel name
                var collector = new FilteredElementCollector(Project_Info.Document)
                    .OfClass(typeof(ElectricalSystem))
                    .Cast<ElectricalSystem>()
                    .Where(sys => GetPanelName(sys) == Schedule_DB_Name);

                systems.AddRange(collector);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting electrical systems: {ex.Message}");
            }

            return systems;
        }

        /// <summary>
        /// Get panel name from electrical system
        /// </summary>
        /// <param name="system">Electrical system</param>
        /// <returns>Panel name</returns>
        private string GetPanelName(ElectricalSystem system)
        {
            try
            {
                Parameter panelParam = system.get_Parameter(BuiltInParameter.RBS_ELEC_CIRCUIT_PANEL_PARAM);
                return panelParam?.AsString() ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Calculate diversified circuit load
        /// </summary>
        private void CalculateDiversifiedCircuitLoad()
        {
            // Reset totals
            Diversified_Total_Phase_Current_R = 0;
            Diversified_Total_Phase_Current_W = 0;
            Diversified_Total_Phase_Current_B = 0;

            // Sum up diversified currents from all circuits
            foreach (var circuit in CCTs)
            {
                if (!circuit.IsSpareOrSpace)
                {
                    // Apply diversity factor to circuit current
                    double diversifiedCurrent = circuit.GetCurrent() * circuit.Diversity;
                    
                    // Distribute across phases based on circuit type
                    if (circuit.Number_Of_Poles == 1)
                    {
                        // Single phase - distribute to one phase
                        Diversified_Total_Phase_Current_R += diversifiedCurrent;
                    }
                    else if (circuit.Number_Of_Poles == 3)
                    {
                        // Three phase - distribute equally
                        Diversified_Total_Phase_Current_R += diversifiedCurrent / 3;
                        Diversified_Total_Phase_Current_W += diversifiedCurrent / 3;
                        Diversified_Total_Phase_Current_B += diversifiedCurrent / 3;
                    }
                }
            }
        }

        /// <summary>
        /// Check pass/fail/warning counts
        /// </summary>
        public void Check_PassFailWarningCount()
        {
            // Reset counters
            PassCount = 0;
            WarningCount = 0;
            FailCount = 0;

            foreach (var circuit in CCTs)
            {
                if (!circuit.IsSpareOrSpace)
                {
                    if (circuit.Check_Pass)
                    {
                        PassCount++;
                    }
                    else
                    {
                        FailCount++;
                    }

                    if (circuit.Warning_Count > 0)
                    {
                        WarningCount++;
                    }
                }
            }

            // Set overall pass flag
            DB_All_Circuits_Pass = FailCount == 0;
        }

        /// <summary>
        /// Load EFLI and electrical data from Revit distribution board element
        /// </summary>
        public void LoadElectricalDataFromRevit()
        {
            try
            {
                if (DB_Element == null) return;

                // Load EFLI_R (Earth Fault Loop Impedance - Resistance)
                var efliRParam = DB_Element.LookupParameter("DB EFLi R");
                if (efliRParam != null && efliRParam.HasValue)
                {
                    EFLI_R = efliRParam.AsDouble();
                }

                // Load EFLI_X (Earth Fault Loop Impedance - Reactance)
                var efliXParam = DB_Element.LookupParameter("DB EFLi X");
                if (efliXParam != null && efliXParam.HasValue)
                {
                    EFLI_X = efliXParam.AsDouble();
                }

                // Load DBVD (Distribution Board Voltage Drop)
                var dbvdParam = DB_Element.LookupParameter("DB VD");
                if (dbvdParam != null && dbvdParam.HasValue)
                {
                    DBVD = dbvdParam.AsDouble();
                }

                // Load Device kA Rating
                var kaRatingParam = DB_Element.LookupParameter("Device kA Rating");
                if (kaRatingParam != null && kaRatingParam.HasValue)
                {
                    Device_kA_Rating = kaRatingParam.AsDouble();
                }

                // Load PSCC (Prospective Short Circuit Current)
                var psccParam = DB_Element.LookupParameter("PSCC");
                if (psccParam != null && psccParam.HasValue)
                {
                    PSCC = psccParam.AsDouble();
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw - use default values
                DB_Check_Result = $"Error loading electrical data: {ex.Message}";
                Data_Good = false;
            }
        }

        /// <summary>
        /// Commit distribution board data to Revit
        /// </summary>
        /// <returns>True if successful</returns>
        public bool CommitToRevit()
        {
            try
            {
                // In a real implementation, this would commit all DB data to Revit parameters
                // For now, just mark as committed and clear unsaved changes flag
                HasUnsavedChanges = false;
                Data_Good = true;
                DB_Check_Result = "Data committed successfully";

                // Also commit all circuits
                foreach (var circuit in CCTs)
                {
                    circuit.CommitToRevit();
                }

                return true;
            }
            catch (Exception ex)
            {
                DB_Check_Result = $"Error committing DB data to Revit: {ex.Message}";
                Data_Good = false;
                return false;
            }
        }

        /// <summary>
        /// Load distribution board data from Revit (refresh from current state)
        /// </summary>
        /// <returns>True if successful</returns>
        public bool LoadFromRevit()
        {
            try
            {
                // In a real implementation, this would reload all data from Revit
                LoadDistributionBoardData();
                HasUnsavedChanges = false;
                return true;
            }
            catch (Exception ex)
            {
                DB_Check_Result = $"Error loading DB data from Revit: {ex.Message}";
                Data_Good = false;
                return false;
            }
        }

        #endregion
    }
}
