using System;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using BecaAzureSQL;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MEP.PowerBIM_6.Handlers;
using Microsoft.Extensions.DependencyInjection;
using Application = System.Windows.Application;

namespace MEP.PowerBIM_6.ViewModels
{
    /// <summary>
    /// Base ViewModel class for PowerBIM 6 WPF application
    /// Provides common functionality for all ViewModels including:
    /// - CommunityToolkit.Mvvm integration
    /// - ExternalEvent request handling
    /// - Dependency injection support
    /// - Common UI state management
    /// - Thread-safe UI updates
    /// </summary>
    public abstract partial class BaseViewModel : ObservableObject, IDisposable
    {
        #region Fields

        protected readonly IServiceProvider _serviceProvider;
        protected readonly BecaActivityLoggerData _logger;
        protected RequestHandler_PB6 _requestHandler;
        protected ExternalEvent _externalEvent;
        private bool _disposed = false;

        #endregion

        #region Observable Properties

        /// <summary>
        /// Indicates if the ViewModel is currently busy processing a request
        /// </summary>
        [ObservableProperty]
        private bool _isBusy;

        /// <summary>
        /// Current status message to display to the user
        /// </summary>
        [ObservableProperty]
        private string _statusMessage = string.Empty;

        /// <summary>
        /// Indicates if there are unsaved changes
        /// </summary>
        [ObservableProperty]
        private bool _hasUnsavedChanges;

        /// <summary>
        /// Indicates if the current state is valid
        /// </summary>
        [ObservableProperty]
        private bool _isValid = true;

        /// <summary>
        /// Progress value for long-running operations (0-100)
        /// </summary>
        [ObservableProperty]
        private double _progress;

        /// <summary>
        /// Indicates if progress should be shown
        /// </summary>
        [ObservableProperty]
        private bool _showProgress;

        /// <summary>
        /// Navigation parameter passed when navigating to this ViewModel
        /// </summary>
        [ObservableProperty]
        private object _navigationParameter;

        #endregion

        #region Public Properties

        /// <summary>
        /// Service provider for dependency injection
        /// </summary>
        public IServiceProvider ServiceProvider => _serviceProvider;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the BaseViewModel
        /// </summary>
        /// <param name="serviceProvider">Service provider for dependency injection</param>
        protected BaseViewModel(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = serviceProvider.GetService<BecaActivityLoggerData>();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Initialize the ViewModel with request handling capabilities
        /// </summary>
        /// <param name="requestHandler">The request handler for ExternalEvent communication</param>
        /// <param name="externalEvent">The ExternalEvent for Revit API thread safety</param>
        public virtual void Initialize(RequestHandler_PB6 requestHandler, ExternalEvent externalEvent)
        {
            _requestHandler = requestHandler ?? throw new ArgumentNullException(nameof(requestHandler));
            _externalEvent = externalEvent ?? throw new ArgumentNullException(nameof(externalEvent));
        }

        /// <summary>
        /// Called when the ViewModel should wake up from a busy state
        /// This is typically called by the RequestHandler after processing a request
        /// </summary>
        public virtual void WakeUp()
        {
            SafeUIUpdate(() =>
            {
                IsBusy = false;
                ShowProgress = false;
                Progress = 0;
                OnPropertyChanged(nameof(IsBusy));
                OnPropertyChanged(nameof(ShowProgress));
                OnPropertyChanged(nameof(Progress));
            });
        }

        #endregion

        #region Protected Methods

        /// <summary>
        /// Make a request to the Revit API via ExternalEvent
        /// </summary>
        /// <param name="requestId">The request to make</param>
        protected void MakeRequest(RequestId_PB6 requestId)
        {
            if (_requestHandler == null || _externalEvent == null)
            {
                _logger?.Log("RequestHandler or ExternalEvent not initialized. Call Initialize() first.", LogType.Error);
                return;
            }

            try
            {
                SetBusyState(true, $"Processing {requestId}...");
                _requestHandler.Request.Make(requestId);
                _externalEvent.Raise();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to make request: {requestId}", LogType.Error);
                SetBusyState(false);
                StatusMessage = $"Error: {ex.Message}";
            }
        }

        /// <summary>
        /// Get a service from the dependency injection container
        /// </summary>
        /// <typeparam name="T">The service type</typeparam>
        /// <returns>The service instance</returns>
        protected T GetService<T>() where T : class
        {
            return _serviceProvider.GetRequiredService<T>();
        }

        /// <summary>
        /// Set the busy state with optional message and progress
        /// </summary>
        /// <param name="isBusy">Whether the ViewModel is busy</param>
        /// <param name="message">Optional status message</param>
        /// <param name="showProgress">Whether to show progress indicator</param>
        protected void SetBusyState(bool isBusy, string message = "", bool showProgress = false)
        {
            SafeUIUpdate(() =>
            {
                IsBusy = isBusy;
                StatusMessage = message;
                ShowProgress = showProgress;
                if (!isBusy)
                {
                    Progress = 0;
                }
            });
        }

        /// <summary>
        /// Update progress for long-running operations
        /// </summary>
        /// <param name="progress">Progress value (0-100)</param>
        /// <param name="message">Optional progress message</param>
        protected void UpdateProgress(double progress, string message = "")
        {
            SafeUIUpdate(() =>
            {
                Progress = Math.Max(0, Math.Min(100, progress));
                if (!string.IsNullOrEmpty(message))
                {
                    StatusMessage = message;
                }
            });
        }

        /// <summary>
        /// Mark the ViewModel as having unsaved changes
        /// </summary>
        protected void MarkAsChanged()
        {
            HasUnsavedChanges = true;
        }

        /// <summary>
        /// Mark the ViewModel as saved (no unsaved changes)
        /// </summary>
        protected void MarkAsSaved()
        {
            HasUnsavedChanges = false;
        }

        /// <summary>
        /// Perform a UI update safely on the UI thread
        /// </summary>
        /// <param name="updateAction">The action to perform</param>
        protected void SafeUIUpdate(Action updateAction)
        {
            if (Application.Current?.Dispatcher.CheckAccess() == true)
            {
                updateAction();
            }
            else
            {
                Application.Current?.Dispatcher.Invoke(updateAction);
            }
        }

        // REMOVED: Async UI update method - use SafeUIUpdate() instead
        // Revit applications should avoid async patterns in ViewModels

        /// <summary>
        /// Validate the current state of the ViewModel
        /// Override in derived classes to implement specific validation logic
        /// </summary>
        /// <returns>True if the state is valid</returns>
        protected virtual bool ValidateState()
        {
            return true;
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to refresh data
        /// </summary>
        [RelayCommand]
        protected virtual void Refresh()
        {
            MakeRequest(RequestId_PB6.RefreshData);
        }

        #endregion

        #region Property Change Handlers

        /// <summary>
        /// Called when HasUnsavedChanges property changes
        /// </summary>
        /// <param name="value">The new value</param>
        partial void OnHasUnsavedChangesChanged(bool value)
        {
            // Notify commands that depend on this property
            RefreshCommand.NotifyCanExecuteChanged();
        }

        /// <summary>
        /// Called when IsBusy property changes
        /// </summary>
        /// <param name="value">The new value</param>
        partial void OnIsBusyChanged(bool value)
        {
            // Notify commands that depend on this property
            RefreshCommand.NotifyCanExecuteChanged();
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Dispose of resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Dispose of resources
        /// </summary>
        /// <param name="disposing">Whether we are disposing</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Dispose managed resources
                _disposed = true;
            }
        }

        #endregion
    }
}
