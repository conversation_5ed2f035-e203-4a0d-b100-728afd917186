## PowerBIM 6 Folder Structure

```
MEP.PowerBIM_6/
├── 📁 RevitCommands/
│   └── PowerBIM_6_Command.cs 
│
├── 📁 Views/ (WPF Windows & UserControls)
│   ├── MainWindow.xaml + .cs 
│   ├── DbEditWindow.xaml + .cs 
│   ├── CircuitEditWindow.xaml + .cs 
│   ├── AdvancedSettingsWindow.xaml + .cs 
│   ├── DbSettingsWindow.xaml + .cs 
│   ├── ExportWindow.xaml + .cs 
│   ├── ImportSettingsWindow.xaml + .cs 
│   └── 📁 UserControls/
│       ├── DistributionBoardSummaryControl.xaml + .cs 
│       ├── ProjectSettingsControl.xaml + .cs
│       ├── CircuitDataGridControl.xaml + .cs 
│       └── BulkOperationsControl.xaml + .cs 
│
├── 📁 ViewModels/ (MVVM ViewModels with CommunityToolkit)
│   ├── BaseViewModel.cs 
│   ├── MainViewModel.cs 
│   ├── DbEditViewModel.cs 
│   ├── CircuitEditViewModel.cs 
│   ├── AdvancedSettingsViewModel.cs 
│   ├── DbSettingsViewModel.cs
│   ├── ExportViewModel.cs 
│   ├── ImportSettingsViewModel.cs 
│   └── 📁 ItemViewModels/
│       ├── DistributionBoardItemViewModel.cs 
│       ├── CircuitItemViewModel.cs 
│       └── ProjectInfoViewModel.cs
│
├── 📁 Models/ (Data Models with ObservableObject)
│   ├── ProjectInfoModel.cs
│   ├── DistributionBoardModel.cs
│   ├── CircuitModel.cs
│   ├── BreakerModel.cs
│   ├── CableModel.cs
│   ├── SettingsModel.cs
│   ├── ExportSettingsModel.cs
│   └── 📁 Enums/
│       ├── RequestId_PB6.cs
│       ├── PathModeEnum.cs
│       └── CircuitStatusEnum.cs
│
├── 📁 Services/ (Business Logic & Revit API)
│   ├── 📁 Interfaces/
│   │   ├── IRevitService.cs
│   │   ├── IDataService.cs
│   │   ├── IExportService.cs
│   │   ├── IImportService.cs
│   │   ├── ICalculationService.cs
│   │   └── IPathEditingService.cs
│   ├── RevitService.cs
│   ├── DataService.cs
│   ├── ExportService.cs
│   ├── ImportService.cs
│   ├── CalculationService.cs
│   └── PathEditingService.cs
│
├── 📁 Handlers/ (Modeless Architecture)
│   ├── ModelessMainWindowHandler.cs
│   ├── Request_PB6_Configure.cs
│   ├── RequestHandler_PB6.cs
│   └── 📁 SpecializedHandlers/
│       ├── CircuitEditHandler.cs
│       ├── DbEditHandler.cs
│       └── PathEditingHandler.cs
│
├── 📁 Converters/ 
│   ├── BooleanToVisibilityConverter.cs ✅
│   ├── StatusColorConverter.cs ✅
│   ├── NumericFormatConverter.cs (new)
│   ├── CircuitStatusConverter.cs (new)
│   ├── PathModeConverter.cs (new)
│   └── ValidationErrorConverter.cs (new)
│
├── 📁 Helpers/ (Utility Classes)
│   ├── EditCircuitPathClicker_WPF.cs
│   ├── EditDBPathClicker_WPF.cs
│   ├── DataGridHelper.cs
│   ├── WindowHelper.cs
│   └── ValidationHelper.cs
│
├── 📁 Resources/ (WPF Resources)
│   ├── Styles/
│   │   ├── ButtonStyles.xaml
│   │   ├── DataGridStyles.xaml
│   │   └── WindowStyles.xaml
│   ├── Templates/
│   │   ├── DataTemplates.xaml
│   │   └── ControlTemplates.xaml
│   └── Images/
│       └── BecaLogoBlack.png ✅
│
└── 📁 Extensions/ (Extension Methods)
    ├── ObservableCollectionExtensions.cs
    ├── DataGridExtensions.cs
    └── ValidationExtensions.cs
```