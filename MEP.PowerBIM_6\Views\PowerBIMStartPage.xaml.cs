﻿using MEP.PowerBIM_6.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace MEP.PowerBIM_6.Views
{
    /// <summary>
    /// Interaction logic for PowerBIMStartPage.xaml
    /// </summary>
    public partial class PowerBIMStartPage : Page
    {
        //public PowerBIMMainViewModel ViewModel { get; private set; }

        public PowerBIMStartPage(/*PowerBIMMainViewModel viewModel*/)
        {
            InitializeComponent();

            //ViewModel = viewModel;
            //DataContext = ViewModel;
        }
    }
}
