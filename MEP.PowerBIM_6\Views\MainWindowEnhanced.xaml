﻿<Window
    x:Class="MEP.PowerBIM_6.Views.MainWindowEnhanced"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Beca Tools | MEP | Electrical"
    Width="1400"
    Height="900"
    MinWidth="1200"
    MinHeight="700"
    Background="White"
    Closing="Window_Closing"
    WindowStartupLocation="CenterScreen"
    WindowState="Normal"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  Converters  -->
            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />
            <converters:NumericFormatConverter x:Key="NumericFormatConverter" />
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <TextBlock
                Grid.Row="0"
                Grid.ColumnSpan="5"
                Margin="10,5,0,10"
                Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                Text="PowerBIM" />
            <StackPanel
                Grid.Column="1" Margin="0,0,0,10"
                HorizontalAlignment="Right"
                VerticalAlignment="Bottom"
                Orientation="Horizontal">
                <Button
                    x:Name="btnHelp"
                    Margin="4,0"
                    Click="OpenHelpDialog"
                    Content="Help and Documentation"
                    Foreground="#12A8B2"
                    Style="{StaticResource MaterialDesignFlatButton}" />
                <Button
                    x:Name="btnAbout"
                    Margin="4,0"
                    Click="OpenAboutWindow"
                    Content="About"
                    Foreground="#12A8B2"
                    Style="{StaticResource MaterialDesignFlatButton}" />
            </StackPanel>
            <Separator
                Grid.ColumnSpan="6"
                Margin="10,45,15,0"
                Background="#FFCE00">
                <Separator.LayoutTransform>
                    <ScaleTransform ScaleY="3.5" />
                </Separator.LayoutTransform>
            </Separator>
        </Grid>

        <!--  Main Content Area with Enhanced Navigation  -->
        <Grid Grid.Row="1" Margin="8">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  Enhanced Ultra-Minimalist Collapsible Sidebar  -->
            <Grid Grid.Column="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Main Sidebar -->
                <Border x:Name="MainSidebar" Grid.Column="0" Width="280" Margin="0,0,4,0"
                        Background="White" CornerRadius="4" BorderThickness="0">
                    <Border.Effect>
                        <DropShadowEffect Color="#000000" Opacity="0.08" ShadowDepth="0" BlurRadius="20"/>
                    </Border.Effect>

                    <StackPanel>
                        <!-- Header with Collapse Button -->
                        <Border Padding="24,20" BorderBrush="#F0F0F0" BorderThickness="0,0,0,1">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Navigation"
                                           FontSize="20" FontWeight="Light"
                                           Foreground="#323130"/>
                                <Button x:Name="CollapseButton" Grid.Column="1" Background="Transparent" BorderThickness="0"
                                        Width="32" Height="32" Cursor="Hand" Click="CollapseButton_Click"
                                        ToolTip="Collapse Sidebar">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Background" Value="Transparent"/>
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#F0F0F0"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                    <Button.Template>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" CornerRadius="4">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Button.Template>
                                    <TextBlock Text="‹" FontSize="16" Foreground="#605E5C" FontWeight="Bold"/>
                                </Button>
                            </Grid>
                        </Border>

                        <!-- Clean Navigation with Icons -->
                        <StackPanel Margin="0,16,0,8">
                            <!-- Selected Home Item -->
                            <Button x:Name="navHome" Background="Transparent" BorderThickness="0" Padding="20,16"
                                    HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Cursor="Hand"
                                    Margin="0,0,0,1" Click="NavigateToHome">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🏠" FontSize="14" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="Home" FontSize="14" Foreground="#605E5C" FontWeight="Medium" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Distribution Boards -->
                            <Button x:Name="navDistributionBoards" Background="Transparent" BorderThickness="0" Padding="20,16"
                                    HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Cursor="Hand"
                                    Margin="0,0,0,1" Click="NavigateToDistributionBoards">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="⚡" FontSize="14" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="Distribution Boards" FontSize="14" Foreground="#605E5C" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- MCC -->
                            <Button x:Name="navCircuits" Background="Transparent" BorderThickness="0" Padding="20,16"
                                    HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Cursor="Hand"
                                    Margin="0,0,0,1" Click="NavigateToCircuits">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="⚙️" FontSize="14" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="MCC" FontSize="14" Foreground="#605E5C" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Bulk Data Entry -->
                            <Button x:Name="navResults" Background="Transparent" BorderThickness="0" Padding="20,16"
                                    HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Cursor="Hand"
                                    Margin="0,0,0,1" Click="NavigateToBulkOperations">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🗂️" FontSize="14" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="Bulk Data Entry" FontSize="14" Foreground="#605E5C" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- PB Settings -->
                            <Button x:Name="navSettings" Background="Transparent" BorderThickness="0" Padding="20,16"
                                    HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Cursor="Hand"
                                    Click="NavigateToSettings">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🎚️" FontSize="14" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="PB Settings" FontSize="14" Foreground="#605E5C" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>

                        <!-- Enhanced Summary with Status Indicators -->
                        <Border Margin="20,16,20,20" Padding="0,16,0,0" BorderBrush="#F0F0F0" BorderThickness="0,1,0,0">
                            <StackPanel>
                                <!-- Main Stats -->
                                <Grid Margin="0,0,0,16">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding DistributionBoards.Count, Mode=OneWay, FallbackValue=24}" FontSize="24" FontWeight="Light" Foreground="#323130" HorizontalAlignment="Center"/>
                                        <TextBlock Text="DBs" FontSize="10" Foreground="#8A8886" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding TotalCircuitCount, Mode=OneWay, FallbackValue=156}" FontSize="24" FontWeight="Light" Foreground="#323130" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Circuits" FontSize="10" Foreground="#8A8886" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding SuccessRate, Mode=OneWay, FallbackValue=91%, StringFormat={}{0:F0}%}" FontSize="24" FontWeight="Light" Foreground="#107C10" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Success" FontSize="10" Foreground="#8A8886" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </Grid>

                                <!-- Status Indicators - Minimalist Style -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,0">
                                    <Border Background="#F0F8F0" CornerRadius="8" Padding="8,4" Margin="-10,0,6,0" BorderThickness="1" BorderBrush="#E8F5E8">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="✓" Foreground="#107C10" FontSize="11" Margin="0,0,4,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="{Binding TotalPassCount, Mode=OneWay, FallbackValue=142}" Foreground="#107C10" FontSize="11" FontWeight="Medium" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Background="#FFFAF0" CornerRadius="8" Padding="8,4" Margin="25,0,36,0" BorderThickness="1" BorderBrush="#FFF4CE">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="⚠" Foreground="#F7630C" FontSize="11" Margin="0,0,4,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="{Binding TotalWarningCount, Mode=OneWay, FallbackValue=12}" Foreground="#F7630C" FontSize="11" FontWeight="Medium" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Background="#FDF8F8" CornerRadius="8" Padding="8,4" BorderThickness="1" BorderBrush="#FDE7E9">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="✗" Foreground="#D13438" FontSize="11" Margin="0,0,4,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="{Binding TotalFailCount, Mode=OneWay, FallbackValue=2}" Foreground="#D13438" FontSize="11" FontWeight="Medium" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Collapsed Sidebar (Icon Only) -->
                <Border x:Name="CollapsedSidebar" Grid.Column="1" Width="60" Visibility="Collapsed"
                        Background="White" CornerRadius="4" BorderThickness="0" Margin="8,0,0,0">
                    <Border.Effect>
                        <DropShadowEffect Color="#000000" Opacity="0.08" ShadowDepth="0" BlurRadius="20"/>
                    </Border.Effect>

                    <StackPanel>
                        <!-- Collapsed Header with Expand Button -->
                        <Border Padding="14,20" BorderBrush="#F0F0F0" BorderThickness="0,0,0,1">
                            <Button x:Name="ExpandButton" Background="Transparent" BorderThickness="0"
                                    Width="32" Height="32" Cursor="Hand" Click="ExpandButton_Click"
                                    ToolTip="Expand Sidebar">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F0F0F0"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="4">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Button.Template>
                                <TextBlock Text="›" FontSize="16" Foreground="#605E5C" FontWeight="Bold"/>
                            </Button>
                        </Border>

                        <!-- Collapsed Navigation Icons -->
                        <StackPanel Margin="0,16,0,8">
                            <!-- Selected Home -->
                            <Button x:Name="navHomeCollapsed" Background="Transparent" BorderThickness="0" Padding="18,16"
                                    HorizontalAlignment="Stretch" Cursor="Hand" Margin="0,0,0,4"
                                    ToolTip="Home" Click="NavigateToHome">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <TextBlock Text="🏠" FontSize="14" Foreground="#605E5C" HorizontalAlignment="Center"/>
                            </Button>

                            <!-- Other Icons -->
                            <Button x:Name="navDistributionBoardsCollapsed" Background="Transparent" BorderThickness="0" Padding="18,16"
                                    HorizontalAlignment="Stretch" Cursor="Hand" Margin="0,0,0,4"
                                    ToolTip="Distribution Boards" Click="NavigateToDistributionBoards">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <TextBlock Text="⚡" FontSize="14" Foreground="#605E5C" HorizontalAlignment="Center"/>
                            </Button>

                            <Button x:Name="navCircuitsCollapsed" Background="Transparent" BorderThickness="0" Padding="18,16"
                                    HorizontalAlignment="Stretch" Cursor="Hand" Margin="0,0,0,4"
                                    ToolTip="MCC" Click="NavigateToCircuits">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <TextBlock Text="⚙️" FontSize="14" Foreground="#605E5C" HorizontalAlignment="Center"/>
                            </Button>

                            <Button x:Name="navResultsCollapsed" Background="Transparent" BorderThickness="0" Padding="18,16"
                                    HorizontalAlignment="Stretch" Cursor="Hand" Margin="0,0,0,4"
                                    ToolTip="Bulk Data Entry" Click="NavigateToBulkOperations">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <TextBlock Text="🗂️" FontSize="14" Foreground="#605E5C" HorizontalAlignment="Center"/>
                            </Button>

                            <Button x:Name="navSettingsCollapsed" Background="Transparent" BorderThickness="0" Padding="18,16"
                                    HorizontalAlignment="Stretch" Cursor="Hand"
                                    ToolTip="PB Settings" Click="NavigateToSettings">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <TextBlock Text="🎚️" FontSize="14" Foreground="#605E5C" HorizontalAlignment="Center"/>
                            </Button>
                        </StackPanel>

                        <!-- Collapsed Status Dots -->
                        <Border Margin="14,16,14,20" Padding="0,16,0,0" BorderBrush="#F0F0F0" BorderThickness="0,1,0,0">
                            <StackPanel HorizontalAlignment="Center">
                                <Ellipse Width="8" Height="8" Fill="#107C10" Margin="0,0,0,4" ToolTip="{Binding TotalPassCount, Mode=OneWay, StringFormat={}{0} Passed}"/>
                                <Ellipse Width="8" Height="8" Fill="#F7630C" Margin="0,0,0,4" ToolTip="{Binding TotalWarningCount, Mode=OneWay, StringFormat={}{0} Warnings}"/>
                                <Ellipse Width="8" Height="8" Fill="#D13438" ToolTip="{Binding TotalFailCount, Mode=OneWay, StringFormat={}{0} Failed}"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>
            </Grid>

            <!--  Splitter  -->
            <GridSplitter
                Grid.Column="1"
                Width="14"
                HorizontalAlignment="Center"
                VerticalAlignment="Stretch"
                Background="White" />

            <!--  Main Content Frame  -->
            <Border
                Grid.Column="2"
                Background="{DynamicResource MaterialDesignCardBackground}"
                CornerRadius="4">
                <Frame
                    x:Name="MainFrame"
                    Margin="8"
                    NavigationUIVisibility="Hidden" />
            </Border>
        </Grid>

        <!--  Status Bar with Progress  -->
        <Border
            Grid.Row="2"
            Padding="16,8"
            Background="White">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  Status Text  -->
                <TextBlock
                    x:Name="StatusText"
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Text="{Binding StatusMessage, FallbackValue=Ready}" />

                <!--  Progress Area  -->
                <StackPanel
                    Grid.Column="1"
                    VerticalAlignment="Center"
                    Orientation="Horizontal"
                    Visibility="{Binding IsProgressVisible, Converter={StaticResource BoolToVisConverter}}">
                    <ProgressBar
                        Width="200"
                        Height="16"
                        Margin="0,0,8,0"
                        Maximum="100"
                        Value="{Binding ProgressValue}" />
                    <TextBlock
                        MinWidth="35"
                        VerticalAlignment="Center"
                        Text="{Binding ProgressValue, StringFormat={}{0:F0}%}" />
                </StackPanel>

                <!--  Busy Indicator  -->
                <materialDesign:Card
                    Grid.Column="1"
                    Padding="8,4"
                    VerticalAlignment="Center"
                    Background="#8d0e84"
                    Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisConverter}}">
                    <StackPanel Orientation="Horizontal">
                        <ProgressBar
                            Width="16"
                            Height="16"
                            Margin="0,0,8,0"
                            IsIndeterminate="True"
                            Style="{StaticResource MaterialDesignCircularProgressBar}" />
                        <TextBlock
                            VerticalAlignment="Center"
                            Foreground="White"
                            Text="Processing..." />
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </Border>

        <!--  Quick Actions Toolbar  -->
        <StackPanel
            Grid.Row="3"
            Margin="0,10,0,10"
            HorizontalAlignment="Center"
            Orientation="Horizontal">
            <Button
                x:Name="btnLoadData"
                Margin="0,0,10,0"
                Background="#12A8B2"
                BorderBrush="#12A8B2"
                Command="{Binding LoadProjectDataCommand}"
                Content="Load Data"
                Foreground="White"
                ToolTip="Load project data from Revit" />
            <Button
                x:Name="btnRunSizer"
                Margin="0,0,10,0"
                Background="#12A8B2"
                BorderBrush="#12A8B2"
                Command="{Binding RunAutoSizerCommand}"
                Content="Run Auto Sizer"
                Foreground="White"
                ToolTip="Run automatic cable and breaker sizing" />
            <Button
                x:Name="btnSave"
                Background="#FFCE00"
                BorderBrush="#FFCE00"
                Command="{Binding SaveCommand}"
                Content="Save"
                ToolTip="Save results to Revit" />
        </StackPanel>

        <!--  Footer  -->
        <TextBlock
            Grid.Row="4"
            Margin="0,5,15,10"
            HorizontalAlignment="Right"
            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
            Text="Make Everyday Better" />
        <Image
            Grid.Row="4"
            Grid.ColumnSpan="2"
            Height="25"
            Margin="15,5,0,10"
            HorizontalAlignment="Left"
            Source="/Common.UI.WPF;component/Resources/BecaLogoBlack.png" />

    </Grid>
</Window>
