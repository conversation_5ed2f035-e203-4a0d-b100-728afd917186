<Page
    x:Class="MEP.PowerBIM_6.Views.CircuitsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Circuit Analysis"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />
            <converters:NumericFormatConverter x:Key="NumericFormatConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  Page Header  -->
        <StackPanel Grid.Row="0" Margin="0,0,0,16">
            <TextBlock Style="{StaticResource MaterialDesignHeadline4TextBlock}" Text="Circuit Analysis" />
            <TextBlock
                Margin="0,4,0,0"
                Foreground="{DynamicResource MaterialDesignBodyLight}"
                Text="Detailed circuit editing and electrical calculations" />
        </StackPanel>

        <!--  Distribution Board Selection  -->
        <materialDesign:Card
            Grid.Row="1"
            Margin="0,0,0,16"
            Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Grid.Column="0"
                    Margin="0,0,16,0"
                    VerticalAlignment="Center"
                    FontWeight="SemiBold"
                    Text="Distribution Board:" />

                <ComboBox
                    Grid.Column="1"
                    Margin="0,0,16,0"
                    DisplayMemberPath="Name"
                    ItemsSource="{Binding DistributionBoards}"
                    SelectedItem="{Binding SelectedDistributionBoard}" />

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        Text="Auto Calculate:" />
                    <CheckBox VerticalAlignment="Center" IsChecked="{Binding AutoCalculate}" />
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!--  Action Toolbar  -->
        <Border
            Grid.Row="2"
            Margin="0,0,0,16"
            Padding="12"
            Background="{DynamicResource MaterialDesignDivider}"
            CornerRadius="4">
            <StackPanel Orientation="Horizontal">
                <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                    Margin="0,0,8,0" 
                    Command="{Binding RecalculateCommand}"
                    Content="Recalculate"
                    ToolTip="Recalculate all circuits" />
                <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                    Margin="0,0,8,0"
                    Command="{Binding SaveCommand}"
                    Content="Save"
                    ToolTip="Save circuit changes" />
                <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                    Margin="0,0,8,0"
                    Command="{Binding UndoCommand}"
                    Content="Undo"
                    IsEnabled="{Binding HasUnsavedChanges}"
                    ToolTip="Undo changes" />
                <Separator Margin="8,0" />
                <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                    Margin="0,0,8,0"
                    Command="{Binding BulkEditLightingCommand}"
                    Content="Bulk Edit Lighting"
                    ToolTip="Bulk edit lighting circuits" />
                <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                    Margin="0,0,8,0"
                    Command="{Binding BulkEditPowerCommand}"
                    Content="Bulk Edit Power"
                    ToolTip="Bulk edit power circuits" />
                <Separator Margin="8,0" />
                <Button Background="#12A8B2"
BorderBrush="#12A8B2" Foreground="White"
                    Command="{Binding ExportCircuitsCommand}"
                    Content="Export"
                    ToolTip="Export circuit data" />
            </StackPanel>
        </Border>

        <!--  Circuits DataGrid  -->
        <materialDesign:Card Grid.Row="3" Padding="16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  Search and Filter  -->
                <Grid Grid.Row="0" Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBox
                        Grid.Column="0"
                        Margin="0,0,8,0"
                        materialDesign:HintAssist.Hint="Search circuits..."
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" />

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <CheckBox
                            Margin="8,0"
                            Content="Show Only Failed"
                            IsChecked="{Binding ShowOnlyFailed}" />
                        <CheckBox
                            Margin="8,0"
                            Content="Show Only Lighting"
                            IsChecked="{Binding ShowOnlyLighting}" />
                        <CheckBox
                            Margin="8,0"
                            Content="Show Only Power"
                            IsChecked="{Binding ShowOnlyPower}" />
                    </StackPanel>
                </Grid>

                <!--  Circuits DataGrid  -->
                <DataGrid
                    Grid.Row="1"
                    AutoGenerateColumns="False"
                    CanUserAddRows="False"
                    CanUserDeleteRows="False"
                    ItemsSource="{Binding FilteredCircuitData}"
                    SelectedItem="{Binding SelectedCircuit}"
                    Style="{StaticResource MaterialDesignDataGrid}">

                    <DataGrid.Columns>
                        <!--  Status Indicator  -->
                        <DataGridTemplateColumn Width="50" Header="Status">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <materialDesign:PackIcon
                                        Width="16"
                                        Height="16"
                                        HorizontalAlignment="Center"
                                        Foreground="{Binding Status, Converter={StaticResource StatusColorConverter}}"
                                        Kind="{Binding StatusIcon}" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Circuit Number  -->
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding CctNumber}"
                            Header="Circuit" />

                        <!--  Description  -->
                        <DataGridTextColumn
                            Width="200"
                            Binding="{Binding ScheduleDescription}"
                            Header="Description" />

                        <!--  Current Values  -->
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding RevitCurrent, Converter={StaticResource NumericFormatConverter}}"
                            Header="Revit (A)" />

                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding CctPowerBimCurrent, Converter={StaticResource NumericFormatConverter}}"
                            Header="PowerBIM (A)" />

                        <!--  Cable Information  -->
                        <DataGridTextColumn
                            Width="120"
                            Binding="{Binding ScheduleCableToFirst}"
                            Header="Cable to First" />

                        <DataGridTextColumn
                            Width="120"
                            Binding="{Binding ScheduleCableToFinal}"
                            Header="Cable to Final" />

                        <!--  Length  -->
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding CctLength, Converter={StaticResource NumericFormatConverter}}"
                            Header="Length (m)" />

                        <!--  Voltage Drop  -->
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding VoltageDropPercent, Converter={StaticResource NumericFormatConverter}}"
                            Header="VD %" />

                        <!--  Protection  -->
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding BreakerRating, Converter={StaticResource NumericFormatConverter}}"
                            Header="Breaker (A)" />

                        <!--  Type Indicators  -->
                        <DataGridCheckBoxColumn
                            Width="60"
                            Binding="{Binding IsLighting}"
                            Header="Light" />

                        <DataGridCheckBoxColumn
                            Width="60"
                            Binding="{Binding IsPower}"
                            Header="Power" />

                        <!--  Manual Override  -->
                        <DataGridCheckBoxColumn
                            Width="70"
                            Binding="{Binding ManualCurrent}"
                            Header="Manual" />
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>
