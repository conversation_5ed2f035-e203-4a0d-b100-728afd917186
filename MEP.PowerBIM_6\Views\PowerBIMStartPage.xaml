﻿<Page
    x:Class="MEP.PowerBIM_6.Views.PowerBIMStartPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="PowerBIM Start"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

        </ResourceDictionary>

    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Project Settings Panel  -->
        <GroupBox
            Grid.Row="0"
            Margin="0,0,0,16"
            Header="Project Settings">
            <Grid Margin="8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Voltage Drop Settings  -->
                <TextBlock
                    Grid.Row="0"
                    Grid.Column="0"
                    Margin="0,0,8,0"
                    VerticalAlignment="Center"
                    Text="System VD Max:" />
                <StackPanel
                    Grid.Row="0"
                    Grid.Column="1"
                    Orientation="Horizontal">
                    <RadioButton
                        x:Name="guiSysVD5pc"
                        Margin="0,0,16,0"
                        Content="5%" />
                    <RadioButton x:Name="guiSysVD7pc" Content="7%" />
                </StackPanel>

                <!--  Cable Selection  -->
                <TextBlock
                    Grid.Row="0"
                    Grid.Column="2"
                    Margin="16,0,8,0"
                    VerticalAlignment="Center"
                    Text="Cable Selection:" />
                <StackPanel
                    Grid.Row="0"
                    Grid.Column="3"
                    Orientation="Horizontal">
                    <RadioButton
                        x:Name="guiNZcableSel"
                        Margin="0,0,16,0"
                        Content="NZ (30°C)" />
                    <RadioButton x:Name="guiAUScableSel" Content="AUS (40°C)" />
                </StackPanel>
            </Grid>
        </GroupBox>

        <!--  Action Buttons  -->
        <StackPanel
            Grid.Row="1"
            Margin="0,0,0,16"
            HorizontalAlignment="Left"
            Orientation="Horizontal">
            <Button
                x:Name="btnRunSizer"
                Margin="0,0,8,0"
                Padding="16,8"
                Content="Run Auto Sizer" />
            <Button
                x:Name="btnSave"
                Margin="0,0,8,0"
                Padding="16,8"
                Content="Save" />
            <Button
                x:Name="btnExport"
                Margin="0,0,8,0"
                Padding="16,8"
                Content="Export" />
            <Button
                x:Name="btnHelp"
                Padding="16,8"
                Content="Help" />
        </StackPanel>

        <!--  Distribution Boards DataGrid  -->
        <GroupBox Grid.Row="2" Header="Distribution Boards">
            <DataGrid
                x:Name="dgvDBSel"
                Margin="8"
                AutoGenerateColumns="False"
                CanUserAddRows="False"
                CanUserDeleteRows="False"
                GridLinesVisibility="All"
                HeadersVisibility="All"
                SelectionMode="Extended">

                <DataGrid.Columns>
                    <DataGridCheckBoxColumn
                        Width="60"
                        Binding="{Binding IsSelected, Mode=TwoWay}"
                        Header="Select" />
                    <DataGridTextColumn
                        Width="60"
                        Binding="{Binding Result_PassCount}"
                        Header="Pass"
                        IsReadOnly="True" />
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding Result_WarningCount}"
                        Header="Warning"
                        IsReadOnly="True" />
                    <DataGridTextColumn
                        Width="60"
                        Binding="{Binding Result_FailCount}"
                        Header="Fail"
                        IsReadOnly="True" />
                    <DataGridTextColumn
                        Width="200"
                        Binding="{Binding GUI_Notes}"
                        Header="Notes"
                        IsReadOnly="True" />
                    <DataGridCheckBoxColumn
                        Width="120"
                        Binding="{Binding Update_Required, Mode=OneWay}"
                        Header="Update Required"
                        IsReadOnly="True" />
                </DataGrid.Columns>
            </DataGrid>
        </GroupBox>

        <!--  Status and Progress  -->
        <StackPanel
            Grid.Row="3"
            HorizontalAlignment="Right"
            Orientation="Horizontal">
            <TextBlock
                x:Name="StatusLabel"
                Margin="0,0,16,0"
                VerticalAlignment="Center"
                Text="Ready" />
            <ProgressBar
                x:Name="ProgressBar"
                Width="200"
                Height="20"
                Visibility="Collapsed" />
        </StackPanel>
    </Grid>
</Page>
