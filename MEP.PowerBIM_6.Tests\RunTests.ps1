# PowerBIM 6 Test Execution Script
# This script runs different categories of tests for MEP.PowerBIM_6

param(
    [string]$TestCategory = "All",
    [string]$Configuration = "Debug",
    [switch]$Coverage = $false,
    [switch]$Parallel = $false,
    [string]$OutputPath = ".\TestResults",
    [switch]$Verbose = $false
)

# Set up paths
$ProjectRoot = Split-Path -Parent $PSScriptRoot
$TestProject = "$PSScriptRoot\MEP.PowerBIM_6.Tests.csproj"
$RunSettings = "$PSScriptRoot\runsettings.xml"

# Ensure output directory exists
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force
}

Write-Host "PowerBIM 6 Test Runner" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host "Test Category: $TestCategory" -ForegroundColor Yellow
Write-Host "Configuration: $Configuration" -ForegroundColor Yellow
Write-Host "Output Path: $OutputPath" -ForegroundColor Yellow
Write-Host ""

# Build the test project first
Write-Host "Building test project..." -ForegroundColor Cyan
dotnet build $TestProject --configuration $Configuration --verbosity minimal
if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed. Exiting."
    exit 1
}

# Prepare test command
$testCommand = "dotnet test `"$TestProject`" --configuration $Configuration --settings `"$RunSettings`" --results-directory `"$OutputPath`""

# Add category filter (xUnit uses Trait instead of TestCategory)
switch ($TestCategory.ToLower()) {
    "unit" {
        $testCommand += " --filter `"Category=Unit`""
        Write-Host "Running Unit Tests..." -ForegroundColor Cyan
    }
    "integration" {
        $testCommand += " --filter `"Category=Integration`""
        Write-Host "Running Integration Tests..." -ForegroundColor Cyan
    }
    "regression" {
        $testCommand += " --filter `"Category=Regression`""
        Write-Host "Running Regression Tests..." -ForegroundColor Cyan
    }
    "services" {
        $testCommand += " --filter `"Category=Services`""
        Write-Host "Running Service Tests..." -ForegroundColor Cyan
    }
    "viewmodels" {
        $testCommand += " --filter `"Category=ViewModels`""
        Write-Host "Running ViewModel Tests..." -ForegroundColor Cyan
    }
    "calculations" {
        $testCommand += " --filter `"Category=Calculations`""
        Write-Host "Running Calculation Tests..." -ForegroundColor Cyan
    }
    "performance" {
        $testCommand += " --filter `"Category=Performance`""
        Write-Host "Running Performance Tests..." -ForegroundColor Cyan
    }
    "smoke" {
        $testCommand += " --filter `"Category=Unit|Category=Integration`""
        Write-Host "Running Smoke Tests (Unit + Integration)..." -ForegroundColor Cyan
    }
    "all" {
        Write-Host "Running All Tests..." -ForegroundColor Cyan
    }
    default {
        $testCommand += " --filter `"Category=$TestCategory`""
        Write-Host "Running Custom Category: $TestCategory..." -ForegroundColor Cyan
    }
}

# Add coverage collection
if ($Coverage) {
    $testCommand += " --collect:`"XPlat Code Coverage`""
    Write-Host "Code coverage enabled" -ForegroundColor Yellow
}

# Add parallel execution
if ($Parallel) {
    $testCommand += " --parallel"
    Write-Host "Parallel execution enabled" -ForegroundColor Yellow
}

# Add verbosity
if ($Verbose) {
    $testCommand += " --verbosity detailed"
} else {
    $testCommand += " --verbosity normal"
}

# Add logger options
$testCommand += " --logger trx --logger html --logger console;verbosity=minimal"

Write-Host ""
Write-Host "Executing: $testCommand" -ForegroundColor Gray
Write-Host ""

# Execute tests
$startTime = Get-Date
Invoke-Expression $testCommand
$exitCode = $LASTEXITCODE
$endTime = Get-Date
$duration = $endTime - $startTime

Write-Host ""
Write-Host "Test Execution Summary" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host "Duration: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor Yellow
Write-Host "Exit Code: $exitCode" -ForegroundColor $(if ($exitCode -eq 0) { "Green" } else { "Red" })

# Process results
$trxFiles = Get-ChildItem -Path $OutputPath -Filter "*.trx" -Recurse | Sort-Object LastWriteTime -Descending
if ($trxFiles.Count -gt 0) {
    $latestTrx = $trxFiles[0]
    Write-Host "Latest Results: $($latestTrx.FullName)" -ForegroundColor Yellow
    
    # Parse TRX for summary (basic parsing)
    try {
        [xml]$trxContent = Get-Content $latestTrx.FullName
        $summary = $trxContent.TestRun.ResultSummary
        $counters = $summary.Counters
        
        Write-Host ""
        Write-Host "Test Results Summary:" -ForegroundColor Cyan
        Write-Host "  Total: $($counters.total)" -ForegroundColor White
        Write-Host "  Passed: $($counters.passed)" -ForegroundColor Green
        Write-Host "  Failed: $($counters.failed)" -ForegroundColor Red
        Write-Host "  Skipped: $($counters.inconclusive)" -ForegroundColor Yellow
        
        if ($counters.failed -gt 0) {
            Write-Host ""
            Write-Host "Failed Tests:" -ForegroundColor Red
            $failedTests = $trxContent.TestRun.Results.UnitTestResult | Where-Object { $_.outcome -eq "Failed" }
            foreach ($test in $failedTests) {
                Write-Host "  - $($test.testName)" -ForegroundColor Red
            }
        }
    }
    catch {
        Write-Warning "Could not parse TRX file for detailed summary"
    }
}

# Process coverage results
if ($Coverage) {
    $coverageFiles = Get-ChildItem -Path $OutputPath -Filter "coverage.cobertura.xml" -Recurse
    if ($coverageFiles.Count -gt 0) {
        Write-Host ""
        Write-Host "Code Coverage Report: $($coverageFiles[0].FullName)" -ForegroundColor Cyan
        
        # You could add coverage report generation here
        # For example, using ReportGenerator tool:
        # reportgenerator -reports:$($coverageFiles[0].FullName) -targetdir:$OutputPath\CoverageReport -reporttypes:Html
    }
}

# Open results if requested
if ($env:OPEN_RESULTS -eq "true") {
    $htmlFiles = Get-ChildItem -Path $OutputPath -Filter "*.html" -Recurse
    if ($htmlFiles.Count -gt 0) {
        Start-Process $htmlFiles[0].FullName
    }
}

Write-Host ""
if ($exitCode -eq 0) {
    Write-Host "All tests completed successfully!" -ForegroundColor Green
} else {
    Write-Host "Some tests failed. Check the results above." -ForegroundColor Red
}

exit $exitCode
