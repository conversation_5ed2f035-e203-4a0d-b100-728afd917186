# MEP.PowerBIM_6 Architecture Documentation

## Overview

MEP.PowerBIM_6 is a modern WPF application for electrical engineering calculations in Autodesk Revit. It represents a complete architectural evolution from the legacy WinForms-based MEP.PowerBIM_1.5, implementing a clean MVVM pattern with dependency injection, thread-safe Revit API communication, and modular design.

## Entry Point and Application Flow

### 1. Entry Point: PowerBIM_6_Command

The application starts with the `PowerBIM_6_Command` class, which inherits from `BecaBaseCommand`:

```csharp
[Transaction(TransactionMode.Manual)]
public class PowerBIM_6_Command : BecaBaseCommand
```

**Key Responsibilities:**
- Validates prerequisites (electrical equipment exists in model)
- Initializes PowerBIM data structures (`PowerBIM_ProjectInfo`, `List<PowerBIM_DBData>`)
- Launches the WPF modeless architecture via `ModelessMainWindowHandler`
- Provides error handling and logging

**Execution Flow:**
1. `ExecuteBecaCommand()` → Entry point from Revit
2. `ValidatePrerequisites()` → Checks for electrical elements
3. `InitializePowerBIMData()` → Creates project info and distribution boards
4. `LoadExistingPowerBIMData()` → Populates data from Revit model
5. `ModelessMainWindowHandler.ShowWindow()` → Launches WPF UI

### 2. Modeless Architecture Handler

The `ModelessMainWindowHandler` is the central orchestrator for the WPF application:

**Key Features:**
- Singleton pattern for main window management
- Dependency injection container setup
- ExternalEvent and RequestHandler creation
- Cross-form communication coordination

**Architecture Components Created:**
- Service Provider (DI container)
- RequestHandler_PB6 (Revit API communication)
- ExternalEvent (thread-safe Revit operations)
- Main ViewModel initialization

## Core Architecture Patterns

### 1. MVVM Pattern with CommunityToolkit.Mvvm

**ViewModels:**
- `MainViewModel` - Main window coordination
- `CircuitEditViewModel` - Critical circuit editing functionality
- `DbEditViewModel` - Distribution board editing
- `BaseViewModel` - Common functionality and ExternalEvent handling

**Models:**
- `ProjectInfoModel` - WPF-bindable project data
- `DistributionBoardModel` - WPF-bindable DB data
- `CircuitModel` - WPF-bindable circuit data

**Views:**
- `MainWindowEnhanced.xaml` - Primary interface with page-based navigation
- `MainWindow.xaml` - Alternative tabbed interface (legacy)
- Page-based views for navigation:
  - `HomePage.xaml` - Dashboard and overview
  - `DistributionBoardsPage.xaml` - Distribution board management
  - `CircuitsPage.xaml` - Circuit management and editing
  - `BulkOperationsPage.xaml` - Bulk operations and results
  - `ProjectSettingsPage.xaml` - Project settings and configuration
  - `ExportPage.xaml` - Export operations
  - `AboutPage.xaml` - Application information
- Modal dialogs for specific operations:
  - `CircuitEditWindow.xaml` - Circuit editing (most critical form)
  - `DbEditWindow.xaml` - Distribution board editing
  - `AdvancedSettingsWindow.xaml` - Advanced settings management

### 2. Thread-Safe Revit API Communication

**Request System:**
```
WPF UI Thread → RequestId_PB6 → Request_PB6_Configure → ExternalEvent → RequestHandler_PB6 → Revit API Thread
```

**Components:**
- `RequestId_PB6` - Enumeration of all possible operations
- `Request_PB6_Configure` - Thread-safe request queuing using `Interlocked`
- `RequestHandler_PB6` - IExternalEventHandler implementation
- `ExternalEvent` - Revit's thread-safe mechanism

### 3. Page-Based Navigation Architecture

**Navigation Service:**
- `INavigationService` - Manages page-based navigation within MainWindowEnhanced
- `NavigationService` - Implementation with Frame-based navigation
- `PageKeys` - Static class defining navigation page identifiers

**Navigation Features:**
- Frame-based content area for page switching
- Navigation history (back/forward functionality)
- Page caching for performance
- Navigation events for UI updates
- Sidebar navigation with collapsible design

**Page Structure:**
- Each page is a UserControl with corresponding ViewModel
- Pages are loaded on-demand and cached
- Navigation parameters supported for data passing
- Consistent page lifecycle management

### 4. Dependency Injection

**Service Configuration:**
- Core dependencies (UIDocument, Document, Logger)
- Business services (ICalculationService, IDataService, IRevitService)
- ViewModels (transient registration)
- Navigation service (singleton registration)

**Service Interfaces:**
- `ICalculationService` - Electrical calculations (located in Services/Interfaces/)
- `IDataService` - Data transformation and validation (located in Services/Interfaces/)
- `IRevitService` - Revit API operations (located in Services/Interfaces/)
- `INavigationService` - Page-based navigation management (located in Services/Interfaces/)
- `IExportService` - Export operations (located in Services/Interfaces/)
- Additional interfaces defined in ServiceConfiguration.cs for dependency injection

## Data Model Architecture

### 1. Core Data Classes (Independent Implementation)

**PowerBIM_ProjectInfo:**
- Project-level settings and metadata
- System voltage, temperature, calculation parameters
- Revit document references

**PowerBIM_DBData (Distribution Board Data):**
- Represents electrical panels/distribution boards
- Contains list of circuits (`List<PowerBIM_CircuitData>`)
- Calculation results, validation status
- Load calculations and diversification

**PowerBIM_CircuitData:**
- Individual electrical circuits
- Current calculations, cable sizing
- Voltage drop calculations
- Validation and compliance checking

### 2. WPF Binding Models

**Wrapper Pattern:**
- Each core data class has a corresponding WPF model
- Models inherit from `ObservableObject` for property change notifications
- Bidirectional data synchronization with core classes

## Business Logic Services

### 1. CalculationService

**Primary Functions:**
- `RecalculateDistributionBoard()` - Complete DB recalculation
- `RunCircuitCalculations()` - Individual circuit calculations
- `CalculatePowerBIMCurrent()` - Current determination logic
- `CalculateVoltageDrop()` - Voltage drop calculations

### 2. DataService

**Primary Functions:**
- Data transformation between core and WPF models
- Validation and business rule enforcement
- Filtering and searching capabilities
- Caching for performance optimization

### 3. RevitService

**Primary Functions:**
- Revit API operations (element creation, modification)
- Parameter reading/writing
- Element selection and filtering
- Transaction management

## Key Architectural Decisions

### 1. Independent Implementation Strategy
- No dependencies on legacy MEP.PowerBIM_1.5 codebase
- Clean separation from legacy technical debt
- Modern C# patterns and practices

### 2. Revit-Safe Threading
- All Revit API operations execute synchronously in Revit context
- No async/await patterns (prevents threading issues)
- ExternalEvent for UI-to-Revit communication

### 3. Modular Design
- Clear separation of concerns
- Dependency injection for testability
- Interface-based service contracts

### 4. Data Integrity
- Wrapper pattern preserves original data structures
- Explicit save operations prevent accidental data loss
- Validation at multiple layers

## Critical Components Priority

### 1. Highest Priority
- `CircuitEditViewModel` - Most critical user interface
- `CalculationService` - Core electrical calculations
- `RequestHandler_PB6` - Revit API communication

### 2. High Priority
- `MainViewModel` - Primary coordination
- `PowerBIM_DBData` - Core data structure
- `ModelessMainWindowHandler` - Application lifecycle

### 3. Medium Priority
- Export/Import functionality
- Advanced settings management
- Path editing capabilities

## Integration Points

### 1. Revit API Integration
- Element selection and filtering
- Parameter reading/writing
- Electrical system analysis
- Transaction management

### 2. Legacy Data Compatibility
- Maintains compatibility with existing Revit models
- Preserves parameter naming conventions
- Supports legacy calculation methods

### 3. Beca Infrastructure
- `BecaActivityLoggerData` for consistent logging
- `BecaBaseCommand` for command framework
- Beca naming conventions and patterns

## Future Extensibility

The architecture supports:
- Additional calculation methods
- New export formats
- Enhanced UI components
- Additional Revit API integrations
- Plugin architecture for custom calculations

This modular, service-oriented architecture provides a solid foundation for electrical engineering calculations while maintaining clean separation of concerns and thread-safe Revit API integration.

## Detailed Class Relationships

### 1. Data Flow Hierarchy

```
PowerBIM_6_Command (Entry Point)
    ↓
ModelessMainWindowHandler (Orchestrator)
    ↓
MainViewModel (Primary Coordinator)
    ↓
DistributionBoardItemViewModel (DB Management)
    ↓
CircuitEditViewModel (Circuit Management)
    ↓
CircuitItemViewModel (Individual Circuits)
```

### 2. Core Data Relationships

```
PowerBIM_ProjectInfo (1)
    ↓
PowerBIM_DBData (1..n) - Distribution Boards
    ↓
PowerBIM_CircuitData (1..n) - Circuits per DB
    ↓
PowerBIM_BreakerData (1) - Circuit Protection
    ↓
PowerBIM_CableData (1..n) - Cable Segments
```

### 3. Service Dependencies

```
ViewModels
    ↓ (Dependency Injection)
ICalculationService → CalculationService
IDataService → DataService
IRevitService → RevitService
    ↓ (Uses)
Core Data Classes (PowerBIM_DBData, etc.)
    ↓ (Operates on)
Revit API Elements
```

## Request Processing Flow

### 1. User Action to Revit API

```
1. User clicks button in WPF UI
2. ViewModel RelayCommand executes
3. ViewModel calls MakeRequest(RequestId_PB6.SomeOperation)
4. Request_PB6_Configure.Make() queues request (thread-safe)
5. ExternalEvent.Raise() signals Revit
6. Revit calls RequestHandler_PB6.Execute() on main thread
7. RequestHandler processes request via services
8. Services perform Revit API operations
9. Results propagate back to UI via property notifications
```

### 2. Critical Request Types

**Project Operations:**
- `SaveProject` - Commit all changes to Revit
- `LoadProjectInfo` - Refresh project settings
- `CommitProjectInfo` - Save project parameters

**Circuit Operations:**
- `UpdateCircuits` - Recalculate all circuits
- `RecalculateCircuits` - Specific circuit calculations
- `SaveCircuitEditEnhanced` - Save circuit editing changes

**Distribution Board Operations:**
- `LoadDistributionBoards` - Refresh DB data
- `SaveDistributionBoard` - Commit DB changes
- `RefreshDistributionBoardSummary` - Update calculations

### 3. Request Processing Implementation Status

**Implemented Request Handlers (15 of 48 total):**
- Project Operations: SaveProject, SaveSettings, CommitProjectInfo, LoadProjectInfo
- Distribution Board Operations: LoadDistributionBoards, SaveDistributionBoard, RefreshDistributionBoardSummary, UpdaterRequired_All
- Circuit Operations: UpdateCircuits, RecalculateCircuits, BulkEditLighting, BulkEditPower
- Path Editing: OpenPathCustomizing, ActivatePathEditView
- Export Operations: ExportData, ExportCircuitImages
- UI Operations: WakeFormUp, RefreshData

**Missing Request Handlers (33+ remaining):**
- Circuit Editing Enhanced: SaveCircuitEditEnhanced, RecalcAndRefreshCircuitToForm, etc.
- Path Editing: OpenPathCustomizingForDB, SetCircuitLengthManual, etc.
- Import Operations: ImportFromCsv, ProcessImportSettings
- Advanced Settings: CommitAdvancedSettings, CommitDistributionBoardSettings
- Manual Operations: SetManualLock, SaveUserNotes
- Additional Circuit Operations: SaveCircuitData, RecalculateAllCircuits, BulkEditOther, etc.

**Note:** The RequestHandler_PB6 implementation is incomplete and requires additional development to handle all defined request types.

## Data Synchronization Strategy

### 1. Original Data Preservation

Each WPF model maintains a reference to its original core data:

```csharp
public class CircuitModel : ObservableObject
{
    private readonly PowerBIM_CircuitData _originalData;

    public void LoadFromOriginalData() { /* Sync from core */ }
    public void SaveToOriginalData() { /* Sync to core */ }
}
```

### 2. Change Tracking

- `HasUnsavedChanges` properties track modifications
- Explicit save operations prevent data loss
- Undo functionality through original data restoration

### 3. Validation Pipeline

```
User Input → WPF Model Validation → Business Rule Validation → Revit API Validation → Commit
```

## Error Handling Strategy

### 1. Layered Error Handling

**UI Layer:**
- Input validation with immediate feedback
- User-friendly error messages
- Graceful degradation

**Service Layer:**
- Business rule validation
- Exception logging via BecaActivityLoggerData
- Rollback capabilities

**Revit API Layer:**
- Transaction management
- Element validation
- API-specific error handling

### 2. Logging Integration

All components use `BecaActivityLoggerData` for consistent logging:
- Information: Normal operations
- Warning: Non-critical issues
- Error: Failures requiring attention

## Performance Considerations

### 1. Lazy Loading

- Distribution boards loaded on-demand
- Circuit data populated when accessed
- UI virtualization for large datasets

### 2. Caching Strategy

- Calculation results cached until data changes
- Revit element references cached per session
- ComboBox data sources cached for performance

### 3. Memory Management

- Proper disposal of ViewModels and services
- Event handler cleanup
- Revit element reference management

## Testing Strategy

### 1. Unit Testing

- Service classes with dependency injection
- Calculation logic verification
- Data transformation testing

### 2. Integration Testing

- Revit API interaction testing
- End-to-end workflow validation
- Performance benchmarking

### 3. UI Testing

- ViewModel command testing
- Data binding verification
- User interaction simulation

## Electrical Engineering Domain Concepts

### 1. Key Terminology

**DB (Distribution Board):**
- Electrical panels that distribute power to circuits
- Contains circuit breakers and protection devices
- Central point for electrical calculations and management

**CCT (Circuit):**
- Individual electrical circuits from distribution board to loads
- Each circuit has protection (breaker), cable, and connected loads
- Subject to electrical code compliance calculations

**PowerBIM Current:**
- Calculated current based on connected loads and diversity factors
- May differ from Revit's native current calculations
- Used for cable sizing and protection coordination

### 2. Critical Calculations

**Voltage Drop Calculations:**
- Ensures voltage at load is within acceptable limits
- Considers cable length, size, and load current
- Critical for electrical code compliance

**Diversified Load Calculations:**
- Applies diversity factors to total connected load
- Accounts for non-simultaneous operation of loads
- Required for distribution board sizing

**Short Circuit Analysis:**
- PSCC (Prospective Short Circuit Current) calculations
- EFLI (Earth Fault Loop Impedance) calculations
- Critical for protection device coordination

### 3. Electrical Parameters

**Circuit Properties:**
- `CCT_Number` - Circuit identification
- `CCT_PowerBIM_Current` - Calculated design current
- `CCT_Length` - Cable run length (manual or calculated)
- `Number_Of_Poles` - Single or multi-phase circuits
- `Schedule_Cable_To_First` - Cable specification

**Distribution Board Properties:**
- `Schedule_DB_Name` - Panel identification
- `Device_kA_Rating` - Fault current rating
- `PSCC` - Prospective short circuit current
- `EFLI_R`, `EFLI_X` - Earth fault loop impedance components

**Validation Results:**
- `Check_Pass` - Circuit compliance status
- `Warning_Count` - Number of warnings
- `DB_All_Circuits_Pass` - Overall panel compliance

### 4. Business Rules

**Circuit Validation:**
- Voltage drop within limits (typically 5%)
- Cable current capacity adequate for load
- Protection device properly sized
- Earth fault protection adequate

**Distribution Board Validation:**
- Total load within panel capacity
- Fault current within device rating
- Phase balance within acceptable limits
- All circuits individually compliant

## Integration with Revit MEP

### 1. Revit Element Mapping

**Electrical Equipment:**
- Maps to `PowerBIM_DBData` (Distribution Boards)
- Contains panel-specific parameters
- Links to electrical systems

**Electrical Systems:**
- Maps to `PowerBIM_CircuitData` (Circuits)
- Contains circuit-specific parameters
- Links connected loads and devices

**Family Instances:**
- Electrical fixtures and equipment
- Source of load calculations
- Connected to electrical systems

### 2. Parameter Management

**Project Parameters:**
- System voltage settings
- Ambient temperature
- Calculation preferences
- Diversity factors

**Element Parameters:**
- Circuit numbers and descriptions
- Current values and calculations
- Cable specifications
- Protection device settings

### 3. Model Synchronization

**Data Flow:**
- Revit model → PowerBIM data structures
- Calculations performed in PowerBIM
- Results written back to Revit parameters
- Model updated with calculated values

**Change Management:**
- Detects model changes requiring recalculation
- Preserves manual overrides
- Maintains calculation history
- Supports undo/redo operations
