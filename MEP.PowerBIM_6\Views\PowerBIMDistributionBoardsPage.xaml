﻿<Page
    x:Class="MEP.PowerBIM_6.Views.PowerBIMDistributionBoardsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Distribution Boards"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />
            <converters:NumericFormatConverter x:Key="NumericFormatConverter" />
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
        </ResourceDictionary>

    </Page.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <StackPanel
            Grid.Row="0"
            Margin="0,0,0,16"
            Orientation="Horizontal">
            <materialDesign:PackIcon
                Width="24"
                Height="24"
                Margin="0,0,8,0"
                VerticalAlignment="Center"
                Kind="ViewDashboard" />
            <TextBlock
                VerticalAlignment="Center"
                Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                Text="Distribution Boards" />
        </StackPanel>

        <!--  Action Bar  -->
        <materialDesign:Card
            Grid.Row="1"
            Margin="0,0,0,16"
            Padding="16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Main Action Buttons  -->
                <StackPanel
                    Grid.Row="0"
                    Margin="0,0,0,8"
                    Orientation="Horizontal">
                    <Button
                        Margin="0,0,8,0"
                        Command="{Binding LoadProjectDataCommand}"
                        Content="Load Project Data"
                        ToolTip="Load distribution boards from Revit project" />
                    <Button
                        Margin="0,0,8,0"
                        Command="{Binding RunAutoSizerCommand}"
                        Content="Run Auto Sizer"
                        ToolTip="Run automatic cable and breaker sizing for selected DBs" />
                    <Button
                        Margin="0,0,8,0"
                        Command="{Binding SaveCommand}"
                        Content="Save to Revit"
                        ToolTip="Save calculation results back to Revit" />
                    <Button
                        Margin="0,0,8,0"
                        Command="{Binding ExportCommand}"
                        Content="Export Results"
                        ToolTip="Export results to Excel or CSV" />
                    <Separator Margin="8,0" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />
                    <Button
                        Margin="0,0,8,0"
                        Content="Enhanced Circuit Edit"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        ToolTip="Open enhanced circuit editor" />
                    <Button
                        Margin="0,0,8,0"
                        Content="Write Lighting"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        ToolTip="Write lighting calculations to schedule" />
                    <Button
                        Margin="0,0,8,0"
                        Content="Write Power"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        ToolTip="Write power calculations to schedule" />
                </StackPanel>

                <!--  Selection and Filter Controls  -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <!--  Selection Controls  -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <Button
                            Margin="0,0,8,0"
                            Command="{Binding SelectAllDBsCommand}"
                            Content="Select All"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                        <Button
                            Margin="0,0,8,0"
                            Command="{Binding SelectNoneDBsCommand}"
                            Content="Select None"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                        <Button
                            Margin="0,0,16,0"
                            Command="{Binding RefreshCommand}"
                            Content="Refresh"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                    </StackPanel>

                    <!--  Selection Summary  -->
                    <StackPanel
                        Grid.Column="2"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock VerticalAlignment="Center" Text="Selected: " />
                        <TextBlock
                            Margin="0,0,8,0"
                            VerticalAlignment="Center"
                            FontWeight="SemiBold"
                            Text="{Binding SelectedDBCount}" />
                        <TextBlock
                            Margin="0,0,4,0"
                            VerticalAlignment="Center"
                            Text="of" />
                        <TextBlock
                            VerticalAlignment="Center"
                            FontWeight="SemiBold"
                            Text="{Binding DistributionBoards.Count, Mode=OneWay}" />
                        <TextBlock
                            Margin="4,0,0,0"
                            VerticalAlignment="Center"
                            Text="DBs" />
                    </StackPanel>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!--  Distribution Boards DataGrid  -->
        <materialDesign:Card Grid.Row="2" Padding="8">
            <DataGrid
                x:Name="dgDistributionBoards"
                AlternatingRowBackground="{DynamicResource MaterialDesignDivider}"
                AutoGenerateColumns="False"
                CanUserAddRows="False"
                CanUserDeleteRows="False"
                GridLinesVisibility="Horizontal"
                HeadersVisibility="Column"
                ItemsSource="{Binding DistributionBoards}"
                RowBackground="Transparent"
                SelectionMode="Extended">

                <DataGrid.Columns>
                    <!--  Selection Column  -->
                    <DataGridCheckBoxColumn
                        Width="60"
                        Binding="{Binding IsSelected, Mode=TwoWay}"
                        ElementStyle="{StaticResource MaterialDesignDataGridCheckBoxColumnStyle}"
                        Header="Select" />

                    <!--  Manual Lock Column (matches original ManualLock column)  -->
                    <DataGridCheckBoxColumn
                        Width="50"
                        Binding="{Binding IsManuallyLocked, Mode=TwoWay}"
                        ElementStyle="{StaticResource MaterialDesignDataGridCheckBoxColumnStyle}"
                        Header="Lock" />

                    <!--  DB Name Column (matches original dBNameDataGridViewTextBoxColumn)  -->
                    <DataGridTextColumn
                        Width="180"
                        Binding="{Binding DisplayName}"
                        Header="DB Name"
                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}" TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="SemiBold" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsLocked}" Value="True">
                                        <Setter Property="Foreground" Value="Gray" />
                                        <Setter Property="FontStyle" Value="Italic" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Result_FailCount}" Value="0">
                                        <Setter Property="Foreground" Value="Green" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Data_Good}" Value="False">
                                        <Setter Property="Foreground" Value="LightGray" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!--  Pass Count Column (matches original dBPassCountDataGridViewTextBoxColumn)  -->
                    <DataGridTextColumn
                        Width="60"
                        Binding="{Binding Result_PassCount}"
                        Header="Pass"
                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}" TargetType="TextBlock">
                                <Setter Property="Foreground" Value="Green" />
                                <Setter Property="FontWeight" Value="SemiBold" />
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!--  Warning Count Column (matches original dBWarningCountDataGridViewTextBoxColumn)  -->
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding Result_WarningCount}"
                        Header="Warning"
                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}" TargetType="TextBlock">
                                <Setter Property="Foreground" Value="Orange" />
                                <Setter Property="FontWeight" Value="SemiBold" />
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!--  Fail Count Column (matches original dBFailCountDataGridViewTextBoxColumn)  -->
                    <DataGridTextColumn
                        Width="60"
                        Binding="{Binding Result_FailCount}"
                        Header="Fail"
                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}" TargetType="TextBlock">
                                <Setter Property="Foreground" Value="Red" />
                                <Setter Property="FontWeight" Value="SemiBold" />
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!--  User Notes Button Column (matches original DB_UserNotes)  -->
                    <DataGridTemplateColumn Width="100" Header="User Notes">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button
                                    Padding="4,2"
                                    Command="{Binding DataContext.EditUserNotesCommand, RelativeSource={RelativeSource AncestorType=Page}}"
                                    CommandParameter="{Binding}"
                                    Content="{Binding User_Notes, TargetNullValue='Add notes', FallbackValue='Add notes'}"
                                    FontSize="10"
                                    Style="{StaticResource MaterialDesignFlatButton}"
                                    ToolTip="Click to add or edit user notes" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!--  GUI Notes Column (matches original dBNotesDataGridViewTextBoxColumn)  -->
                    <DataGridTemplateColumn Width="200" Header="System Notes">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button
                                    Padding="4,2"
                                    Command="{Binding DataContext.ViewSystemNotesCommand, RelativeSource={RelativeSource AncestorType=Page}}"
                                    CommandParameter="{Binding}"
                                    Content="View Details"
                                    FontSize="10"
                                    Style="{StaticResource MaterialDesignFlatButton}"
                                    ToolTip="{Binding GUI_Notes}"
                                    Visibility="{Binding GUI_Notes, Converter={StaticResource StringToVisibilityConverter}}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!--  DB Settings Button Column (matches original DB_Settings)  -->
                    <DataGridTemplateColumn Width="80" Header="Settings">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button
                                    Command="{Binding DataContext.EditDBSettingsCommand, RelativeSource={RelativeSource AncestorType=Page}}"
                                    CommandParameter="{Binding}"
                                    Style="{StaticResource MaterialDesignIconButton}"
                                    ToolTip="Edit DB Settings">
                                    <materialDesign:PackIcon
                                        Width="16"
                                        Height="16"
                                        Kind="Settings" />
                                </Button>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!--  Update Required Column (matches original DB_UpdateRequired)  -->
                    <DataGridCheckBoxColumn
                        Width="120"
                        Binding="{Binding Update_Required, Mode=OneWay}"
                        ElementStyle="{StaticResource MaterialDesignDataGridCheckBoxColumnStyle}"
                        Header="Update Required"
                        IsReadOnly="True" />
                </DataGrid.Columns>

                <!--  Row Style for highlighting  -->
                <DataGrid.RowStyle>
                    <Style BasedOn="{StaticResource MaterialDesignDataGridRow}" TargetType="DataGridRow">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Background" Value="{DynamicResource PrimaryHueLightBrush}" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Result_FailCount}" Value="0">
                                <DataTrigger.Setters>
                                    <Setter Property="BorderBrush" Value="Green" />
                                    <Setter Property="BorderThickness" Value="0,0,0,1" />
                                </DataTrigger.Setters>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
        </materialDesign:Card>


    </Grid>
</Page>
