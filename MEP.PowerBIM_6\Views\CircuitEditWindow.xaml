﻿<Window
    x:Class="MEP.PowerBIM_6.Views.CircuitEditWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    Title="{Binding DistributionBoardName, StringFormat='Live Circuit Editor: {0}'}"
    Width="1600"
    Height="900"
    Background="White"
    WindowStartupLocation="CenterOwner"
    WindowState="Maximized">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  Converters  -->
            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />

            <!--  DataGrid Cell Style for validation results  -->
            <Style x:Key="ValidationCellStyle" TargetType="DataGridCell">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding CircuitCheckResult}" Value="PASS">
                        <Setter Property="Background" Value="LightGreen" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding CircuitCheckResult}" Value="FAIL">
                        <Setter Property="Background" Value="LightCoral" />
                    </DataTrigger>
                    <DataTrigger Binding="{Binding CircuitCheckResult}" Value="WARNING">
                        <Setter Property="Background" Value="LightYellow" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>
    </Window.Resources>

    <materialDesign:DialogHost>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <!--  Header & Toolbar  -->
                <RowDefinition Height="*" />
                <!--  Main Content  -->
                <RowDefinition Height="Auto" />
                <!--  Phase Summary  -->
                <RowDefinition Height="Auto" />
                <!--  Action Buttons  -->
            </Grid.RowDefinitions>

            <!--  Header & Toolbar  -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Title  -->
                <StackPanel
                    Grid.Column="0"
                    Margin="16,8"
                    Orientation="Horizontal">
                    <Image Height="24" Margin="0,0,8,0" />
                    <TextBlock
                        VerticalAlignment="Center"
                        Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                        Text="{Binding DistributionBoardName, StringFormat='Live Circuit Editor: {0}'}" />
                </StackPanel>

                <!--  Toolbar  -->
                <StackPanel
                    Grid.Column="1"
                    Margin="16,8"
                    Orientation="Horizontal">
                    <Button
                        Margin="4"
                        Command="{Binding ToggleAutoCalculateCommand}"
                        Content="{Binding AutoCalculateButtonText}"
                        Style="{StaticResource MaterialDesignRaisedButton}" />
                    <Button
                        Margin="4"
                        Background="#12A8B2"
                        BorderBrush="#12A8B2"
                        Command="{Binding ActivatePathEditCommand}"
                        Content="Activate Path Edit"
                        Foreground="White"
                        Style="{StaticResource MaterialDesignRaisedAccentButton}" />
                    <Button
                        Margin="4"
                        Command="{Binding RefreshFromRevitCommand}"
                        Content="Refresh"
                        Style="{StaticResource MaterialDesignOutlinedButton}" />
                </StackPanel>

                <Separator
                    Grid.Row="1"
                    Grid.ColumnSpan="2"
                    Margin="15,0,20,0"
                    Background="#FFCE00">
                    <Separator.LayoutTransform>
                        <ScaleTransform ScaleY="3.5" />
                    </Separator.LayoutTransform>
                </Separator>
            </Grid>

            <!--  Main Content Area  -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="9*" />
                    <ColumnDefinition Width="151*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <!--  Search Bar  -->
                    <RowDefinition Height="*" />
                    <!--  DataGrid  -->
                </Grid.RowDefinitions>

                <!--  Search Bar  -->
                <materialDesign:Card
                    Grid.Row="0"
                    Grid.ColumnSpan="2"
                    Margin="20,16,20,10">
                    <Grid Margin="16,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <TextBox
                            Grid.Column="0"
                            materialDesign:HintAssist.Hint="Search circuits by number or description..."
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" />

                        <Button
                            Grid.Column="1"
                            Margin="8,0,0,0"
                            Command="{Binding ClearSearchCommand}"
                            Content="Clear"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                    </Grid>
                </materialDesign:Card>

                <!--  Main Circuit DataGrid  -->
                <materialDesign:Card
                    Grid.Row="1"
                    Grid.ColumnSpan="2"
                    Margin="20,10,20,10">
                    <DataGrid
                        Grid.ColumnSpan="2"
                        Margin="16,0,16,8"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        ItemsSource="{Binding CircuitData}"
                        ScrollViewer.CanContentScroll="True"
                        ScrollViewer.HorizontalScrollBarVisibility="Auto"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        SelectedItem="{Binding SelectedCircuit}"
                        Style="{StaticResource MaterialDesignDataGrid}">

                        <DataGrid.Columns>
                            <!--  Circuit Number  -->
                            <DataGridTextColumn
                                Width="80"
                                Binding="{Binding CircuitNumber}"
                                CellStyle="{StaticResource ValidationCellStyle}"
                                Header="Circuit #"
                                IsReadOnly="True" />

                            <!--  Description  -->
                            <DataGridTextColumn
                                Width="150"
                                Binding="{Binding Description}"
                                Header="Description" />

                            <!--  Device Rating  -->
                            <DataGridComboBoxColumn
                                Width="100"
                                Header="Device Rating"
                                ItemsSource="{Binding DataContext.DeviceRatings, RelativeSource={RelativeSource AncestorType=Window}}"
                                SelectedItemBinding="{Binding DeviceRating}" />

                            <!--  Device Curve Type  -->
                            <DataGridComboBoxColumn
                                Width="80"
                                Header="Curve Type"
                                ItemsSource="{Binding DataContext.CurveTypes, RelativeSource={RelativeSource AncestorType=Window}}"
                                SelectedItemBinding="{Binding DeviceCurveType}" />

                            <!--  Protection Device  -->
                            <DataGridComboBoxColumn
                                Width="120"
                                Header="Protection Device"
                                ItemsSource="{Binding DataContext.ProtectionDevices, RelativeSource={RelativeSource AncestorType=Window}}"
                                SelectedItemBinding="{Binding ProtectionDevice}" />

                            <!--  RCD Protection  -->
                            <DataGridComboBoxColumn
                                Width="100"
                                Header="RCD Protection"
                                ItemsSource="{Binding DataContext.RcdProtectionOptions, RelativeSource={RelativeSource AncestorType=Window}}"
                                SelectedItemBinding="{Binding RcdProtection}" />

                            <!--  Other Controls  -->
                            <DataGridTextColumn
                                Width="120"
                                Binding="{Binding OtherControls}"
                                Header="Other Controls" />

                            <!--  Cable to First  -->
                            <DataGridComboBoxColumn
                                Width="100"
                                Header="Cable to First"
                                ItemsSource="{Binding DataContext.CableTypes, RelativeSource={RelativeSource AncestorType=Window}}"
                                SelectedItemBinding="{Binding CableToFirst}" />

                            <!--  Cable to Remainder  -->
                            <DataGridComboBoxColumn
                                Width="120"
                                Header="Cable to Remainder"
                                ItemsSource="{Binding DataContext.CableTypes, RelativeSource={RelativeSource AncestorType=Window}}"
                                SelectedItemBinding="{Binding CableToRemainder}" />

                            <!--  Installation Method  -->
                            <DataGridComboBoxColumn
                                Width="120"
                                Header="Installation Method"
                                ItemsSource="{Binding DataContext.InstallationMethods, RelativeSource={RelativeSource AncestorType=Window}}"
                                SelectedItemBinding="{Binding InstallationMethod}" />

                            <!--  Derating Factor  -->
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding DeratingFactor, StringFormat=N2}"
                                Header="Derating Factor" />

                            <!--  Diversity  -->
                            <DataGridTextColumn
                                Width="80"
                                Binding="{Binding Diversity, StringFormat=N2}"
                                Header="Diversity" />

                            <!--  Manual Override  -->
                            <DataGridCheckBoxColumn
                                Width="60"
                                Binding="{Binding IsManual}"
                                Header="Manual" />

                            <!--  Path Mode  -->
                            <DataGridComboBoxColumn
                                Width="100"
                                Header="Path Mode"
                                ItemsSource="{Binding DataContext.PathModes, RelativeSource={RelativeSource AncestorType=Window}}"
                                SelectedItemBinding="{Binding PathMode}" />

                            <!--  Set Path Button  -->
                            <DataGridTemplateColumn Width="80" Header="Set Path">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button
                                            Padding="4,2"
                                            Command="{Binding DataContext.SetPathCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                            CommandParameter="{Binding}"
                                            Content="Set Path"
                                            FontSize="10"
                                            IsEnabled="{Binding CanEditPath}"
                                            Style="{StaticResource MaterialDesignFlatButton}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Length to First  -->
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding LengthToFirst, StringFormat=N2}"
                                Header="Length to First" />

                            <!--  Length to Final  -->
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding LengthToFinal, StringFormat=N2}"
                                Header="Total Length" />

                            <!--  Manual Current Checkbox  -->
                            <DataGridCheckBoxColumn
                                Width="100"
                                Binding="{Binding ManualCurrent}"
                                Header="Manual Current" />

                            <!--  Current Value  -->
                            <DataGridTextColumn
                                Width="80"
                                Binding="{Binding Current, StringFormat=N2}"
                                Header="Current (A)"
                                IsReadOnly="True" />

                            <!--  Number of Elements Button  -->
                            <DataGridTemplateColumn Width="80" Header="Elements">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button
                                            Padding="4,2"
                                            Content="{Binding NumberOfElements}"
                                            FontSize="10"
                                            Style="{StaticResource MaterialDesignFlatButton}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!--  Validation Results Columns  -->
                            <DataGridTextColumn
                                Width="120"
                                Binding="{Binding CheckTripRating}"
                                Header="Trip Rating Check"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding Cable1Valid}"
                                Header="Cable 1 Valid"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding Cable2Valid}"
                                Header="Cable 2 Valid"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="120"
                                Binding="{Binding CheckCpdDiscriminates}"
                                Header="CPD Discriminates"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="120"
                                Binding="{Binding CheckLoadCurrent}"
                                Header="Load Current Check"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding CheckCable1Current}"
                                Header="Cable 1 Current"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding CheckCable2Current}"
                                Header="Cable 2 Current"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding CheckCable1VoltageDropPercent}"
                                Header="Cable 1 VD%"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding CheckCable2VoltageDropPercent}"
                                Header="Cable 2 VD%"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding CheckCable1ScWithstand}"
                                Header="Cable 1 SC"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding CheckCable2ScWithstand}"
                                Header="Cable 2 SC"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="150"
                                Binding="{Binding CircuitCheckSummary}"
                                Header="Check Summary"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding CircuitCheckResult}"
                                CellStyle="{StaticResource ValidationCellStyle}"
                                Header="Check Result"
                                IsReadOnly="True" />

                            <DataGridTextColumn
                                Width="80"
                                Binding="{Binding CircuitRevision}"
                                Header="Revision" />

                            <DataGridCheckBoxColumn
                                Width="80"
                                Binding="{Binding IsSpareOrSpace}"
                                Header="Spare/Space" />
                        </DataGrid.Columns>
                    </DataGrid>
                </materialDesign:Card>
            </Grid>

            <!--  Phase Loading Summary  -->
            <Grid Grid.Row="2" Margin="16,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="20" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <!--  Diversified Phase Loading  -->
                <materialDesign:Card Grid.Column="0">
                    <StackPanel Margin="16">
                        <TextBlock
                            Margin="0,0,0,8"
                            Padding="8,4"
                            HorizontalAlignment="Center"
                            Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                            Text="Diversified Phase Loading (Run PowerBIM)" />
                        <DataGrid
                            AutoGenerateColumns="False"
                            Background="Transparent"
                            GridLinesVisibility="None"
                            HeadersVisibility="Column"
                            IsReadOnly="True"
                            ItemsSource="{Binding DiversifiedPhaseData}">
                            <DataGrid.Columns>
                                <DataGridTextColumn
                                    Width="*"
                                    Binding="{Binding PhaseR, StringFormat=N2}"
                                    Header="Phase R" />
                                <DataGridTextColumn
                                    Width="*"
                                    Binding="{Binding PhaseW, StringFormat=N2}"
                                    Header="Phase W" />
                                <DataGridTextColumn
                                    Width="*"
                                    Binding="{Binding PhaseB, StringFormat=N2}"
                                    Header="Phase B" />
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>

                <!--  Un-Diversified Phase Loading  -->
                <materialDesign:Card Grid.Column="2">
                    <StackPanel Margin="16">
                        <TextBlock
                            Margin="0,0,0,8"
                            Padding="8,4"
                            HorizontalAlignment="Center"
                            Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                            Text="Un-Diversified Phase Loading" />
                        <DataGrid
                            AutoGenerateColumns="False"
                            Background="Transparent"
                            GridLinesVisibility="None"
                            HeadersVisibility="Column"
                            IsReadOnly="True"
                            ItemsSource="{Binding UnDiversifiedPhaseData}">
                            <DataGrid.Columns>
                                <DataGridTextColumn
                                    Width="*"
                                    Binding="{Binding RevitPhaseR, StringFormat=N2}"
                                    Header="Revit Phase R" />
                                <DataGridTextColumn
                                    Width="*"
                                    Binding="{Binding RevitPhaseW, StringFormat=N2}"
                                    Header="Revit Phase W" />
                                <DataGridTextColumn
                                    Width="*"
                                    Binding="{Binding RevitPhaseB, StringFormat=N2}"
                                    Header="Revit Phase B" />
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!--  Action Buttons & Status  -->
            <Grid Grid.Row="3" Background="{StaticResource MaterialDesignPaper}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  Tips & Status  -->
                <StackPanel
                    Grid.Column="0"
                    Margin="16,8"
                    VerticalAlignment="Center">
                    <TextBlock
                        Foreground="{StaticResource MaterialDesignBodyLight}"
                        Style="{StaticResource MaterialDesignCaptionTextBlock}"
                        Text="{Binding TipsText}"
                        TextWrapping="Wrap" />
                    <TextBlock
                        Margin="0,4,0,0"
                        Foreground="{StaticResource PrimaryHueMidBrush}"
                        Style="{StaticResource MaterialDesignCaptionTextBlock}"
                        Text="{Binding StatusMessage}"
                        Visibility="{Binding StatusMessage, Converter={StaticResource BoolToVisConverter}}" />
                </StackPanel>

                <!--  Action Buttons  -->
                <StackPanel
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    Margin="16,8"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        Width="100"
                        Margin="10"
                        Padding="16,8"
                        Background="#12A8B2"
                        BorderBrush="#12A8B2"
                        Command="{Binding CancelCommand}"
                        Content="Cancel"
                        Foreground="White"
                        Style="{StaticResource MaterialDesignOutlinedButton}" />
                    <Button
                        Width="100"
                        Margin="10"
                        Padding="16,8"
                        Background="#FFCE00"
                        BorderBrush="#FFCE00"
                        Command="{Binding SaveCommand}"
                        Content="Save"
                        Style="{StaticResource MaterialDesignRaisedButton}" />
                </StackPanel>
            </Grid>
        </Grid>
    </materialDesign:DialogHost>
</Window>

