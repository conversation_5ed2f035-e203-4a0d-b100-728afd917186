# Testing Cheat Sheet for MEP.PowerBIM_6

## Quick Commands

### Run Tests in Visual Studio
1. **Build Solution**: `Ctrl+Shift+B`
2. **Open Test Explorer**: `Test` menu > `Test Explorer`
3. **Run All Tests**: Click ▶️ button in Test Explorer
4. **Run One Test**: Right-click test > `Run`

### Run Tests with PowerShell
```powershell
# In your test project folder:

# Run all tests
.\RunTests.ps1 -TestCategory All

# Run only unit tests (fast)
.\RunTests.ps1 -TestCategory Unit

# Run with code coverage
.\RunTests.ps1 -TestCategory Unit -Coverage

# Run specific category
.\RunTests.ps1 -TestCategory Services
```

### Run Tests with Command Line
```bash
# Simple run
dotnet test

# Run specific category
dotnet test --filter "TestCategory=Unit"

# Run with detailed output
dotnet test --verbosity detailed
```

## Test Template

### Basic Test Structure
```csharp
using FluentAssertions;
using Xunit;

public class MyClassTests
{
    [Fact]
    public void MethodName_Scenario_ExpectedResult()
    {
        // Arrange: Set up test data
        var input = "test data";

        // Act: Call the method you're testing
        var result = MyClass.MethodName(input);

        // Assert: Check if it worked
        result.Should().Be("expected result");
    }
}
```

### Test with Setup and Cleanup
```csharp
public class MyClassTests : IDisposable
{
    private readonly MyClass _myClass;

    public MyClassTests()  // Constructor runs before each test
    {
        _myClass = new MyClass();
    }

    public void Dispose()  // Runs after each test
    {
        _myClass?.Dispose();
    }

    [Fact]
    public void MyTest()
    {
        // Your test here
    }
}
```

## Common Assertions (FluentAssertions)

### Basic Checks
```csharp
// Check if equal
result.Should().Be(expectedValue);

// Check if not null
result.Should().NotBeNull();

// Check if null
result.Should().BeNull();

// Check if true/false
result.Should().BeTrue();
result.Should().BeFalse();
```

### String Checks
```csharp
text.Should().Contain("substring");
text.Should().StartWith("prefix");
text.Should().EndWith("suffix");
text.Should().BeEmpty();
text.Should().NotBeNullOrEmpty();
```

### Number Checks
```csharp
number.Should().BeGreaterThan(5);
number.Should().BeLessThan(10);
number.Should().BeInRange(1, 100);
number.Should().BeApproximately(3.14, 0.01); // For decimals
```

### Collection Checks
```csharp
list.Should().HaveCount(3);
list.Should().Contain(item);
list.Should().NotContain(item);
list.Should().BeEmpty();
list.Should().NotBeEmpty();
```

### Exception Checks
```csharp
Action act = () => MyMethod();
act.Should().Throw<ArgumentException>();
act.Should().NotThrow();
```

## Test Categories

### Add Categories to Tests
```csharp
[Fact]
[Trait("Category", "Unit")]
[Trait("Category", "Services")]
public void MyTest()
{
    // Test code
}
```

### Available Categories
- `Unit` - Test individual methods
- `Integration` - Test how parts work together
- `Regression` - Test that old bugs don't come back
- `Performance` - Test speed and memory
- `Services` - Test service classes
- `ViewModels` - Test ViewModel classes
- `Calculations` - Test electrical calculations

### Run Tests by Category
```bash
# Run unit tests only
dotnet test --filter "Category=Unit"

# Run multiple categories
dotnet test --filter "Category=Unit|Category=Integration"

# Run specific category
dotnet test --filter "Category=Services"
```

## Creating Test Data

### Using TestDataFactory
```csharp
// Create test project info
var projectInfo = TestDataFactory.CreateTestProjectInfo("My Project");

// Create test distribution board
var db = TestDataFactory.CreateTestDistributionBoard("DB-1", 5);

// Create multiple distribution boards
var dbs = TestDataFactory.CreateTestDistributionBoards(3);

// Create test circuits
var circuits = TestDataFactory.CreateTestCircuits(10);
```

### Creating Custom Test Data
```csharp
var testProject = new PowerBIM_ProjectInfo
{
    JobName = "Test Project",
    Engineer = "Test Engineer",
    SystemVoltageDropMax = 5.0
};
```

## Mocking (Creating Fake Objects)

### Basic Mock Setup
```csharp
// Create a fake service
var mockService = new Mock<IDataService>();

// Make it return specific values
mockService.Setup(x => x.LoadSettings())
           .Returns(new SettingsModel());

// Use the fake service
var fakeService = mockService.Object;
```

### Verify Mock Was Called
```csharp
// Check if method was called
mockService.Verify(x => x.SaveSettings(It.IsAny<SettingsModel>()), 
                   Times.Once);

// Check if method was never called
mockService.Verify(x => x.DeleteSettings(), Times.Never);
```

## Debugging Tests

### Debug a Failing Test
1. **Right-click the test** in Test Explorer
2. **Click "Debug"**
3. **Set breakpoints** in your test or code
4. **Step through** to see what's happening

### Common Debugging Tips
```csharp
[TestMethod]
public void MyTest()
{
    // Add this to see values in output
    Console.WriteLine($"Value is: {myValue}");
    
    // Or use TestContext
    TestContext.WriteLine($"Debug info: {myValue}");
}
```

## Test Naming Conventions

### Good Test Names
```csharp
// Pattern: MethodName_Scenario_ExpectedBehavior
[Fact]
public void CalculateVoltDrop_HighLoad_ReturnsAccurateResult() { }

[Fact]
public void ValidateProjectInfo_EmptyJobName_ReturnsValidationError() { }

[Fact]
public void ConvertToModel_ValidData_ReturnsCorrectModel() { }
```

### Bad Test Names
```csharp
Test1()
TestMethod()
CheckStuff()
```

## File Organization

### Recommended Structure
```
MEP.PowerBIM_6.Tests/
├── Unit/
│   ├── Services/
│   │   ├── DataServiceTests.cs
│   │   └── NavigationServiceTests.cs
│   ├── ViewModels/
│   │   ├── MainViewModelTests.cs
│   │   └── BaseViewModelTests.cs
│   └── Models/
│       └── ProjectInfoModelTests.cs
├── Integration/
│   └── ServiceConfigurationTests.cs
└── Regression/
    └── CalculationRegressionTests.cs
```

## Troubleshooting

### Tests Won't Run
1. **Build the solution** first
2. **Check NuGet packages** are installed
3. **Check project references** are correct
4. **Clean and rebuild** solution

### Tests Are Failing
1. **Read the error message** carefully
2. **Check your assertions** - are you expecting the right value?
3. **Debug the test** to see what's actually happening
4. **Check if your code changed** and broke something

### Tests Are Slow
1. **Run only unit tests** during development
2. **Use mocks** instead of real services
3. **Avoid file I/O** in unit tests
4. **Keep tests simple** and focused

## Best Practices

### Do This ✅
- Write descriptive test names
- Test one thing per test
- Use the AAA pattern (Arrange, Act, Assert)
- Keep tests simple and readable
- Run tests frequently

### Don't Do This ❌
- Test multiple things in one test
- Write tests that depend on other tests
- Test private methods directly
- Write tests that require manual setup
- Ignore failing tests

## Quick Reference Commands

### Visual Studio Shortcuts
- `Ctrl+R, A` - Run all tests
- `Ctrl+R, T` - Run tests in current context
- `Ctrl+R, L` - Run last test run

### Test Explorer Actions
- **Green checkmark** ✅ = Test passed
- **Red X** ❌ = Test failed  
- **Yellow triangle** ⚠️ = Test skipped
- **Blue circle** 🔵 = Test running

### PowerShell Quick Commands
```powershell
# Development workflow
.\RunTests.ps1 -TestCategory Unit -Verbose

# Before commit
.\RunTests.ps1 -TestCategory "Unit|Integration"

# Before release
.\RunTests.ps1 -TestCategory All -Coverage
```

## Getting Help

### When Stuck
1. **Read the error message** - it usually tells you what's wrong
2. **Check the beginner guide** for detailed explanations
3. **Start with simpler tests** and work up to complex ones
4. **Ask for help** - testing is a learnable skill!

### Resources
- **Beginner Guide**: `Testing_Beginner_Guide.md`
- **Quick Tutorial**: `Testing_Quick_Start_Tutorial.md`
- **Test Examples**: Look at the test files I created

Remember: **Any testing is better than no testing!** Start simple and improve over time.
