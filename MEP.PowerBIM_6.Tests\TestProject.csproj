<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>true</UseWPF>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MEP.PowerBIM_6\MEP.PowerBIM_6.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Unit\Services\" />
    <Folder Include="Unit\ViewModels\" />
    <Folder Include="Unit\Models\" />
    <Folder Include="Unit\Converters\" />
    <Folder Include="Integration\DependencyInjection\" />
    <Folder Include="Integration\Navigation\" />
    <Folder Include="Integration\DataFlow\" />
    <Folder Include="Regression\Calculations\" />
    <Folder Include="Regression\Export\" />
    <Folder Include="Regression\Workflows\" />
    <Folder Include="TestData\SampleProjects\" />
    <Folder Include="TestData\TestDocuments\" />
    <Folder Include="TestData\ExpectedResults\" />
    <Folder Include="Helpers\MockServices\" />
    <Folder Include="Helpers\TestFixtures\" />
    <Folder Include="Helpers\Utilities\" />
  </ItemGroup>

</Project>
