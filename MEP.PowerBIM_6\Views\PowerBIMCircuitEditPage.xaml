﻿<Page
    x:Class="MEP.PowerBIM_6.Views.PowerBIMCircuitEditPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Circuit Editor"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />
            <converters:NumericFormatConverter x:Key="NumericFormatConverter" />
            <converters:PercentageConverter x:Key="PercentageConverter" />
        </ResourceDictionary>

    </Page.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <StackPanel
            Grid.Row="0"
            Margin="0,0,0,16"
            Orientation="Horizontal">
            <materialDesign:PackIcon
                Width="24"
                Height="24"
                Margin="0,0,8,0"
                VerticalAlignment="Center"
                Kind="ElectricSwitch" />
            <TextBlock
                VerticalAlignment="Center"
                Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                Text="Enhanced Circuit Editor" />
        </StackPanel>

        <!--  Action Bar  -->
        <materialDesign:Card
            Grid.Row="1"
            Margin="0,0,0,16"
            Padding="16">
            <StackPanel Orientation="Horizontal">
                <Button
                    Margin="0,0,8,0"
                    Content="Calculate All"
                    ToolTip="Run calculations for all circuits" />
                <Button
                    Margin="0,0,8,0"
                    Content="Save Changes"
                    ToolTip="Save circuit modifications" />
                <Button
                    Margin="0,0,8,0"
                    Content="Reset Values"
                    ToolTip="Reset to original values" />
                <Separator Margin="8,0" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />
                <Button
                    Margin="0,0,8,0"
                    Content="Export to Excel"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    ToolTip="Export circuit data to Excel" />
                <Button
                    Margin="0,0,8,0"
                    Content="Import from Excel"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    ToolTip="Import circuit data from Excel" />
            </StackPanel>
        </materialDesign:Card>

        <!--  Circuit DataGrid  -->
        <materialDesign:Card Grid.Row="2" Padding="8">
            <DataGrid
                x:Name="dgCircuits"
                AlternatingRowBackground="{DynamicResource MaterialDesignDivider}"
                AutoGenerateColumns="False"
                CanUserAddRows="False"
                CanUserDeleteRows="False"
                GridLinesVisibility="All"
                HeadersVisibility="Column"
                RowBackground="Transparent"
                SelectionMode="Extended">

                <DataGrid.Columns>
                    <!--  Circuit Number Column  -->
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding CCT_Number}"
                        Header="Circuit"
                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}" TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="SemiBold" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!--  Description Column  -->
                    <DataGridTextColumn
                        Width="200"
                        Binding="{Binding Schedule_Description}"
                        Header="Description"
                        IsReadOnly="True" />

                    <!--  Current Column (Editable)  -->
                    <DataGridTemplateColumn Width="100" Header="Current (A)">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock
                                        VerticalAlignment="Center"
                                        Text="{Binding EffectiveCurrent, StringFormat=F1}"
                                        Visibility="{Binding ManualCurrent, Converter={StaticResource BoolToVisConverter}, ConverterParameter=True}" />
                                    <TextBox
                                        Width="60"
                                        Text="{Binding Manual_PowerBim_User_Current, StringFormat=F1}"
                                        Visibility="{Binding ManualCurrent, Converter={StaticResource BoolToVisConverter}}" />
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!--  Manual Current Toggle  -->
                    <DataGridCheckBoxColumn
                        Width="60"
                        Binding="{Binding ManualCurrent}"
                        ElementStyle="{StaticResource MaterialDesignDataGridCheckBoxColumnStyle}"
                        Header="Manual" />

                    <!--  Poles Column  -->
                    <DataGridTextColumn
                        Width="60"
                        Binding="{Binding Number_Of_Poles}"
                        Header="Poles" />

                    <!--  Elements Column  -->
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding Number_Of_Elements}"
                        Header="Elements" />

                    <!--  Cable To First Column  -->
                    <DataGridTextColumn
                        Width="120"
                        Binding="{Binding Schedule_Cable_To_First}"
                        Header="Cable to First" />

                    <!--  Cable To Final Column  -->
                    <DataGridTextColumn
                        Width="120"
                        Binding="{Binding Schedule_Cable_To_Final}"
                        Header="Cable to Final" />

                    <!--  Protection Device Column  -->
                    <DataGridTextColumn
                        Width="100"
                        Binding="{Binding Schedule_Protective_Device}"
                        Header="Protection" />

                    <!--  Trip Rating Column  -->
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding Schedule_Trip_Rating}"
                        Header="Trip (A)" />

                    <!--  Circuit Type Column  -->
                    <DataGridTextColumn
                        Width="80"
                        Binding="{Binding CircuitTypeDescription}"
                        Header="Type"
                        IsReadOnly="True" />

                    <!--  Status Column  -->
                    <DataGridTemplateColumn Width="80" Header="Status">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border
                                    Padding="6,2"
                                    HorizontalAlignment="Center"
                                    CornerRadius="12">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding CheckResult}" Value="Pass">
                                                    <Setter Property="Background" Value="LightGreen" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding CheckResult}" Value="Warning">
                                                    <Setter Property="Background" Value="LightGoldenrodYellow" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding CheckResult}" Value="Fail">
                                                    <Setter Property="Background" Value="LightCoral" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        FontSize="10"
                                        FontWeight="SemiBold"
                                        Text="{Binding CheckResult}" />
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!--  Error Message Column  -->
                    <DataGridTemplateColumn Width="150" Header="Notes">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button
                                    Padding="4,2"
                                    Content="View"
                                    FontSize="10"
                                    Style="{StaticResource MaterialDesignFlatButton}"
                                    ToolTip="{Binding ErrorMessage}"
                                    Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>

                <!--  Row Style for status indication  -->
                <DataGrid.RowStyle>
                    <Style BasedOn="{StaticResource MaterialDesignDataGridRow}" TargetType="DataGridRow">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding CheckResult}" Value="Fail">
                                <Setter Property="BorderBrush" Value="Red" />
                                <Setter Property="BorderThickness" Value="2,0,0,0" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding CheckResult}" Value="Warning">
                                <Setter Property="BorderBrush" Value="Orange" />
                                <Setter Property="BorderThickness" Value="2,0,0,0" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding CheckResult}" Value="Pass">
                                <Setter Property="BorderBrush" Value="Green" />
                                <Setter Property="BorderThickness" Value="2,0,0,0" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding CCT_Is_Spare_Or_Space}" Value="True">
                                <Setter Property="Foreground" Value="Gray" />
                                <Setter Property="FontStyle" Value="Italic" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</Page>
