using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Services;
using MEP.PowerBIM_6.ViewModels;
using MEP.PowerBIM_6.Views;
using Microsoft.Extensions.DependencyInjection;
using MessageBox = System.Windows.MessageBox;

namespace MEP.PowerBIM_6.Handlers
{
    /// <summary>
    /// Enhanced modeless window handler for PowerBIM 6 WPF application
    /// Manages the lifecycle of the main modeless window and provides cross-form communication
    /// Maintains the proven ExternalEvent architecture while adding modern dependency injection
    /// </summary>
    public static class ModelessMainWindowHandler
    {
        #region Fields

        private static MainWindowEnhanced _mainWindow;
        private static RequestHandler_PB6 _requestHandler;
        private static ExternalEvent _externalEvent;
        private static IServiceProvider _serviceProvider;

        // Static references for cross-form communication (preserving original pattern)
        public static PowerBIM_ProjectInfo ProjectInfo { get; set; }
        public static List<PowerBIM_DBData> AllDistributionBoards { get; set; }
        public static BecaActivityLoggerData ActivityLogger { get; private set; }

        #endregion

        #region Properties

        /// <summary>
        /// Indicates if the main window is currently open and active
        /// </summary>
        public static bool IsWindowOpen => _mainWindow != null && _mainWindow.IsLoaded;

        /// <summary>
        /// Get the current service provider for dependency injection
        /// </summary>
        public static IServiceProvider ServiceProvider => _serviceProvider;

        #endregion

        #region Public Methods

        /// <summary>
        /// Show the main PowerBIM window (singleton pattern)
        /// This is the main entry point called from PowerBIM_6_Command
        /// </summary>
        /// <param name="dbs">List of distribution boards from PowerBIM core logic</param>
        /// <param name="projInfo">Project information from PowerBIM core logic</param>
        /// <param name="logger">Activity logger for tracking operations</param>
        public static void ShowWindow(List<PowerBIM_DBData> dbs, PowerBIM_ProjectInfo projInfo, BecaActivityLoggerData logger)
        {
            try
            {
                // Store references for cross-form communication
                AllDistributionBoards = dbs ?? throw new ArgumentNullException(nameof(dbs));
                ProjectInfo = projInfo ?? throw new ArgumentNullException(nameof(projInfo));
                ActivityLogger = logger ?? throw new ArgumentNullException(nameof(logger));

                // Singleton pattern - only one main window allowed
                if (_mainWindow == null || !_mainWindow.IsLoaded)
                {
                    InitializeServices();
                    CreateModelessArchitecture();
                    CreateMainWindow();
                }
                else
                {
                    // Bring existing window to front
                    _mainWindow.Activate();
                    _mainWindow.WindowState = WindowState.Normal;
                    ActivityLogger?.Log("PowerBIM main window activated", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                ActivityLogger?.Log($"Failed to show PowerBIM main window: {ex.Message}", LogType.Error);
                ShowErrorMessage("Failed to open PowerBIM", ex.Message);
            }
        }

        /// <summary>
        /// Close the main window and clean up resources
        /// </summary>
        public static void CloseWindow()
        {
            try
            {
                ActivityLogger?.Log("Closing PowerBIM main window", LogType.Information);

                // Close and dispose main window
                _mainWindow?.Close();
                _mainWindow = null;

                // Dispose ExternalEvent
                _externalEvent?.Dispose();
                _externalEvent = null;

                // Clear references
                _requestHandler = null;
                _serviceProvider = null;
                ProjectInfo = null;
                AllDistributionBoards = null;
                ActivityLogger = null;

                // Note: ActivityLogger is set to null above, so no final log message
            }
            catch (Exception ex)
            {
                ActivityLogger?.Log($"Error closing PowerBIM main window: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Show distribution board edit window as modal dialog
        /// Preserves the original form-to-form communication pattern
        /// </summary>
        /// <param name="distributionBoard">Distribution board to edit</param>
        public static void ShowDbEditWindow(object distributionBoard)
        {
            try
            {
                if (_serviceProvider == null)
                {
                    ActivityLogger?.Log("Service provider not initialized", LogType.Information);
                    return;
                }

                var dbEditViewModel = _serviceProvider.GetRequiredService<DbEditViewModel>();
                dbEditViewModel.Initialize(_requestHandler, _externalEvent);
                // TODO: Initialize with distribution board data

                var dbEditWindow = new DbEditWindow 
                { 
                    DataContext = dbEditViewModel,
                    Owner = _mainWindow
                };

                ActivityLogger?.Log("Showing distribution board edit window", LogType.Information);
                dbEditWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                ActivityLogger?.Log($"Failed to show distribution board edit window: {ex.Message}", LogType.Error);
                ShowErrorMessage("Error", "Failed to open distribution board editor");
            }
        }

        /// <summary>
        /// Show circuit edit window as modal dialog
        /// Preserves the original form-to-form communication pattern
        /// </summary>
        /// <param name="distributionBoard">Distribution board containing circuits to edit</param>
        public static void ShowCircuitEditWindow(object distributionBoard)
        {
            try
            {
                if (_serviceProvider == null)
                {
                    ActivityLogger?.Log("Service provider not initialized", LogType.Information);
                    return;
                }

                if (distributionBoard is not PowerBIM_DBData distributionBoardData)
                {
                    ActivityLogger?.Log("Invalid distribution board type for circuit edit window", LogType.Information);
                    ShowErrorMessage("Error", "Invalid distribution board data");
                    return;
                }

                if (ProjectInfo == null)
                {
                    ActivityLogger?.Log("Project info not available for circuit edit window", LogType.Information);
                    ShowErrorMessage("Error", "Project information not available");
                    return;
                }

                // Check if DB is locked
                if (distributionBoardData.IsManuallyLocked)
                {
                    ShowInfoMessage("Distribution Board Locked",
                        $"Distribution Board '{distributionBoardData.Schedule_DB_Name}' is currently locked and cannot be edited.");
                    return;
                }

                var circuitEditWindow = new CircuitEditWindow
                {
                    Owner = _mainWindow,
                    WindowState = WindowState.Maximized
                };

                // Initialize the window with proper data
                circuitEditWindow.Initialize(_serviceProvider, distributionBoardData, ProjectInfo);

                ActivityLogger?.Log($"Showing circuit edit window for DB: {distributionBoardData.Schedule_DB_Name}", LogType.Information);
                circuitEditWindow.ShowDialog();

                // After closing, update the main window data (similar to original WinForms behavior)
                RefreshMainWindowData();
            }
            catch (Exception ex)
            {
                ActivityLogger?.Log($"Failed to show circuit edit window: {ex.Message}", LogType.Error);
                ShowErrorMessage("Error", $"Failed to open circuit editor: {ex.Message}");
            }
        }

        /// <summary>
        /// Show advanced settings window
        /// </summary>
        public static void ShowAdvancedSettingsWindow()
        {
            try
            {
                if (_serviceProvider == null) return;

                var settingsViewModel = _serviceProvider.GetRequiredService<AdvancedSettingsViewModel>();
                settingsViewModel.Initialize(_requestHandler, _externalEvent);

                var settingsWindow = new AdvancedSettingsWindow 
                { 
                    DataContext = settingsViewModel,
                    Owner = _mainWindow
                };

                ActivityLogger?.Log("Showing advanced settings window", LogType.Information);
                settingsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                ActivityLogger?.Log($"Failed to show advanced settings window: {ex.Message}", LogType.Error);
                ShowErrorMessage("Error", "Failed to open settings");
            }
        }

        /// <summary>
        /// Show export window
        /// </summary>
        public static void ShowExportWindow()
        {
            try
            {
                if (_serviceProvider == null) return;

                var exportViewModel = _serviceProvider.GetRequiredService<ExportViewModel>();
                exportViewModel.Initialize(_requestHandler, _externalEvent);

                var exportWindow = new ExportWindow 
                { 
                    DataContext = exportViewModel,
                    Owner = _mainWindow
                };

                ActivityLogger?.Log("Showing export window", LogType.Information);
                exportWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                ActivityLogger?.Log($"Failed to show export window: {ex.Message}", LogType.Error);
                ShowErrorMessage("Error", "Failed to open export dialog");
            }
        }

        /// <summary>
        /// Wake up the main window after ExternalEvent processing
        /// Called by RequestHandler_PB6 to update UI state
        /// </summary>
        public static void WakeUpMainWindow()
        {
            try
            {
                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    if (_mainWindow?.DataContext is MainViewModel mainViewModel)
                    {
                        mainViewModel.WakeUp();
                        ActivityLogger?.Log("Main window woken up", LogType.Information);
                    }
                });
            }
            catch (Exception ex)
            {
                ActivityLogger?.Log($"Failed to wake up main window: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Initialize the dependency injection services
        /// </summary>
        private static void InitializeServices()
        {
            try
            {
                _serviceProvider = ServiceConfiguration.BuildServiceProvider(ProjectInfo.UIDocument, ActivityLogger);
                ActivityLogger?.Log("Services initialized successfully", LogType.Information);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to initialize services", ex);
            }
        }

        /// <summary>
        /// Create the modeless architecture components
        /// </summary>
        private static void CreateModelessArchitecture()
        {
            try
            {
                _requestHandler = new RequestHandler_PB6(_serviceProvider, ActivityLogger);
                _externalEvent = ExternalEvent.Create(_requestHandler);
                ActivityLogger?.Log("Modeless architecture created successfully", LogType.Information);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to create modeless architecture", ex);
            }
        }

        /// <summary>
        /// Create and show the main window
        /// </summary>
        private static void CreateMainWindow()
        {
            try
            {
                var mainViewModel = _serviceProvider.GetRequiredService<MainViewModel>();
                mainViewModel.InitializeWithData(AllDistributionBoards, ProjectInfo, _requestHandler, _externalEvent);

                _mainWindow = new MainWindowEnhanced
                {
                    DataContext = mainViewModel,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen
                };

                // Handle window closing
                _mainWindow.Closed += OnMainWindowClosed;

                _mainWindow.Show();
                ActivityLogger?.Log("Main window created and shown successfully", LogType.Information);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to create main window", ex);
            }
        }

        /// <summary>
        /// Handle main window closed event
        /// </summary>
        private static void OnMainWindowClosed(object sender, EventArgs e)
        {
            CloseWindow();
        }

        /// <summary>
        /// Show error message to user
        /// </summary>
        private static void ShowErrorMessage(string title, string message)
        {
            try
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch
            {
                // Fallback if MessageBox fails
                System.Diagnostics.Debug.WriteLine($"Error: {title} - {message}");
            }
        }

        /// <summary>
        /// Show info message to user
        /// </summary>
        /// <param name="title">Message title</param>
        /// <param name="message">Message content</param>
        private static void ShowInfoMessage(string title, string message)
        {
            try
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch
            {
                // Fallback if MessageBox fails
                System.Diagnostics.Debug.WriteLine($"Info: {title} - {message}");
            }
        }

        /// <summary>
        /// Refresh main window data after circuit editing
        /// Mimics the behavior from original WinForms implementation
        /// </summary>
        private static void RefreshMainWindowData()
        {
            try
            {
                if (_mainWindow?.DataContext is MainViewModel mainViewModel)
                {
                    // Refresh distribution board data
                    mainViewModel.RefreshDistributionBoards();
                    ActivityLogger?.Log("Main window data refreshed after circuit edit", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                ActivityLogger?.Log($"Failed to refresh main window data: {ex.Message}", LogType.Error);
            }
        }

        #endregion
    }
}

// Note: RequestHandler_PB6 will be implemented in a separate file
