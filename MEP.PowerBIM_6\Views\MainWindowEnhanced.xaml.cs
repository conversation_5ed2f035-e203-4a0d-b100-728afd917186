using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using MEP.PowerBIM_6.Services;
using MEP.PowerBIM_6.Services.Interfaces;
using MEP.PowerBIM_6.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Brushes = System.Windows.Media.Brushes;
using Button = System.Windows.Controls.Button;
using Color = System.Windows.Media.Color;
using ColorConverter = System.Windows.Media.ColorConverter;
using ListBox = System.Windows.Controls.ListBox;
using MessageBox = System.Windows.MessageBox;

namespace MEP.PowerBIM_6.Views
{
    /// <summary>
    /// Interaction logic for MainWindowEnhanced.xaml
    /// Enhanced main window with page-based navigation
    /// </summary>
    public partial class MainWindowEnhanced : Window
    {
        #region Fields

        private INavigationService _navigationService;
        private IServiceProvider _serviceProvider;
        private bool _isUpdatingSelection = false; // Flag to prevent circular calls
        private bool _isSidebarCollapsed = false; // Track sidebar state

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the enhanced main window
        /// </summary>
        public MainWindowEnhanced()
        {
            InitializeComponent();
            Loaded += OnWindowLoaded;
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle window loaded event
        /// </summary>
        private void OnWindowLoaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Get services from DataContext (MainViewModel should have ServiceProvider)
                if (DataContext is MainViewModel mainViewModel)
                {
                    _serviceProvider = mainViewModel.ServiceProvider;
                    _navigationService = _serviceProvider?.GetService<INavigationService>();

                    if (_navigationService != null)
                    {
                        // Initialize navigation service with the main frame
                        _navigationService.Initialize(MainFrame);

                        // Navigate to home page by default
                        _navigationService.NavigateTo(PageKeys.Home);

                        // Subscribe to navigation events
                        _navigationService.Navigated += OnNavigated;
                        _navigationService.NavigationFailed += OnNavigationFailed;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to initialize navigation: {ex.Message}", "Navigation Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Handle window closing event
        /// </summary>
        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // Unsubscribe from navigation events
                if (_navigationService != null)
                {
                    _navigationService.Navigated -= OnNavigated;
                    _navigationService.NavigationFailed -= OnNavigationFailed;
                }

                // Allow the window to close
                // The ModelessMainWindowHandler will handle cleanup
            }
            catch (Exception ex)
            {
                // Log error but don't prevent closing
                System.Diagnostics.Debug.WriteLine($"Error during window closing: {ex.Message}");
            }
        }

        /// <summary>
        /// Navigate to Distribution Boards page
        /// </summary>
        private void NavigateToDistributionBoards(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_navigationService?.NavigateTo(PageKeys.DistributionBoards) == true)
                {
                    UpdateNavigationSelection("navDistributionBoards");
                }
            }
            catch (Exception ex)
            {
                ShowNavigationError($"Failed to navigate to Distribution Boards: {ex.Message}");
            }
        }

        /// <summary>
        /// Navigate to Circuits page
        /// </summary>
        private void NavigateToCircuits(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_navigationService?.NavigateTo(PageKeys.Circuits) == true)
                {
                    UpdateNavigationSelection("navCircuits");
                }
            }
            catch (Exception ex)
            {
                ShowNavigationError($"Failed to navigate to Circuits: {ex.Message}");
            }
        }

        /// <summary>
        /// Navigate to Bulk Operations page
        /// </summary>
        private void NavigateToBulkOperations(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_navigationService?.NavigateTo(PageKeys.BulkOperations) == true)
                {
                    UpdateNavigationSelection("navResults");
                }
            }
            catch (Exception ex)
            {
                ShowNavigationError($"Failed to navigate to Bulk Operations: {ex.Message}");
            }
        }

        /// <summary>
        /// Navigate to home page
        /// </summary>
        private void NavigateToHome(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_navigationService?.NavigateTo(PageKeys.Home) == true)
                {
                    UpdateNavigationSelection("navHome");
                }
            }
            catch (Exception ex)
            {
                ShowNavigationError($"Failed to navigate to home: {ex.Message}");
            }
        }

        /// <summary>
        /// Collapse the sidebar to icon-only view
        /// </summary>
        private void CollapseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _isSidebarCollapsed = true;
                MainSidebar.Visibility = System.Windows.Visibility.Collapsed;
                CollapsedSidebar.Visibility = System.Windows.Visibility.Visible;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error collapsing sidebar: {ex.Message}");
            }
        }

        /// <summary>
        /// Expand the sidebar to full view
        /// </summary>
        private void ExpandButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _isSidebarCollapsed = false;
                CollapsedSidebar.Visibility = System.Windows.Visibility.Collapsed;
                MainSidebar.Visibility = System.Windows.Visibility.Visible;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error expanding sidebar: {ex.Message}");
            }
        }

        /// <summary>
        /// Navigate to settings page
        /// </summary>
        private void NavigateToSettings(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_navigationService?.NavigateTo(PageKeys.ProjectSettings) == true)
                {
                    UpdateNavigationSelection("navSettings");
                }
            }
            catch (Exception ex)
            {
                ShowNavigationError($"Failed to navigate to settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Navigate to about page
        /// </summary>
        private void NavigateToAbout(object sender, RoutedEventArgs e)
        {
            try
            {
                _navigationService?.NavigateTo(PageKeys.About);
                // About page is not in the navigation list, so don't update selection
            }
            catch (Exception ex)
            {
                ShowNavigationError($"Failed to navigate to about: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle successful navigation
        /// </summary>
        private void OnNavigated(object sender, System.Windows.Navigation.NavigationEventArgs e)
        {
            try
            {
                // Navigation completed successfully - no additional UI updates needed
                // to prevent circular calls with UpdateNavigationHighlight
                System.Diagnostics.Debug.WriteLine($"Navigation completed to: {e.Uri}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling navigation event: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle navigation failure
        /// </summary>
        private void OnNavigationFailed(object sender, MEP.PowerBIM_6.Services.NavigationFailedEventArgs e)
        {
            ShowNavigationError($"Navigation failed: {e.Exception?.Message ?? "Unknown error"}");
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Update navigation selection to match current page
        /// </summary>
        /// <param name="selectedItemName">Name of the item to select</param>
        private void UpdateNavigationSelection(string selectedItemName)
        {
            try
            {
                // Set flag to prevent circular calls
                _isUpdatingSelection = true;

                // Update both expanded and collapsed navigation states
                UpdateNavigationButtonStates(selectedItemName);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating navigation selection: {ex.Message}");
            }
            finally
            {
                // Always reset the flag
                _isUpdatingSelection = false;
            }
        }

        /// <summary>
        /// Update the visual states of navigation buttons
        /// </summary>
        /// <param name="selectedItemName">Name of the selected item</param>
        private void UpdateNavigationButtonStates(string selectedItemName)
        {
            try
            {
                // Reset all buttons to default state
                ResetNavigationButtonStates();

                // Set selected state for the appropriate button
                switch (selectedItemName)
                {
                    case "navHome":
                        SetButtonSelected(navHome, navHomeCollapsed);
                        break;
                    case "navDistributionBoards":
                        SetButtonSelected(navDistributionBoards, navDistributionBoardsCollapsed);
                        break;
                    case "navCircuits":
                        SetButtonSelected(navCircuits, navCircuitsCollapsed);
                        break;
                    case "navResults":
                        SetButtonSelected(navResults, navResultsCollapsed);
                        break;
                    case "navSettings":
                        SetButtonSelected(navSettings, navSettingsCollapsed);
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating navigation button states: {ex.Message}");
            }
        }

        /// <summary>
        /// Reset all navigation buttons to default state
        /// </summary>
        private void ResetNavigationButtonStates()
        {
            // Reset expanded buttons
            SetButtonDefault(navHome);
            SetButtonDefault(navDistributionBoards);
            SetButtonDefault(navCircuits);
            SetButtonDefault(navResults);
            SetButtonDefault(navSettings);

            // Reset collapsed buttons
            SetButtonDefault(navHomeCollapsed);
            SetButtonDefault(navDistributionBoardsCollapsed);
            SetButtonDefault(navCircuitsCollapsed);
            SetButtonDefault(navResultsCollapsed);
            SetButtonDefault(navSettingsCollapsed);
        }

        /// <summary>
        /// Set button to selected state
        /// </summary>
        private void SetButtonSelected(Button expandedButton, Button collapsedButton)
        {
            if (expandedButton != null)
            {
                expandedButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F8F9FA"));
                if (expandedButton.Content is StackPanel panel)
                {
                    foreach (var child in panel.Children)
                    {
                        if (child is TextBlock textBlock && textBlock.Margin.Left > 0) // Text label
                        {
                            textBlock.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#0078D4"));
                            textBlock.FontWeight = FontWeights.Medium;
                        }
                    }
                }
            }

            if (collapsedButton != null)
            {
                collapsedButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F8F9FA"));
            }
        }

        /// <summary>
        /// Set button to default state
        /// </summary>
        private void SetButtonDefault(Button button)
        {
            if (button != null)
            {
                button.Background = Brushes.Transparent;
                if (button.Content is StackPanel panel)
                {
                    foreach (var child in panel.Children)
                    {
                        if (child is TextBlock textBlock && textBlock.Margin.Left > 0) // Text label
                        {
                            textBlock.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#605E5C"));
                            textBlock.FontWeight = FontWeights.Normal;
                        }
                    }
                }
                else if (button.Content is TextBlock directTextBlock)
                {
                    directTextBlock.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#605E5C"));
                }
            }
        }

        /// <summary>
        /// Update navigation highlight based on current page
        /// </summary>
        private void UpdateNavigationHighlight()
        {
            try
            {
                if (_navigationService?.CurrentPageKey != null)
                {
                    string itemName = _navigationService.CurrentPageKey switch
                    {
                        PageKeys.Home => "navHome",
                        PageKeys.DistributionBoards => "navDistributionBoards",
                        PageKeys.Circuits => "navCircuits",
                        PageKeys.BulkOperations => "navResults",
                        PageKeys.ProjectSettings => "navSettings",
                        _ => "navHome"
                    };

                    UpdateNavigationSelection(itemName);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating navigation highlight: {ex.Message}");
            }
        }

        /// <summary>
        /// Show navigation error message
        /// </summary>
        /// <param name="message">Error message</param>
        private void ShowNavigationError(string message)
        {
            try
            {
                MessageBox.Show(message, "Navigation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            catch
            {
                // Fallback if MessageBox fails
                System.Diagnostics.Debug.WriteLine($"Navigation Error: {message}");
            }
        }

        #endregion

        /// <summary>
        /// Open Help and Documentation dialog
        /// </summary>
        private void OpenHelpDialog(object sender, RoutedEventArgs e)
        {
            try
            {
                PowerBIMHelpDialog.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open Help dialog: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Open About window
        /// </summary>
        private void OpenAboutWindow(object sender, RoutedEventArgs e)
        {
            try
            {
                var aboutWindow = new PowerBIMAboutWindow()
                {
                    Owner = this
                };
                aboutWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open About window: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
