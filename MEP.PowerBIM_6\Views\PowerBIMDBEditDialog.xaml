﻿<Window
    x:Class="MEP.PowerBIM_6.Views.PowerBIMDBEditDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Distribution Board Settings"
    Width="500"
    Height="600"
    Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}"
    ResizeMode="CanResize"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  Converters  -->
            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:NumericFormatConverter x:Key="NumericFormatConverter" />
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <Border
            Grid.Row="0"
            Padding="16,12"
            Background="{DynamicResource PrimaryHueMidBrush}">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon
                    Width="24"
                    Height="24"
                    Margin="0,0,8,0"
                    VerticalAlignment="Center"
                    Foreground="White"
                    Kind="Settings" />
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="16"
                    FontWeight="SemiBold"
                    Foreground="White"
                    Text="{Binding Schedule_DB_Name, StringFormat='Settings for {0}'}" />
            </StackPanel>
        </Border>

        <!--  Content  -->
        <ScrollViewer
            Grid.Row="1"
            Padding="16"
            VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!--  Basic Information  -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock
                            Margin="0,0,0,16"
                            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                            Text="Basic Information" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="DB Name:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="0,0,0,8"
                                Background="{DynamicResource MaterialDesignDivider}"
                                IsReadOnly="True"
                                Text="{Binding Schedule_DB_Name}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="Circuit Naming:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0,0,0,8"
                                Text="{Binding CircuitNaming}" />

                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="0"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="Number of Ways:" />
                            <TextBox
                                Grid.Row="2"
                                Grid.Column="1"
                                Margin="0,0,0,8"
                                Text="{Binding NumberOfWays}" />

                            <CheckBox
                                Grid.Row="3"
                                Grid.Column="0"
                                Grid.ColumnSpan="2"
                                Content="Manually Lock this Distribution Board"
                                IsChecked="{Binding IsManuallyLocked}" />
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!--  Electrical Parameters  -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock
                            Margin="0,0,0,16"
                            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                            Text="Electrical Parameters" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="Upstream Device Rating (A):" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="0,0,16,8"
                                Text="{Binding UpstreamDeviceRating, Converter={StaticResource NumericFormatConverter}}" />

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="2"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="Device kA Rating:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="3"
                                Margin="0,0,0,8"
                                Text="{Binding DeviceKARating, Converter={StaticResource NumericFormatConverter}}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="EFLI R (Ω):" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0,0,16,8"
                                Text="{Binding EFLI_R, Converter={StaticResource NumericFormatConverter}}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="2"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="EFLI X (Ω):" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="3"
                                Margin="0,0,0,8"
                                Text="{Binding EFLI_X, Converter={StaticResource NumericFormatConverter}}" />

                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="0"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="DB Voltage Drop (%):" />
                            <TextBox
                                Grid.Row="2"
                                Grid.Column="1"
                                Margin="0,0,16,8"
                                Text="{Binding DBVD, Converter={StaticResource NumericFormatConverter}}" />

                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="2"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="PSCC (kA):" />
                            <TextBox
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="0,0,0,8"
                                Text="{Binding PSCC, Converter={StaticResource NumericFormatConverter}}" />
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!--  Status Information  -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock
                            Margin="0,0,0,16"
                            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                            Text="Status Information" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  Pass Count  -->
                            <Border
                                Grid.Column="0"
                                Margin="0,0,8,0"
                                Padding="12"
                                Background="LightGreen"
                                CornerRadius="4">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        Foreground="Green"
                                        Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                        Text="{Binding Result_PassCount}" />
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        Text="Passing Circuits"
                                        TextAlignment="Center" />
                                </StackPanel>
                            </Border>

                            <!--  Warning Count  -->
                            <Border
                                Grid.Column="1"
                                Margin="0,0,8,0"
                                Padding="12"
                                Background="LightGoldenrodYellow"
                                CornerRadius="4">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        Foreground="Orange"
                                        Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                        Text="{Binding Result_WarningCount}" />
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        Text="Warning Circuits"
                                        TextAlignment="Center" />
                                </StackPanel>
                            </Border>

                            <!--  Fail Count  -->
                            <Border
                                Grid.Column="2"
                                Padding="12"
                                Background="LightCoral"
                                CornerRadius="4">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        Foreground="Red"
                                        Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                        Text="{Binding Result_FailCount}" />
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        Text="Failed Circuits"
                                        TextAlignment="Center" />
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!--  Notes  -->
                        <TextBlock
                            Margin="0,16,0,8"
                            FontWeight="SemiBold"
                            Text="System Notes:" />
                        <TextBox
                            MinHeight="60"
                            Background="{DynamicResource MaterialDesignDivider}"
                            IsReadOnly="True"
                            Text="{Binding GUI_Notes}"
                            TextWrapping="Wrap" />

                        <TextBlock
                            Margin="0,16,0,8"
                            FontWeight="SemiBold"
                            Text="User Notes:" />
                        <TextBox
                            MinHeight="60"
                            Text="{Binding User_Notes}"
                            TextWrapping="Wrap" />
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!--  Button Panel  -->
        <Border
            Grid.Row="2"
            Padding="16"
            Background="{DynamicResource MaterialDesignDivider}">
            <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                <Button
                    Margin="0,0,8,0"
                    Click="Calculate_Click"
                    Content="Calculate" />
                <Button
                    Margin="0,0,8,0"
                    Click="Save_Click"
                    Content="Save" />
                <Button Click="Cancel_Click" Content="Cancel" />
            </StackPanel>
        </Border>
    </Grid>
</Window>
