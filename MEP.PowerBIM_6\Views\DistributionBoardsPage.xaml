<Page
    x:Class="MEP.PowerBIM_6.Views.DistributionBoardsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Distribution Boards"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  Page Header  -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Style="{StaticResource MaterialDesignHeadline4TextBlock}" Text="Distribution Boards" />
            <TextBlock
                Margin="0,4,0,0"
                Foreground="{DynamicResource MaterialDesignBodyLight}"
                Text="Manage electrical distribution boards and their circuits" />
        </StackPanel>

        <!--  Distribution Boards List  -->
        <materialDesign:Card Grid.Row="2" Padding="16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  Search and Filter  -->
                <Grid Grid.Row="0" Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBox
                        Grid.Column="0"
                        Margin="0,0,8,0"
                        materialDesign:HintAssist.Hint="Search distribution boards..."
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" />

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <CheckBox
                            Margin="8,0"
                            Content="Show Only Failed"
                            IsChecked="{Binding ShowOnlyFailed}" />
                        <CheckBox
                            Margin="8,0"
                            Content="Show Only Locked"
                            IsChecked="{Binding ShowOnlyLocked}" />
                    </StackPanel>
                </Grid>

                <Grid Grid.Row="1" Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!--  Distribution Board List with Status  -->
                    <materialDesign:Card
                        Grid.Column="0"
                        Margin="0,0,8,0"
                        materialDesign:ElevationAssist.Elevation="Dp2">
                        <DataGrid
                            Grid.Row="1"
                            Margin="16,0,16,16"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            ItemsSource="{Binding DistributionBoards}"
                            SelectedItem="{Binding SelectedDistributionBoard}"
                            Style="{StaticResource MaterialDesignDataGrid}">
                            <DataGrid.Columns>
                                <!--  Distribution Board Name  -->
                                <DataGridTextColumn
                                    Width="*"
                                    Binding="{Binding Name}"
                                    Header="Distribution Board Name"
                                    IsReadOnly="True" />
                                <!--  Pass Count  -->
                                <DataGridTextColumn
                                    Width="80"
                                    Binding="{Binding PassCount}"
                                    Header="Pass CCTs" />
                                <!--  Warning Count  -->
                                <DataGridTextColumn
                                    Width="80"
                                    Binding="{Binding WarningCount}"
                                    Header="Warning CCTs" />
                                <!--  Fail Count  -->
                                <DataGridTextColumn
                                    Width="80"
                                    Binding="{Binding FailCount}"
                                    Header="Fail CCTs"
                                    IsReadOnly="True" />
                                <!--  User Notes  -->
                                <DataGridTextColumn
                                    Width="80"
                                    Binding="{Binding User_Notes}"
                                    Header="Notes"
                                    IsReadOnly="True" />
                                <!--  User Notes  -->
                                <DataGridTextColumn
                                    Width="80"
                                    Binding="{Binding GUI_Notes}"
                                    Header="Error/Warning"
                                    IsReadOnly="True" />
                                <!--  Update Required  -->
                                <DataGridCheckBoxColumn
                                    Width="60"
                                    Binding="{Binding Update_Required}"
                                    Header="UR"
                                    IsReadOnly="True" />
                                <!--  Status  -->
                                <DataGridTextColumn
                                    Width="100"
                                    Binding="{Binding Status}"
                                    Header="Status"
                                    IsReadOnly="True" />
                            </DataGrid.Columns>
                        </DataGrid>
                    </materialDesign:Card>

                    <!--  Distribution Board Actions Panel  -->
                    <materialDesign:Card
                        Grid.Column="1"
                        Margin="8,0,0,0"
                        materialDesign:ElevationAssist.Elevation="Dp2">
                        <StackPanel Margin="16">
                            <TextBlock
                                Margin="0,0,0,16"
                                Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                Text="Distribution Board Actions" />

                            <Button
                                Margin="0,4"
                                Background="#12A8B2"
                                BorderBrush="#12A8B2"
                                Command="{Binding RunCalculationsCommand}"
                                Content="Run Calculations"
                                Foreground="White"
                                IsEnabled="{Binding CanRunCalculations}"
                                Style="{StaticResource MaterialDesignRaisedButton}" />

                            <Button
                                Margin="0,4"
                                Background="#12A8B2"
                                BorderBrush="#12A8B2"
                                Command="{Binding OpenCircuitEditCommand}"
                                Content="Enhanced Circuit Edit"
                                Foreground="White"
                                IsEnabled="{Binding HasSelectedDistributionBoard}"
                                Style="{StaticResource MaterialDesignRaisedButton}" />

                            <Button
                                Margin="0,4"
                                Background="#12A8B2"
                                BorderBrush="#12A8B2"
                                Command="{Binding OpenDbEditCommand}"
                                Content="Distribution Board Edit"
                                Foreground="White"
                                IsEnabled="{Binding HasSelectedDistributionBoard}"
                                Style="{StaticResource MaterialDesignRaisedButton}" />

                            <Separator Margin="0,16" />

                            <Button
                                Margin="0,4"
                                BorderBrush="#12A8B2"
                                Command="{Binding ExportDataCommand}"
                                Content="Export Data"
                                Foreground="#12A8B2"
                                Style="{StaticResource MaterialDesignOutlinedButton}" />

                            <Button
                                Margin="0,4"
                                BorderBrush="#12A8B2"
                                Command="{Binding OpenAdvancedSettingsCommand}"
                                Content="Advanced Settings"
                                Foreground="#12A8B2"
                                Style="{StaticResource MaterialDesignOutlinedButton}" />

                            <Separator Margin="0,16" />

                            <Button
                                Margin="0,4"
                                Background="#FFCE00"
                                BorderBrush="#FFCE00"
                                Command="{Binding SaveProjectCommand}"
                                Content="Save Project"
                                IsEnabled="{Binding HasUnsavedChanges}"
                                Style="{StaticResource MaterialDesignRaisedAccentButton}" />
                        </StackPanel>
                    </materialDesign:Card>
                </Grid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>
