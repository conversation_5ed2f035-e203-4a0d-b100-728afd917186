# Quick Start Tutorial: Your First 30 Minutes with Testing

## Before We Start

**What you need:**
- Visual Studio 2019 or later
- Your MEP.PowerBIM_6 project open
- 30 minutes of time

**What we'll do:**
1. Set up testing in 5 minutes
2. Run your first test in 2 minutes
3. Understand what happened in 10 minutes
4. Write your own test in 13 minutes

## Part 1: Setup (5 minutes)

### Step 1: Add Test Project (2 minutes)

1. **In Visual Studio, right-click your solution name** (top item in Solution Explorer)
2. **Click "Add" > "New Project"**
3. **In the search box, type "MSTest"**
4. **Select "MSTest Test Project (.NET Framework)"**
5. **Name it exactly**: `MEP.PowerBIM_6.Tests`
6. **Click "Create"**

**You should see**: A new project appears in Solution Explorer with a file called `UnitTest1.cs`

### Step 2: Connect Test Project to Main Project (1 minute)

1. **Right-click "MEP.PowerBIM_6.Tests" project**
2. **Click "Add" > "Project Reference"**
3. **Check the box next to "MEP.PowerBIM_6"**
4. **Click "OK"**

**You should see**: Under References in your test project, you'll see MEP.PowerBIM_6

### Step 3: Install Test Packages (2 minutes)

1. **Right-click "MEP.PowerBIM_6.Tests" project**
2. **Click "Manage NuGet Packages"**
3. **Click "Browse" tab**
4. **Search for and install these packages** (click Install for each):
   - `xunit` (the testing framework)
   - `xunit.runner.visualstudio` (to run tests in Visual Studio)
   - `Moq` (for creating fake objects)
   - `FluentAssertions` (for easier test writing)

**You should see**: These packages appear under "Dependencies" > "Packages"

## Part 2: Your First Test (2 minutes)

### Step 1: Replace the Default Test

1. **Delete the file `UnitTest1.cs`**
2. **Right-click the test project > Add > Class**
3. **Name it**: `MyFirstTest.cs`
4. **Replace all the code** with this:

```csharp
using FluentAssertions;
using Xunit;

namespace MEP.PowerBIM_6.Tests
{
    public class MyFirstTest
    {
        [Fact]
        public void AddNumbers_TwoNumbers_ReturnsSum()
        {
            // Arrange: Set up the test
            int firstNumber = 5;
            int secondNumber = 3;

            // Act: Do the thing we're testing
            int result = firstNumber + secondNumber;

            // Assert: Check if it worked
            result.Should().Be(8);
        }

        [Fact]
        public void MultiplyNumbers_TwoNumbers_ReturnsProduct()
        {
            // Arrange
            int firstNumber = 4;
            int secondNumber = 6;

            // Act
            int result = firstNumber * secondNumber;

            // Assert
            result.Should().Be(24);
        }
    }
}
```

### Step 2: Run Your First Test

1. **Build your solution**: Press `Ctrl+Shift+B`
2. **Open Test Explorer**: Go to `Test` menu > `Test Explorer`
3. **Click "Run All Tests"** (play button icon)

**You should see**: Two green checkmarks next to your test names

🎉 **Congratulations! You just ran your first tests!**

## Part 3: Understanding What Happened (10 minutes)

### What is a Test?

A test is a small program that checks if another program works correctly. Think of it like this:

**Real Life Example:**
- You bake a cake
- You taste it to see if it's good
- The "taste test" is like a unit test

**Code Example:**
- You write a function to add numbers
- You write a test to check if it adds correctly
- The test is your "taste test" for code

### Anatomy of a Test

```csharp
[TestMethod]  // ← This tells Visual Studio "this is a test"
public void AddNumbers_TwoNumbers_ReturnsSum()  // ← Descriptive name
{
    // Arrange: Set up the test data
    int firstNumber = 5;
    int secondNumber = 3;
    
    // Act: Call the function you're testing
    int result = firstNumber + secondNumber;
    
    // Assert: Check if the result is what you expected
    result.Should().Be(8);
}
```

### The AAA Pattern

Every test follows this pattern:

1. **Arrange**: Set up your test data (like preparing ingredients)
2. **Act**: Call the function you're testing (like baking the cake)
3. **Assert**: Check if it worked (like tasting the cake)

### What Makes a Test Pass or Fail?

**✅ Test Passes When:**
- The `Assert` statement is true
- `result.Should().Be(8)` and result actually IS 8

**❌ Test Fails When:**
- The `Assert` statement is false
- `result.Should().Be(8)` but result is actually 7

### Let's Break a Test on Purpose

1. **Change line 16** from `result.Should().Be(8);` to `result.Should().Be(10);`
2. **Run the test again**
3. **You should see**: A red X and an error message like:
   ```
   Expected result to be 10, but found 8.
   ```

This shows you exactly what went wrong! Change it back to `8` and the test will pass again.

## Part 4: Testing Real PowerBIM Code (13 minutes)

Now let's test something from your actual PowerBIM project.

### Step 1: Create a Real Test (5 minutes)

1. **Add a new class**: `ProjectInfoTests.cs`
2. **Add this code**:

```csharp
using FluentAssertions;
using MEP.PowerBIM_6.Models;
using Xunit;

namespace MEP.PowerBIM_6.Tests
{
    public class ProjectInfoTests
    {
        [Fact]
        public void ProjectInfoModel_NewProject_HasDefaultValues()
        {
            // Arrange: Create a new project info
            var projectInfo = new PowerBIM_ProjectInfo();

            // Act: Create the model
            var model = new ProjectInfoModel(projectInfo);

            // Assert: Check it has expected default values
            model.Should().NotBeNull();
            model.JobName.Should().NotBeNull();
        }

        [Fact]
        public void ProjectInfoModel_SetJobName_StoresCorrectly()
        {
            // Arrange
            var projectInfo = new PowerBIM_ProjectInfo();
            var model = new ProjectInfoModel(projectInfo);

            // Act
            model.JobName = "Test Project Name";

            // Assert
            model.JobName.Should().Be("Test Project Name");
        }
    }
}
```

### Step 2: Run and Fix (3 minutes)

1. **Build the solution**
2. **Run the tests**

**If tests fail**: Don't worry! This is normal. The error messages will tell you what's wrong:
- Missing using statements? Add them.
- Class doesn't exist? Check the namespace.
- Property doesn't exist? Check the property name.

### Step 3: Understanding Real Tests (5 minutes)

These tests are checking:

1. **First Test**: When you create a new ProjectInfoModel, it should work and not crash
2. **Second Test**: When you set the JobName property, it should remember the value

**Why is this useful?**
- If someone changes the ProjectInfoModel class and breaks it, these tests will turn red
- You'll know immediately what broke instead of finding out when a user complains

### Step 4: Add More Tests

Try adding this test:

```csharp
[Fact]
public void ProjectInfoModel_SetVoltageDropMax_AcceptsValidValue()
{
    // Arrange
    var projectInfo = new PowerBIM_ProjectInfo();
    var model = new ProjectInfoModel(projectInfo);

    // Act
    model.SystemVoltageDropMax = 5.0;

    // Assert
    model.SystemVoltageDropMax.Should().Be(5.0);
}
```

## What You've Learned

### ✅ You Can Now:
- Create a test project
- Write simple tests
- Run tests and see results
- Understand when tests pass or fail
- Test real code from your project

### ✅ You Understand:
- What testing is and why it's useful
- The AAA pattern (Arrange, Act, Assert)
- How to read test results
- How tests help catch bugs early

## Next Steps (Choose Your Adventure)

### If You Want to Learn More:
1. **Read the full beginner guide** I created
2. **Try the test files I made** for your project
3. **Write tests for functions you're currently working on**

### If You Want to Use My Pre-Made Tests:
1. **Copy the test files** I created into your test project
2. **Run them** to see how they work
3. **Modify them** for your specific needs

### If You Want to Keep It Simple:
1. **Just write simple tests** like the ones in this tutorial
2. **Test new functions** as you write them
3. **Gradually add more tests** over time

## Common Questions

**Q: How many tests should I write?**
A: Start with 1-2 tests per important function. Add more as you get comfortable.

**Q: What if my tests fail?**
A: Great! That means they're working. Fix the code or fix the test.

**Q: Do I need to test everything?**
A: No. Focus on important business logic and functions that could break.

**Q: When should I run tests?**
A: Before committing code, and whenever you make changes.

**Q: What if I don't understand the advanced test files you created?**
A: That's fine! Start with simple tests like these. You can learn the advanced stuff later.

## Remember

- **Testing is a skill** - you'll get better with practice
- **Start simple** - basic tests are better than no tests
- **Tests save time** - they catch bugs before users do
- **Don't be perfect** - any testing is better than none

🎉 **You're now a beginner tester! Welcome to the world of confident coding!**
