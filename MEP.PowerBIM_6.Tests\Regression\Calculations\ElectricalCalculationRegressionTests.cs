using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using FluentAssertions;
using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Services.Interfaces;
using MEP.PowerBIM_6.Tests.Helpers.MockServices;
using MEP.PowerBIM_6.Tests.Helpers.TestFixtures;
using Newtonsoft.Json;
using Xunit;
using Xunit.Abstractions;

namespace MEP.PowerBIM_6.Tests.Regression.Calculations
{
    [Trait("Category", "Regression")]
    [Trait("Category", "Calculations")]
    public class ElectricalCalculationRegressionTests : IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ICalculationService _calculationService;
        private readonly string _testDataPath;
        private readonly ITestOutputHelper _output;

        public ElectricalCalculationRegressionTests(ITestOutputHelper output)
        {
            _output = output;
            _serviceProvider = MockServiceProvider.CreateWithMockServices();
            _calculationService = _serviceProvider.GetService<ICalculationService>();
            _testDataPath = Path.Combine(Directory.GetCurrentDirectory(), "TestData", "ExpectedResults");

            // Ensure test data directory exists
            Directory.CreateDirectory(_testDataPath);
        }

        public void Dispose()
        {
            // Cleanup handled automatically by xUnit
        }

        #region Distribution Board Calculation Regression Tests

        [Theory]
        [InlineData("SmallProject", 3, 15)] // 3 DBs, 15 total circuits
        [InlineData("MediumProject", 10, 50)] // 10 DBs, 50 total circuits
        [InlineData("LargeProject", 25, 125)] // 25 DBs, 125 total circuits
        public void DistributionBoardCalculations_KnownProjects_MatchExpectedResults(string projectName, int dbCount, int totalCircuits)
        {
            // Arrange
            var testProject = CreateTestProject(projectName, dbCount, totalCircuits / dbCount);
            var expectedResults = LoadExpectedResults($"{projectName}_DBCalculations.json");

            // Act
            var actualResults = new Dictionary<string, object>();
            foreach (var db in testProject)
            {
                var success = _calculationService.RecalculateDistributionBoard(db);
                success.Should().BeTrue($"Calculation should succeed for {db.Schedule_DB_Name}");

                // Collect results for comparison
                actualResults[db.Schedule_DB_Name] = new
                {
                    TotalLoad = db.DB_TotalLoad,
                    DiversifiedLoad = db.DB_DiversifiedLoad,
                    CircuitCount = db.CCTs.Count,
                    MaxVoltageDropPercentage = db.CCTs.Max(c => c.CCT_VoltageDropPercentage),
                    PassingCircuits = db.CCTs.Count(c => c.CCT_ValidationPassed),
                    FailingCircuits = db.CCTs.Count(c => !c.CCT_ValidationPassed)
                };
            }

            // Assert
            if (expectedResults != null)
            {
                CompareCalculationResults(actualResults, expectedResults, $"{projectName} DB calculations");
            }
            else
            {
                // Save results as baseline for future regression testing
                SaveExpectedResults($"{projectName}_DBCalculations.json", actualResults);
                _output.WriteLine($"Baseline results saved for {projectName}");
            }
        }

        [Fact]
        public void DistributionBoardCalculations_EdgeCases_HandledCorrectly()
        {
            // Test edge cases that have caused issues in the past
            var testCases = new[]
            {
                TestDataFactory.CreateEmptyDistributionBoard(), // No circuits
                TestDataFactory.CreateLockedDistributionBoard(), // Locked DB
                CreateDBWithHighLoads(), // Very high loads
                CreateDBWithZeroLoads(), // Zero loads
                CreateDBWithMixedPhases() // Mixed single/three phase
            };

            foreach (var db in testCases)
            {
                // Act & Assert - Should not throw exceptions
                var exception = Record.Exception(() => _calculationService.RecalculateDistributionBoard(db));
                exception.Should().BeNull($"Calculation should handle edge case: {db.Schedule_DB_Name}");
            }
        }

        #endregion

        #region Circuit Calculation Regression Tests

        [Theory]
        [InlineData("StandardCircuits")]
        [InlineData("HighLoadCircuits")]
        [InlineData("LongCableCircuits")]
        [InlineData("MixedPhaseCircuits")]
        public void CircuitCalculations_KnownScenarios_MatchExpectedResults(string scenarioName)
        {
            // Arrange
            var testCircuits = CreateTestCircuits(scenarioName);
            var expectedResults = LoadExpectedResults($"{scenarioName}_CircuitCalculations.json");

            // Act
            var actualResults = new Dictionary<string, object>();
            foreach (var circuit in testCircuits)
            {
                var success = _calculationService.RunCircuitCalculations(circuit);
                success.Should().BeTrue($"Calculation should succeed for circuit {circuit.CCT_Number}");

                actualResults[circuit.CCT_Number] = new
                {
                    Current = circuit.CCT_Current,
                    VoltageDropPercentage = circuit.CCT_VoltageDropPercentage,
                    EFLI = circuit.CCT_EFLI,
                    ValidationPassed = circuit.CCT_ValidationPassed,
                    ClearingTime = circuit.CCT_ClearingTime
                };
            }

            // Assert
            if (expectedResults != null)
            {
                CompareCalculationResults(actualResults, expectedResults, $"{scenarioName} circuit calculations");
            }
            else
            {
                SaveExpectedResults($"{scenarioName}_CircuitCalculations.json", actualResults);
                _output.WriteLine($"Baseline results saved for {scenarioName}");
            }
        }

        [Fact]
        public void VoltageDropCalculations_VariousScenarios_WithinTolerances()
        {
            // Test voltage drop calculations with known values
            var testCases = new[]
            {
                new { Circuit = CreateTestCircuit("VD_Test_1", 1000, 50, "2.5"), ExpectedVD = 2.1, Tolerance = 0.1 },
                new { Circuit = CreateTestCircuit("VD_Test_2", 2000, 100, "4.0"), ExpectedVD = 3.8, Tolerance = 0.1 },
                new { Circuit = CreateTestCircuit("VD_Test_3", 500, 25, "1.5"), ExpectedVD = 1.9, Tolerance = 0.1 }
            };

            foreach (var testCase in testCases)
            {
                // Act
                var actualVD = _calculationService.CalculateFinalCircuitVoltDrop(testCase.Circuit, VoltDropCalculation.Standard);

                // Assert
                actualVD.Should().BeApproximately(testCase.ExpectedVD, testCase.Tolerance,
                    $"Voltage drop for circuit {testCase.Circuit.CCT_Number} should be within tolerance");
            }
        }

        [Fact]
        public void EFLICalculations_KnownValues_MatchExpectedResults()
        {
            // Test EFLI calculations with known impedance values
            var testCircuits = new[]
            {
                CreateTestCircuitWithEFLI("EFLI_Test_1", 0.5, 0.3),
                CreateTestCircuitWithEFLI("EFLI_Test_2", 0.8, 0.4),
                CreateTestCircuitWithEFLI("EFLI_Test_3", 1.2, 0.6)
            };

            foreach (var circuit in testCircuits)
            {
                // Act
                var success = _calculationService.CalculateAndValidateEFLI(circuit);
                var efli = _calculationService.CalculateEFLI(circuit, 0.2, 0.1); // DB EFLI values

                // Assert
                success.Should().BeTrue($"EFLI calculation should succeed for circuit {circuit.CCT_Number}");
                efli.Should().BeGreaterThan(0, "EFLI should be positive");
                efli.Should().BeLessThan(10, "EFLI should be reasonable");
            }
        }

        #endregion

        #region Performance Regression Tests

        [Fact(Timeout = 30000)] // 30 second timeout
        public void LargeProjectCalculations_PerformanceRegression_CompletesWithinTimeLimit()
        {
            // Arrange
            var largeProject = TestDataFactory.CreateLargeProject(50, 20); // 50 DBs, 20 circuits each = 1000 circuits

            // Act
            var startTime = DateTime.Now;
            foreach (var db in largeProject)
            {
                _calculationService.RecalculateDistributionBoard(db);
            }
            var endTime = DateTime.Now;
            var duration = endTime - startTime;

            // Assert
            duration.TotalSeconds.Should().BeLessThan(25, "Large project calculations should complete within 25 seconds");
            _output.WriteLine($"Large project calculation completed in {duration.TotalSeconds:F2} seconds");
        }

        [Fact]
        public void MemoryUsage_LargeCalculations_DoesNotExceedLimits()
        {
            // Arrange
            var initialMemory = GC.GetTotalMemory(true);
            var largeProject = TestDataFactory.CreateLargeProject(20, 15);

            // Act
            foreach (var db in largeProject)
            {
                _calculationService.RecalculateDistributionBoard(db);
            }

            var finalMemory = GC.GetTotalMemory(true);
            var memoryIncrease = finalMemory - initialMemory;

            // Assert
            memoryIncrease.Should().BeLessThan(100 * 1024 * 1024, "Memory increase should be less than 100MB");
            _output.WriteLine($"Memory increase: {memoryIncrease / (1024 * 1024):F2} MB");
        }

        #endregion

        #region Helper Methods

        private List<PowerBIM_DBData> CreateTestProject(string projectName, int dbCount, int circuitsPerDB)
        {
            return TestDataFactory.CreateLargeProject(dbCount, circuitsPerDB);
        }

        private List<PowerBIM_CircuitData> CreateTestCircuits(string scenarioName)
        {
            return scenarioName switch
            {
                "StandardCircuits" => TestDataFactory.CreateTestCircuits(5),
                "HighLoadCircuits" => CreateHighLoadCircuits(),
                "LongCableCircuits" => CreateLongCableCircuits(),
                "MixedPhaseCircuits" => CreateMixedPhaseCircuits(),
                _ => TestDataFactory.CreateTestCircuits(5)
            };
        }

        private PowerBIM_DBData CreateDBWithHighLoads()
        {
            var db = TestDataFactory.CreateTestDistributionBoard("High Load DB", 5);
            foreach (var circuit in db.CCTs)
            {
                circuit.CCT_Load = 5000; // High load
            }
            return db;
        }

        private PowerBIM_DBData CreateDBWithZeroLoads()
        {
            var db = TestDataFactory.CreateTestDistributionBoard("Zero Load DB", 3);
            foreach (var circuit in db.CCTs)
            {
                circuit.CCT_Load = 0; // Zero load
            }
            return db;
        }

        private PowerBIM_DBData CreateDBWithMixedPhases()
        {
            var db = TestDataFactory.CreateTestDistributionBoard("Mixed Phase DB", 6);
            for (int i = 0; i < db.CCTs.Count; i++)
            {
                db.CCTs[i].CCT_Phases = i % 3 == 0 ? 3 : 1; // Mix of 1 and 3 phase
            }
            return db;
        }

        private List<PowerBIM_CircuitData> CreateHighLoadCircuits()
        {
            var circuits = TestDataFactory.CreateTestCircuits(3);
            foreach (var circuit in circuits)
            {
                circuit.CCT_Load = 10000; // 10kW load
            }
            return circuits;
        }

        private List<PowerBIM_CircuitData> CreateLongCableCircuits()
        {
            var circuits = TestDataFactory.CreateTestCircuits(3);
            foreach (var circuit in circuits)
            {
                circuit.CCT_Length = 200; // 200m cable
            }
            return circuits;
        }

        private List<PowerBIM_CircuitData> CreateMixedPhaseCircuits()
        {
            var circuits = TestDataFactory.CreateTestCircuits(6);
            for (int i = 0; i < circuits.Count; i++)
            {
                circuits[i].CCT_Phases = i % 2 == 0 ? 1 : 3;
            }
            return circuits;
        }

        private PowerBIM_CircuitData CreateTestCircuit(string number, double load, double length, string cableSize)
        {
            return new PowerBIM_CircuitData
            {
                CCT_Number = number,
                CCT_Load = load,
                CCT_Length = length,
                CCT_CableSize = cableSize,
                CCT_Voltage = 230,
                CCT_Phases = 1,
                CCT_PowerFactor = 0.85
            };
        }

        private PowerBIM_CircuitData CreateTestCircuitWithEFLI(string number, double r, double x)
        {
            var circuit = CreateTestCircuit(number, 1000, 50, "2.5");
            circuit.CCT_EFLI_R = r;
            circuit.CCT_EFLI_X = x;
            return circuit;
        }

        private Dictionary<string, object> LoadExpectedResults(string fileName)
        {
            var filePath = Path.Combine(_testDataPath, fileName);
            if (!File.Exists(filePath))
                return null;

            var json = File.ReadAllText(filePath);
            return JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
        }

        private void SaveExpectedResults(string fileName, Dictionary<string, object> results)
        {
            var filePath = Path.Combine(_testDataPath, fileName);
            var json = JsonConvert.SerializeObject(results, Formatting.Indented);
            File.WriteAllText(filePath, json);
        }

        private void CompareCalculationResults(Dictionary<string, object> actual, Dictionary<string, object> expected, string testName)
        {
            actual.Should().NotBeNull($"{testName} actual results should not be null");
            expected.Should().NotBeNull($"{testName} expected results should not be null");

            foreach (var expectedItem in expected)
            {
                actual.Should().ContainKey(expectedItem.Key, $"{testName} should contain key {expectedItem.Key}");

                // For complex comparisons, you might need custom comparison logic here
                // This is a simplified comparison - expand based on your specific needs
                _output.WriteLine($"Comparing {expectedItem.Key}: Expected={expectedItem.Value}, Actual={actual[expectedItem.Key]}");
            }
        }

        #endregion
    }
}
