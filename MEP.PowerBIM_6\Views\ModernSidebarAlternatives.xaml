﻿<Window
    x:Class="MEP.PowerBIM_6.Views.ModernSidebarAlternatives"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="ModernSidebarAlternatives"
    Width="1400"
    Height="900"
    Background="#F3F3F3"
    mc:Ignorable="d">

    <Window.Resources>
        <!--  Windows 11 Style Colors  -->
        <SolidColorBrush x:Key="Win11AccentBrush" Color="#0078D4" />
        <SolidColorBrush x:Key="Win11AccentLightBrush" Color="#106EBE" />
        <SolidColorBrush x:Key="Win11BackgroundBrush" Color="#FAFAFA" />
        <SolidColorBrush x:Key="Win11CardBrush" Color="#FFFFFF" />
        <SolidColorBrush x:Key="Win11BorderBrush" Color="#E5E5E5" />
        <SolidColorBrush x:Key="Win11TextBrush" Color="#323130" />
        <SolidColorBrush x:Key="Win11TextSecondaryBrush" Color="#605E5C" />
        <SolidColorBrush x:Key="Win11HoverBrush" Color="#F5F5F5" />
        <SolidColorBrush x:Key="Win11SelectedBrush" Color="#E3F2FD" />

        <!--  Modern Navigation Button Style  -->
        <Style x:Key="ModernNavButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Padding" Value="16,12" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
            <Setter Property="HorizontalContentAlignment" Value="Left" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="Normal" />
            <Setter Property="Foreground" Value="{StaticResource Win11TextBrush}" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border
                            x:Name="border"
                            Margin="4,2"
                            Background="{TemplateBinding Background}"
                            CornerRadius="6">
                            <ContentPresenter
                                Margin="{TemplateBinding Padding}"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource Win11HoverBrush}" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource Win11SelectedBrush}" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!--  Selected Navigation Button Style  -->
        <Style
            x:Key="SelectedNavButtonStyle"
            BasedOn="{StaticResource ModernNavButtonStyle}"
            TargetType="Button">
            <Setter Property="Background" Value="{StaticResource Win11SelectedBrush}" />
            <Setter Property="Foreground" Value="{StaticResource Win11AccentBrush}" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>

        <!--  Modern Card Style  -->
        <Style x:Key="ModernCardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource Win11CardBrush}" />
            <Setter Property="CornerRadius" Value="8" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="{StaticResource Win11BorderBrush}" />
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect
                        BlurRadius="8"
                        Opacity="0.1"
                        ShadowDepth="2"
                        Color="#000000" />
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <TextBlock Text="Modern Windows 11 Style Sidebar Alternatives" 
                       FontSize="24" FontWeight="Bold" 
                       Foreground="{StaticResource Win11TextBrush}"
                       Margin="0,0,0,30"/>

            <!-- Alternative 1: Clean Minimal Design -->
         
            <!-- Alternative 2: Fluent Design with Acrylic Effect -->
          
            <!-- Alternative 3: Dark Theme with Rounded Design -->
         
            <!-- Alternative 4: Enhanced Minimalist Sidebar with Collapsible Feature -->
            <TextBlock Text="Alternative 4: Enhanced Ultra-Minimalist Design (Collapsible)"
                       FontSize="18" FontWeight="SemiBold"
                       Foreground="{StaticResource Win11AccentBrush}"
                       Margin="0,0,0,15"/>

            <!-- Collapsible Sidebar Container -->
            <Grid HorizontalAlignment="Left" Margin="0,0,0,40">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Main Sidebar -->
                <Border x:Name="MainSidebar" Grid.Column="0" Width="280"
                        Background="White" CornerRadius="4" BorderThickness="0">
                    <Border.Effect>
                        <DropShadowEffect Color="#000000" Opacity="0.08" ShadowDepth="0" BlurRadius="20"/>
                    </Border.Effect>

                    <StackPanel>
                        <!-- Header with Collapse Button -->
                        <Border Padding="24,20" BorderBrush="#F0F0F0" BorderThickness="0,0,0,1">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="PowerBIM"
                                           FontSize="20" FontWeight="Light"
                                           Foreground="#323130"/>
                                <Button Grid.Column="1" Background="Transparent" BorderThickness="0"
                                        Width="32" Height="32" Cursor="Hand"
                                        ToolTip="Collapse Sidebar">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Background" Value="Transparent"/>
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#F0F0F0"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                    <Button.Template>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" CornerRadius="4">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Button.Template>
                                    <TextBlock Text="‹" FontSize="16" Foreground="#605E5C" FontWeight="Bold"/>
                                </Button>
                            </Grid>
                        </Border>

                        <!-- Clean Navigation with Icons -->
                        <StackPanel Margin="0,16,0,8">
                            <!-- Selected Home Item -->
                            <Button Background="#F8F9FA" BorderThickness="0" Padding="20,16" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Cursor="Hand" Margin="0,0,0,1">
                                <StackPanel Orientation="Horizontal">
                                    <Border Width="3" Height="16" Background="#0078D4" CornerRadius="2" Margin="0,0,16,0"/>
                                    <TextBlock Text="🏠" FontSize="14" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="Home" FontSize="14" Foreground="#0078D4" FontWeight="Medium" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Distribution Boards -->
                            <Button Background="Transparent" BorderThickness="0" Padding="20,16" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Cursor="Hand" Margin="0,0,0,1">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="⚡" FontSize="14" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="Distribution Boards" FontSize="14" Foreground="#605E5C" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- MCC -->
                            <Button Background="Transparent" BorderThickness="0" Padding="20,16" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Cursor="Hand" Margin="0,0,0,1">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="⚙️" FontSize="14" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="MCC" FontSize="14" Foreground="#605E5C" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Bulk Data Entry -->
                            <Button Background="Transparent" BorderThickness="0" Padding="20,16" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Cursor="Hand" Margin="0,0,0,1">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🗂️" FontSize="14" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="Bulk Data Entry" FontSize="14" Foreground="#605E5C" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- PB Settings -->
                            <Button Background="Transparent" BorderThickness="0" Padding="20,16" HorizontalAlignment="Stretch" HorizontalContentAlignment="Left" Cursor="Hand">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🎚️" FontSize="14" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="PB Settings" FontSize="14" Foreground="#605E5C" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>

                        <!-- Enhanced Summary with Status Indicators -->
                        <Border Margin="20,16,20,20" Padding="0,16,0,0" BorderBrush="#F0F0F0" BorderThickness="0,1,0,0">
                            <StackPanel>
                                <!-- Main Stats -->
                                <Grid Margin="0,0,0,16">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                        <TextBlock Text="24" FontSize="24" FontWeight="Light" Foreground="#323130" HorizontalAlignment="Center"/>
                                        <TextBlock Text="DBs" FontSize="10" Foreground="#8A8886" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                        <TextBlock Text="156" FontSize="24" FontWeight="Light" Foreground="#323130" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Circuits" FontSize="10" Foreground="#8A8886" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                        <TextBlock Text="91%" FontSize="24" FontWeight="Light" Foreground="#107C10" HorizontalAlignment="Center"/>
                                        <TextBlock Text="Success" FontSize="10" Foreground="#8A8886" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </Grid>

                                <!-- Status Indicators - Minimalist Style -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Border Background="#F0F8F0" CornerRadius="8" Padding="8,4" Margin="0,0,6,0" BorderThickness="1" BorderBrush="#E8F5E8">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="✓" Foreground="#107C10" FontSize="11" Margin="0,0,4,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="142" Foreground="#107C10" FontSize="11" FontWeight="Medium" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Background="#FFFAF0" CornerRadius="8" Padding="8,4" Margin="0,0,6,0" BorderThickness="1" BorderBrush="#FFF4CE">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="⚠" Foreground="#F7630C" FontSize="11" Margin="0,0,4,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="12" Foreground="#F7630C" FontSize="11" FontWeight="Medium" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Background="#FDF8F8" CornerRadius="8" Padding="8,4" BorderThickness="1" BorderBrush="#FDE7E9">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="✗" Foreground="#D13438" FontSize="11" Margin="0,0,4,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="2" Foreground="#D13438" FontSize="11" FontWeight="Medium" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Collapsed Sidebar (Icon Only) -->
                <Border x:Name="CollapsedSidebar" Grid.Column="1" Width="60" Visibility="Collapsed"
                        Background="White" CornerRadius="4" BorderThickness="0" Margin="8,0,0,0">
                    <Border.Effect>
                        <DropShadowEffect Color="#000000" Opacity="0.08" ShadowDepth="0" BlurRadius="20"/>
                    </Border.Effect>

                    <StackPanel>
                        <!-- Collapsed Header with Expand Button -->
                        <Border Padding="14,20" BorderBrush="#F0F0F0" BorderThickness="0,0,0,1">
                            <Button Background="Transparent" BorderThickness="0"
                                    Width="32" Height="32" Cursor="Hand"
                                    ToolTip="Expand Sidebar">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F0F0F0"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="4">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Button.Template>
                                <TextBlock Text="›" FontSize="16" Foreground="#605E5C" FontWeight="Bold"/>
                            </Button>
                        </Border>

                        <!-- Collapsed Navigation Icons -->
                        <StackPanel Margin="0,16,0,8">
                            <!-- Selected Home -->
                            <Button Background="#F8F9FA" BorderThickness="0" Padding="18,16" HorizontalAlignment="Stretch" Cursor="Hand" Margin="0,0,0,4"
                                    ToolTip="Home">
                                <Border Width="3" Height="16" Background="#0078D4" CornerRadius="2" HorizontalAlignment="Left"/>
                            </Button>

                            <!-- Other Icons -->
                            <Button Background="Transparent" BorderThickness="0" Padding="18,16" HorizontalAlignment="Stretch" Cursor="Hand" Margin="0,0,0,4"
                                    ToolTip="Distribution Boards">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <TextBlock Text="⚡" FontSize="14" Foreground="#605E5C" HorizontalAlignment="Center"/>
                            </Button>

                            <Button Background="Transparent" BorderThickness="0" Padding="18,16" HorizontalAlignment="Stretch" Cursor="Hand" Margin="0,0,0,4"
                                    ToolTip="MCC">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <TextBlock Text="⚙️" FontSize="14" Foreground="#605E5C" HorizontalAlignment="Center"/>
                            </Button>

                            <Button Background="Transparent" BorderThickness="0" Padding="18,16" HorizontalAlignment="Stretch" Cursor="Hand" Margin="0,0,0,4"
                                    ToolTip="Bulk Data Entry">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <TextBlock Text="🗂️" FontSize="14" Foreground="#605E5C" HorizontalAlignment="Center"/>
                            </Button>

                            <Button Background="Transparent" BorderThickness="0" Padding="18,16" HorizontalAlignment="Stretch" Cursor="Hand"
                                    ToolTip="PB Settings">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <TextBlock Text="🎚️" FontSize="14" Foreground="#605E5C" HorizontalAlignment="Center"/>
                            </Button>
                        </StackPanel>

                        <!-- Collapsed Status Dots -->
                        <Border Margin="14,16,14,20" Padding="0,16,0,0" BorderBrush="#F0F0F0" BorderThickness="0,1,0,0">
                            <StackPanel HorizontalAlignment="Center">
                                <Ellipse Width="8" Height="8" Fill="#107C10" Margin="0,0,0,4" ToolTip="142 Passed"/>
                                <Ellipse Width="8" Height="8" Fill="#F7630C" Margin="0,0,0,4" ToolTip="12 Warnings"/>
                                <Ellipse Width="8" Height="8" Fill="#D13438" ToolTip="2 Failed"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>
            </Grid>
        </StackPanel>
    </ScrollViewer>

</Window>
