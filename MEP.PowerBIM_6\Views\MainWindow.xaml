﻿<Window
    x:Class="MEP.PowerBIM_6.Views.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="MEP Tool | Electrical"
    Width="1200"
    Height="800"
    Background="White"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  Converters  -->
            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />
        </ResourceDictionary>
    </Window.Resources>

    <materialDesign:DialogHost>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <!--  Header  -->
                <RowDefinition Height="*" />
                <!--  Main Content  -->
                <RowDefinition Height="Auto" />
                <!--  Status Bar  -->
            </Grid.RowDefinitions>

            <!--  Header with Logo and Title  -->
            <materialDesign:ColorZone
                Grid.Row="0"
                Padding="16"
                materialDesign:ElevationAssist.Elevation="Dp4"
                Mode="PrimaryMid">
                <StackPanel Orientation="Horizontal">
                    <Image
                        Height="32"
                        Margin="0,0,16,0"
                        VerticalAlignment="Center" />
                    <TextBlock
                        VerticalAlignment="Center"
                        Foreground="White"
                        Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                        Text="PowerBIM 6" />
                    <TextBlock
                        Margin="8,0,0,0"
                        VerticalAlignment="Center"
                        Foreground="White"
                        Opacity="0.8"
                        Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                        Text="- Electrical Design Analysis Tool" />
                </StackPanel>
            </materialDesign:ColorZone>

            <!--  Main Tabbed Interface  -->
            <TabControl
                Grid.Row="1"
                Margin="8"
                Style="{StaticResource MaterialDesignNavigationRailTabControl}">

                <!--  Distribution Board Overview Tab  -->
                <TabItem Header="Distribution Board Overview">
                    <Grid Margin="16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  Distribution Board List with Status  -->
                        <materialDesign:Card
                            Grid.Column="0"
                            Margin="0,0,8,0"
                            materialDesign:ElevationAssist.Elevation="Dp2">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>

                                <!--  Page Header  -->
                                <StackPanel Grid.Row="0" Margin="0,0,0,16">
                                    <TextBlock Style="{StaticResource MaterialDesignHeadline4TextBlock}" Text="Distribution Boards" />
                                    <TextBlock
                                        Margin="0,4,0,0"
                                        Foreground="{DynamicResource MaterialDesignBodyLight}"
                                        Text="Manage electrical distribution boards and their circuits" />
                                </StackPanel>

                                <DataGrid
                                    Grid.Row="1"
                                    Margin="16,0,16,16"
                                    AutoGenerateColumns="False"
                                    CanUserAddRows="False"
                                    CanUserDeleteRows="False"
                                    ItemsSource="{Binding DistributionBoards}"
                                    SelectedItem="{Binding SelectedDistributionBoard}"
                                    Style="{StaticResource MaterialDesignDataGrid}">
                                    <DataGrid.Columns>
                                        <!--  Distribution Board Name  -->
                                        <DataGridTextColumn
                                            Width="*"
                                            Binding="{Binding Name}"
                                            Header="Distribution Board Name"
                                            IsReadOnly="True" />
                                        <!--  Pass Count  -->
                                        <DataGridTextColumn
                                            Width="80"
                                            Binding="{Binding PassCount}"
                                            Header="Pass CCTs" />
                                        <!--  Warning Count  -->
                                        <DataGridTextColumn
                                            Width="80"
                                            Binding="{Binding WarningCount}"
                                            Header="Warning CCTs" />
                                        <!--  Fail Count  -->
                                        <DataGridTextColumn
                                            Width="80"
                                            Binding="{Binding FailCount}"
                                            Header="Fail CCTs"
                                            IsReadOnly="True" />
                                        <!--  User Notes  -->
                                        <DataGridTextColumn
                                            Width="80"
                                            Binding="{Binding User_Notes}"
                                            Header="Notes"
                                            IsReadOnly="True" />
                                        <!--  User Notes  -->
                                        <DataGridTextColumn
                                            Width="80"
                                            Binding="{Binding GUI_Notes}"
                                            Header="Error/Warning"
                                            IsReadOnly="True" />
                                        <!--  Update Required  -->
                                        <DataGridCheckBoxColumn
                                            Width="60"
                                            Binding="{Binding Update_Required}"
                                            Header="UR"
                                            IsReadOnly="True" />
                                        <!--  Status  -->
                                        <DataGridTextColumn
                                            Width="100"
                                            Binding="{Binding Status}"
                                            Header="Status"
                                            IsReadOnly="True" />
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </materialDesign:Card>

                        <!--  Distribution Board Actions Panel  -->
                        <materialDesign:Card
                            Grid.Column="1"
                            Margin="8,0,0,0"
                            materialDesign:ElevationAssist.Elevation="Dp2">
                            <StackPanel Margin="16">
                                <TextBlock
                                    Margin="0,0,0,16"
                                    Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                    Text="Distribution Board Actions" />

                                <Button
                                    Margin="0,4"
                                    Command="{Binding RunCalculationsCommand}"
                                    Content="Run Calculations"
                                    IsEnabled="{Binding CanRunCalculations}"
                                    Style="{StaticResource MaterialDesignRaisedButton}" />

                                <Button
                                    Margin="0,4"
                                    Command="{Binding OpenCircuitEditCommand}"
                                    Content="Enhanced Circuit Edit"
                                    IsEnabled="{Binding HasSelectedDistributionBoard}"
                                    Style="{StaticResource MaterialDesignRaisedButton}" />

                                <Button
                                    Margin="0,4"
                                    Command="{Binding OpenDbEditCommand}"
                                    Content="Distribution Board Edit"
                                    IsEnabled="{Binding HasSelectedDistributionBoard}"
                                    Style="{StaticResource MaterialDesignRaisedButton}" />

                                <Separator Margin="0,16" />

                                <Button
                                    Margin="0,4"
                                    Command="{Binding ExportDataCommand}"
                                    Content="Export Data"
                                    Style="{StaticResource MaterialDesignOutlinedButton}" />

                                <Button
                                    Margin="0,4"
                                    Command="{Binding OpenAdvancedSettingsCommand}"
                                    Content="Advanced Settings"
                                    Style="{StaticResource MaterialDesignOutlinedButton}" />

                                <Separator Margin="0,16" />

                                <Button
                                    Margin="0,4"
                                    Command="{Binding SaveProjectCommand}"
                                    Content="Save Project"
                                    IsEnabled="{Binding HasUnsavedChanges}"
                                    Style="{StaticResource MaterialDesignRaisedAccentButton}" />
                            </StackPanel>
                        </materialDesign:Card>
                    </Grid>
                </TabItem>

                <!--  Project Settings Tab  -->
                <TabItem Header="Project Settings">
                    <materialDesign:Card Margin="16" materialDesign:ElevationAssist.Elevation="Dp2">
                        <StackPanel Margin="16">
                            <TextBlock
                                Margin="0,0,0,16"
                                Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                Text="Project Configuration" />

                            <!--  Project settings controls will be added here  -->
                            <TextBlock
                                Foreground="Gray"
                                Style="{StaticResource MaterialDesignBody1TextBlock}"
                                Text="Project settings will be implemented in the next phase" />
                        </StackPanel>
                    </materialDesign:Card>
                </TabItem>

                <!--  Bulk Operations Tab  -->
                <TabItem Header="Bulk Operations">
                    <materialDesign:Card Margin="16" materialDesign:ElevationAssist.Elevation="Dp2">
                        <StackPanel Margin="16">
                            <TextBlock
                                Margin="0,0,0,16"
                                Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                Text="Bulk Edit Operations" />

                            <!--  Bulk operations controls will be added here  -->
                            <TextBlock
                                Foreground="Gray"
                                Style="{StaticResource MaterialDesignBody1TextBlock}"
                                Text="Bulk operations will be implemented in the next phase" />
                        </StackPanel>
                    </materialDesign:Card>
                </TabItem>
            </TabControl>

            <!--  Status Bar  -->
            <StatusBar
                Grid.Row="2"
                materialDesign:ElevationAssist.Elevation="Dp1"
                Background="{DynamicResource MaterialDesignPaper}">
                <StatusBarItem>
                    <TextBlock Margin="8,0" Text="{Binding StatusMessage}" />
                </StatusBarItem>
                <StatusBarItem HorizontalAlignment="Right">
                    <StackPanel Orientation="Horizontal">
                        <ProgressBar
                            Width="100"
                            Height="16"
                            Margin="0,0,8,0"
                            Visibility="{Binding ShowProgress, Converter={StaticResource BoolToVisConverter}}"
                            Value="{Binding Progress}" />
                        <TextBlock
                            Margin="0,0,8,0"
                            VerticalAlignment="Center"
                            Text="{Binding Progress, StringFormat={}{0:F0}%}"
                            Visibility="{Binding ShowProgress, Converter={StaticResource BoolToVisConverter}}" />
                    </StackPanel>
                </StatusBarItem>
            </StatusBar>
        </Grid>
    </materialDesign:DialogHost>
</Window>

