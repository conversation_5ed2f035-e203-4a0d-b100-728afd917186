using System;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Independent PowerBIM Breaker Data class for MEP.PowerBIM_6
    /// Contains breaker/protection device information
    /// </summary>
    public class PowerBIM_BreakerData
    {
        #region Properties

        /// <summary>
        /// Schedule protective device type
        /// </summary>
        public string Schedule_Protective_Device { get; set; }

        /// <summary>
        /// Schedule curve type
        /// </summary>
        public string Schedule_Curve_Type { get; set; }

        /// <summary>
        /// Schedule device rating
        /// </summary>
        public string Schedule_Device_Rating { get; set; }

        /// <summary>
        /// Schedule RCD protection
        /// </summary>
        public string Schedule_RCD_Protection { get; set; }

        /// <summary>
        /// Schedule other controls
        /// </summary>
        public string Schedule_Other_Controls { get; set; }

        /// <summary>
        /// Device rating in amps
        /// </summary>
        public double Device_Rating_Amps { get; set; }

        /// <summary>
        /// Device curve type index
        /// </summary>
        public int Device_Curve_Type_Index { get; set; }

        /// <summary>
        /// Device fault rating in kA
        /// </summary>
        public double Device_kA_Rating { get; set; }

        /// <summary>
        /// RCD rating in mA
        /// </summary>
        public double RCD_Rating_mA { get; set; }

        /// <summary>
        /// RCD type
        /// </summary>
        public string RCD_Type { get; set; }

        /// <summary>
        /// Indicates if RCD is present
        /// </summary>
        public bool RCD_Present { get; set; }

        /// <summary>
        /// Trip time at fault current
        /// </summary>
        public double Trip_Time_At_Fault_Current { get; set; }

        /// <summary>
        /// Magnetic trip current
        /// </summary>
        public double Magnetic_Trip_Current { get; set; }

        /// <summary>
        /// Thermal trip current
        /// </summary>
        public double Thermal_Trip_Current { get; set; }

        /// <summary>
        /// Indicates if device can discriminate
        /// </summary>
        public bool Can_Discriminate { get; set; }

        /// <summary>
        /// Discrimination check result
        /// </summary>
        public string Discrimination_Check { get; set; }

        /// <summary>
        /// Device validation result
        /// </summary>
        public string Device_Valid { get; set; }

        /// <summary>
        /// Indicates if data is good
        /// </summary>
        public bool Data_Good { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        public string Error_Message { get; set; }

        /// <summary>
        /// Schedule trip rating
        /// </summary>
        public double Schedule_Trip_Rating { get; set; }

        /// <summary>
        /// Minimum kA rating
        /// </summary>
        public double Min_kA_Rating { get; set; }

        /// <summary>
        /// kA rating used
        /// </summary>
        public double kA_Rating_Used { get; set; }

        /// <summary>
        /// I²t phase value
        /// </summary>
        public double I2t_Phase { get; set; }

        /// <summary>
        /// I²t earth value
        /// </summary>
        public double I2t_Earth { get; set; }

        /// <summary>
        /// kA earth value
        /// </summary>
        public double kA_Earth { get; set; }

        /// <summary>
        /// EFLI 0.4 second value
        /// </summary>
        public double EFLI_04 { get; set; }

        /// <summary>
        /// EFLI 5.0 second value
        /// </summary>
        public double EFLI_50 { get; set; }

        /// <summary>
        /// EFLI maximum value
        /// </summary>
        public double EFLI_Max { get; set; }

        /// <summary>
        /// Indicates if parameters are good
        /// </summary>
        public bool Parameters_Good { get; set; }

        /// <summary>
        /// Indicates if values are missing
        /// </summary>
        public bool Values_Missing { get; set; }

        /// <summary>
        /// Indicates if this is spare or space
        /// </summary>
        public bool Is_Spare_Or_Space { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor
        /// </summary>
        public PowerBIM_BreakerData()
        {
            InitializeDefaults();
        }

        #endregion

        #region Methods

        /// <summary>
        /// Update breaker data and cross-reference with manufacturer tables
        /// Based on legacy UpdateBreakerData() method
        /// </summary>
        public void UpdateBreakerData()
        {
            try
            {
                // Cross reference this data to the CPD manufacturer tables
                if (Parameters_Good)
                {
                    if (DetermineCircuitBreakerData())
                    {
                        Data_Good = true;
                        Error_Message = string.Empty;
                    }
                    else
                    {
                        Data_Good = false;
                        Error_Message = "Failed to determine circuit breaker data from manufacturer tables";
                    }
                }
                else
                {
                    Data_Good = false;
                    Error_Message = "Parameters not valid for breaker data update";
                }
            }
            catch (Exception ex)
            {
                Data_Good = false;
                Error_Message = $"Error updating breaker data: {ex.Message}";
            }
        }

        /// <summary>
        /// Determine circuit breaker data from manufacturer tables
        /// Based on legacy DetermineCircuitBreakerData() method
        /// </summary>
        private bool DetermineCircuitBreakerData()
        {
            try
            {
                // For now, implement simplified logic without manufacturer database
                // In a full implementation, this would lookup values from CPD_Database

                if (string.IsNullOrEmpty(Schedule_Protective_Device) ||
                    string.IsNullOrEmpty(Schedule_Curve_Type) ||
                    Schedule_Trip_Rating <= 0)
                {
                    return false;
                }

                // Set typical values based on device type and rating
                if (Schedule_Protective_Device == "MCB")
                {
                    // Typical MCB values
                    I2t_Phase = Schedule_Trip_Rating * Schedule_Trip_Rating * 0.01; // Simplified I²t calculation
                    I2t_Earth = I2t_Phase * 0.8; // Earth I²t typically lower
                    kA_Earth = Min_kA_Rating;

                    // EFLI values based on curve type and rating
                    if (Schedule_Curve_Type == "B")
                    {
                        EFLI_04 = 230.0 / (Schedule_Trip_Rating * 5); // 5x for B curve
                        EFLI_50 = 230.0 / (Schedule_Trip_Rating * 3); // 3x for B curve
                    }
                    else if (Schedule_Curve_Type == "C")
                    {
                        EFLI_04 = 230.0 / (Schedule_Trip_Rating * 10); // 10x for C curve
                        EFLI_50 = 230.0 / (Schedule_Trip_Rating * 5); // 5x for C curve
                    }
                    else if (Schedule_Curve_Type == "D")
                    {
                        EFLI_04 = 230.0 / (Schedule_Trip_Rating * 20); // 20x for D curve
                        EFLI_50 = 230.0 / (Schedule_Trip_Rating * 10); // 10x for D curve
                    }
                    else
                    {
                        // Default to C curve values
                        EFLI_04 = 230.0 / (Schedule_Trip_Rating * 10);
                        EFLI_50 = 230.0 / (Schedule_Trip_Rating * 5);
                    }
                }
                else if (Schedule_Protective_Device == "RCBO")
                {
                    // Similar to MCB but with RCD protection
                    I2t_Phase = Schedule_Trip_Rating * Schedule_Trip_Rating * 0.01;
                    I2t_Earth = I2t_Phase * 0.8;
                    kA_Earth = Min_kA_Rating;
                    EFLI_04 = 230.0 / (Schedule_Trip_Rating * 10);
                    EFLI_50 = 230.0 / (Schedule_Trip_Rating * 5);
                }
                else
                {
                    // Default values for unknown device types
                    I2t_Phase = 0;
                    I2t_Earth = 0;
                    kA_Earth = 0;
                    EFLI_04 = 0;
                    EFLI_50 = 0;
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                // If breaker results in error, set values to 0
                I2t_Phase = 0;
                I2t_Earth = 0;
                kA_Earth = 0;
                EFLI_04 = 0;
                EFLI_50 = 0;
                Error_Message = $"Error in DetermineCircuitBreakerData: {ex.Message}";
                return false;
            }
        }
        
        /// <summary>
        /// Initialize default values
        /// </summary>
        private void InitializeDefaults()
        {
            Schedule_Protective_Device = "MCB";
            Schedule_Curve_Type = "C";
            Schedule_Device_Rating = "16A";
            Schedule_RCD_Protection = "None";
            Schedule_Other_Controls = string.Empty;
            
            Device_Rating_Amps = 16.0;
            Device_Curve_Type_Index = 2; // C curve
            Device_kA_Rating = 6.0;
            RCD_Rating_mA = 30.0;
            RCD_Type = "AC";
            RCD_Present = false;
            
            Trip_Time_At_Fault_Current = 0.0;
            Magnetic_Trip_Current = 0.0;
            Thermal_Trip_Current = 0.0;
            
            Can_Discriminate = false;
            Discrimination_Check = "PENDING";
            Device_Valid = "PENDING";
            Data_Good = false;
            Error_Message = string.Empty;

            // Initialize additional properties
            Schedule_Trip_Rating = 16.0;
            Min_kA_Rating = 6.0;
            kA_Rating_Used = 6.0;
            I2t_Phase = 0.0;
            I2t_Earth = 0.0;
            kA_Earth = 0.0;
            EFLI_04 = 0.0;
            EFLI_50 = 0.0;
            EFLI_Max = 0.0;
            Parameters_Good = false;
            Values_Missing = false;
            Is_Spare_Or_Space = false;
        }

        /// <summary>
        /// Load breaker data from device rating string
        /// </summary>
        /// <param name="deviceRating">Device rating string (e.g., "16A")</param>
        public void LoadFromDeviceRating(string deviceRating)
        {
            try
            {
                Schedule_Device_Rating = deviceRating ?? "16A";
                
                // Extract numeric rating
                string numericPart = deviceRating?.Replace("A", "").Replace("a", "").Trim() ?? "16";
                if (double.TryParse(numericPart, out double rating))
                {
                    Device_Rating_Amps = rating;
                    
                    // Set typical magnetic trip current (5-10 times rating for MCB)
                    Magnetic_Trip_Current = rating * 7.0; // Typical for C curve
                    Thermal_Trip_Current = rating * 1.13; // 113% of rating
                }
                
                Data_Good = true;
                Device_Valid = "VALID";
            }
            catch (Exception ex)
            {
                Data_Good = false;
                Error_Message = ex.Message;
                Device_Valid = "ERROR";
            }
        }

        /// <summary>
        /// Validate breaker data
        /// </summary>
        public void ValidateBreaker()
        {
            try
            {
                if (Device_Rating_Amps <= 0)
                {
                    Device_Valid = "INVALID - No rating";
                    Data_Good = false;
                    return;
                }

                if (string.IsNullOrEmpty(Schedule_Protective_Device))
                {
                    Device_Valid = "INVALID - No device type";
                    Data_Good = false;
                    return;
                }

                Device_Valid = "VALID";
                Data_Good = true;
                Error_Message = string.Empty;
            }
            catch (Exception ex)
            {
                Device_Valid = "ERROR";
                Data_Good = false;
                Error_Message = ex.Message;
            }
        }

        /// <summary>
        /// Check discrimination with upstream device
        /// </summary>
        /// <param name="upstreamDevice">Upstream breaker</param>
        public void CheckDiscrimination(PowerBIM_BreakerData upstreamDevice)
        {
            try
            {
                if (upstreamDevice == null)
                {
                    Discrimination_Check = "NO UPSTREAM";
                    Can_Discriminate = false;
                    return;
                }

                // Simple discrimination check - upstream should be higher rated
                if (upstreamDevice.Device_Rating_Amps > Device_Rating_Amps * 1.6)
                {
                    Discrimination_Check = "PASS";
                    Can_Discriminate = true;
                }
                else
                {
                    Discrimination_Check = "FAIL";
                    Can_Discriminate = false;
                }
            }
            catch (Exception ex)
            {
                Discrimination_Check = "ERROR";
                Can_Discriminate = false;
                Error_Message = ex.Message;
            }
        }

        /// <summary>
        /// Find maximum EFLI value
        /// </summary>
        /// <param name="clearingTimeIs5Sec">Whether clearing time is 5 seconds</param>
        /// <returns>Maximum EFLI value</returns>
        public double Find_EFLIMax(bool clearingTimeIs5Sec)
        {
            try
            {
                // Calculate EFLI based on clearing time and device characteristics
                double efliValue;

                if (clearingTimeIs5Sec)
                {
                    // For 5 second clearing time, use thermal trip characteristics
                    efliValue = EFLI_50;
                    if (efliValue <= 0)
                    {
                        // Calculate based on thermal trip current
                        efliValue = (230.0 / Thermal_Trip_Current) * 0.8; // 80% safety factor
                    }
                }
                else
                {
                    // For 0.4 second clearing time, use magnetic trip characteristics
                    efliValue = EFLI_04;
                    if (efliValue <= 0)
                    {
                        // Calculate based on magnetic trip current
                        efliValue = (230.0 / Magnetic_Trip_Current) * 0.8; // 80% safety factor
                    }
                }

                EFLI_Max = efliValue;
                return efliValue;
            }
            catch (Exception ex)
            {
                Error_Message = ex.Message;
                return 0.0;
            }
        }

        /// <summary>
        /// Find maximum short circuit withstand
        /// </summary>
        /// <returns>Maximum SC withstand value</returns>
        public double Find_MaxSCWithstand()
        {
            try
            {
                // Calculate maximum short circuit withstand based on device rating
                double maxSC = Device_kA_Rating;

                // Apply safety factors based on device type
                if (Schedule_Protective_Device?.ToUpper().Contains("MCB") == true)
                {
                    maxSC = Device_kA_Rating * 0.9; // 90% for MCB
                }
                else if (Schedule_Protective_Device?.ToUpper().Contains("MCCB") == true)
                {
                    maxSC = Device_kA_Rating * 0.95; // 95% for MCCB
                }

                return maxSC;
            }
            catch (Exception ex)
            {
                Error_Message = ex.Message;
                return 0.0;
            }
        }

        /// <summary>
        /// Commit all breaker data to Revit
        /// </summary>
        /// <returns>True if successful</returns>
        public bool Commit_AllBreakerDataToRevit()
        {
            try
            {
                // Validate data before committing
                ValidateBreaker();

                if (!Data_Good)
                {
                    Error_Message = "Cannot commit invalid breaker data to Revit";
                    return false;
                }

                // In a real implementation, this would update Revit parameters
                // For now, just mark as successfully committed
                Data_Good = true;
                Error_Message = string.Empty;

                return true;
            }
            catch (Exception ex)
            {
                Error_Message = $"Error committing breaker data to Revit: {ex.Message}";
                Data_Good = false;
                return false;
            }
        }

        /// <summary>
        /// Commit breaker rating only to Revit
        /// </summary>
        /// <returns>True if successful</returns>
        public bool Commit_BreakerRatingOnlyToRevit()
        {
            try
            {
                // Validate rating data
                if (Device_Rating_Amps <= 0)
                {
                    Error_Message = "Invalid device rating - cannot commit to Revit";
                    return false;
                }

                // In a real implementation, this would update only the rating parameter in Revit
                // For now, just mark as successfully committed
                Data_Good = true;
                Error_Message = string.Empty;

                return true;
            }
            catch (Exception ex)
            {
                Error_Message = $"Error committing breaker rating to Revit: {ex.Message}";
                return false;
            }
        }

        #endregion
    }
}
