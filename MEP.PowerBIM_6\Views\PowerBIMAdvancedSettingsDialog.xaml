﻿<Window
    x:Class="MEP.PowerBIM_6.Views.PowerBIMAdvancedSettingsDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Advanced Settings"
    Width="700"
    Height="600"
    Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}"
    ResizeMode="CanResize"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  Converters  -->
            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:NumericFormatConverter x:Key="NumericFormatConverter" />
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <Border
            Grid.Row="0"
            Padding="16,12"
            Background="{DynamicResource PrimaryHueMidBrush}">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon
                    Width="24"
                    Height="24"
                    Margin="0,0,8,0"
                    VerticalAlignment="Center"
                    Foreground="White"
                    Kind="SettingsBox" />
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="16"
                    FontWeight="SemiBold"
                    Foreground="White"
                    Text="PowerBIM Advanced Settings" />
            </StackPanel>
        </Border>

        <!--  Content  -->
        <ScrollViewer
            Grid.Row="1"
            Padding="16"
            VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!--  GPO Calculation Settings  -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock
                            Margin="0,0,0,16"
                            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                            Text="GPO Calculation Settings" />

                        <TextBlock
                            Margin="0,0,0,12"
                            Foreground="{DynamicResource MaterialDesignBodyLight}"
                            Text="Select how GPO (General Power Outlet) circuits should be calculated:"
                            TextWrapping="Wrap" />

                        <StackPanel>
                            <RadioButton
                                Margin="0,0,0,8"
                                Content="Calculate at 80% of breaker rating"
                                IsChecked="{Binding GPOCalc80Percent}" />
                            <RadioButton
                                Margin="0,0,0,8"
                                Content="Use actual assigned load"
                                IsChecked="{Binding GPOActualLoad}" />
                            <RadioButton Content="1000W + 100W per additional outlet" IsChecked="{Binding GPO1000Plus100}" />
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!--  Clearing Time Settings  -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock
                            Margin="0,0,0,16"
                            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                            Text="Clearing Time Settings" />

                        <!--  Power Circuits  -->
                        <GroupBox Margin="0,0,0,16" Header="Power Circuits">
                            <StackPanel Margin="8">
                                <RadioButton
                                    Margin="0,0,0,8"
                                    Content="0.4 seconds clearing time"
                                    IsChecked="{Binding ClearingTimePower04}" />
                                <RadioButton Content="5 seconds clearing time" IsChecked="{Binding ClearingTimePower5}" />
                            </StackPanel>
                        </GroupBox>

                        <!--  Lighting Circuits  -->
                        <GroupBox Header="Lighting Circuits">
                            <StackPanel Margin="8">
                                <RadioButton
                                    Margin="0,0,0,8"
                                    Content="0.4 seconds clearing time"
                                    IsChecked="{Binding ClearingTimeLighting04}" />
                                <RadioButton Content="5 seconds clearing time" IsChecked="{Binding ClearingTimeLighting5}" />
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </materialDesign:Card>

                <!--  Database Settings  -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock
                            Margin="0,0,0,16"
                            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                            Text="Cable Database Settings" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                Grid.Column="0"
                                Margin="0,0,8,0"
                                VerticalAlignment="Center"
                                Text="Database Path:" />
                            <TextBox
                                Grid.Column="1"
                                Margin="0,0,8,0"
                                Background="{DynamicResource MaterialDesignDivider}"
                                IsReadOnly="True"
                                Text="{Binding DatabasePath}" />
                            <Button
                                Grid.Column="2"
                                Click="BrowseDatabase_Click"
                                Content="Browse..." />
                        </Grid>

                        <TextBlock
                            Margin="0,16,0,0"
                            FontStyle="Italic"
                            Foreground="{DynamicResource MaterialDesignBodyLight}"
                            Text="Current database status and information will be displayed here." />
                    </StackPanel>
                </materialDesign:Card>

                <!--  Calculation Parameters  -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock
                            Margin="0,0,0,16"
                            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                            Text="Calculation Parameters" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="Discrimination Test Multiplier:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="0,0,16,8"
                                Text="{Binding DiscriminationTestMultiplier, Converter={StaticResource NumericFormatConverter}}" />

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="2"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="Ambient Temperature (°C):" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="3"
                                Margin="0,0,0,8"
                                Text="{Binding AmbientTemperature, Converter={StaticResource NumericFormatConverter}}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="Node Circuit Path Tolerance (mm):" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0,0,16,8"
                                Text="{Binding NodeCircuitPathTolerance, Converter={StaticResource NumericFormatConverter}}" />

                            <CheckBox
                                Grid.Row="2"
                                Grid.Column="0"
                                Grid.ColumnSpan="4"
                                Margin="0,8,0,0"
                                Content="Enable advanced path finding algorithms"
                                IsChecked="{Binding EnableAdvancedPathFinding}" />
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!--  Safety Factors  -->
                <materialDesign:Card Padding="16">
                    <StackPanel>
                        <TextBlock
                            Margin="0,0,0,16"
                            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                            Text="Safety Factors" />

                        <TextBlock
                            Margin="0,0,0,12"
                            Foreground="{DynamicResource MaterialDesignBodyLight}"
                            Text="Configure safety factors for different circuit types:"
                            TextWrapping="Wrap" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="Power Circuits:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="0,0,16,8"
                                Text="{Binding PowerSafetyFactor, Converter={StaticResource NumericFormatConverter}}" />

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="2"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="Lighting Circuits:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="3"
                                Margin="0,0,0,8"
                                Text="{Binding LightingSafetyFactor, Converter={StaticResource NumericFormatConverter}}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,8,0"
                                VerticalAlignment="Center"
                                Text="Other Circuits:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0,0,16,0"
                                Text="{Binding OtherSafetyFactor, Converter={StaticResource NumericFormatConverter}}" />
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!--  Button Panel  -->
        <Border
            Grid.Row="2"
            Padding="16"
            Background="{DynamicResource MaterialDesignDivider}">
            <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                <Button
                    Margin="0,0,8,0"
                    Click="ResetDefaults_Click"
                    Content="Reset to Defaults"
                    Style="{StaticResource MaterialDesignOutlinedButton}" />
                <Button
                    Margin="0,0,8,0"
                    Click="Save_Click"
                    Content="Save"
                    IsDefault="True" />
                <Button
                    Click="Cancel_Click"
                    Content="Cancel"
                    IsCancel="True" />
            </StackPanel>
        </Border>
    </Grid>
</Window>
