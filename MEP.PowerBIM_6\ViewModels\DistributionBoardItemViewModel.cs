using System;
using CommunityToolkit.Mvvm.ComponentModel;
using MEP.PowerBIM_6.Models;

namespace MEP.PowerBIM_6.ViewModels
{
    /// <summary>
    /// ViewModel for individual distribution board items in the main window
    /// Provides UI-specific properties and behavior for distribution board display
    /// </summary>
    public partial class DistributionBoardItemViewModel : ObservableObject
    {
        #region Fields

        private readonly DistributionBoardModel _distributionBoardModel;

        #endregion

        #region Observable Properties

        /// <summary>
        /// Distribution Board name for display
        /// </summary>
        [ObservableProperty]
        private string _name;

        /// <summary>
        /// Number of circuits in the distribution board
        /// </summary>
        [ObservableProperty]
        private int _circuitCount;

        /// <summary>
        /// Distribution Board status for display
        /// </summary>
        [ObservableProperty]
        private string _status;

        /// <summary>
        /// Indicates if the distribution board is locked
        /// </summary>
        [ObservableProperty]
        private bool _isLocked;

        /// <summary>
        /// Pass count for calculations
        /// </summary>
        [ObservableProperty]
        private int _passCount;

        /// <summary>
        /// Total load for display
        /// </summary>
        [ObservableProperty]
        private double _totalLoad;

        /// <summary>
        /// Formatted total load string
        /// </summary>
        [ObservableProperty]
        private string _totalLoadFormatted;

        /// <summary>
        /// Indicates if there are validation errors
        /// </summary>
        [ObservableProperty]
        private bool _hasErrors;

        /// <summary>
        /// Status color for UI display
        /// </summary>
        [ObservableProperty]
        private string _statusColor;

        #endregion

        #region Properties

        /// <summary>
        /// Get the underlying distribution board model
        /// </summary>
        public DistributionBoardModel DistributionBoardModel => _distributionBoardModel;

        /// <summary>
        /// Get the original PowerBIM_DBData
        /// </summary>
        public PowerBIM_DBData OriginalData => _distributionBoardModel?.OriginalData;

        /// <summary>
        /// Indicates if the distribution board is not locked (for UI binding)
        /// </summary>
        public bool IsNotLocked => !IsLocked;

        /// <summary>
        /// Indicates if the distribution board can be edited
        /// </summary>
        public bool CanEdit => !IsLocked && !HasErrors;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the DistributionBoardItemViewModel
        /// </summary>
        /// <param name="dbData">Original PowerBIM distribution board data</param>
        public DistributionBoardItemViewModel(PowerBIM_DBData dbData)
        {
            if (dbData == null)
                throw new ArgumentNullException(nameof(dbData));

            // Create the distribution board model
            _distributionBoardModel = new DistributionBoardModel(dbData);
            
            // Load properties from the model
            LoadFromModel();
            
            // Subscribe to model changes
            _distributionBoardModel.PropertyChanged += OnDistributionBoardModelPropertyChanged;
        }

        /// <summary>
        /// Initialize the DistributionBoardItemViewModel with a DistributionBoardModel
        /// </summary>
        /// <param name="distributionBoardModel">Distribution Board model</param>
        public DistributionBoardItemViewModel(DistributionBoardModel distributionBoardModel)
        {
            _distributionBoardModel = distributionBoardModel ?? throw new ArgumentNullException(nameof(distributionBoardModel));
            
            // Load properties from the model
            LoadFromModel();
            
            // Subscribe to model changes
            _distributionBoardModel.PropertyChanged += OnDistributionBoardModelPropertyChanged;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Refresh the ViewModel from the underlying model
        /// </summary>
        public void Refresh()
        {
            _distributionBoardModel?.Refresh();
            LoadFromModel();
        }

        /// <summary>
        /// Save changes back to the model
        /// </summary>
        public void SaveChanges()
        {
            if (_distributionBoardModel != null)
            {
                // Update model properties
                _distributionBoardModel.Name = Name;
                _distributionBoardModel.IsLocked = IsLocked;
                
                // Save to original data
                _distributionBoardModel.SaveToOriginalData();
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Load properties from the distribution board model
        /// </summary>
        private void LoadFromModel()
        {
            if (_distributionBoardModel != null)
            {
                Name = _distributionBoardModel.Name;
                CircuitCount = _distributionBoardModel.CircuitCount;
                Status = _distributionBoardModel.Status;
                IsLocked = _distributionBoardModel.IsLocked;
                PassCount = _distributionBoardModel.PassCount;
                TotalLoad = _distributionBoardModel.TotalLoad;
                HasErrors = _distributionBoardModel.HasErrors;
                
                // Update calculated properties
                UpdateCalculatedProperties();
            }
        }

        /// <summary>
        /// Update calculated properties for UI display
        /// </summary>
        private void UpdateCalculatedProperties()
        {
            // Format total load
            TotalLoadFormatted = TotalLoad > 0 ? $"{TotalLoad:N2} VA" : "0 VA";
            
            // Determine status color
            StatusColor = Status switch
            {
                "Calculated" => "Green",
                "Error" => "Red",
                "Locked" => "Orange",
                "Pending" => "Gray",
                _ => "Black"
            };
            
            // Notify dependent properties
            OnPropertyChanged(nameof(IsNotLocked));
            OnPropertyChanged(nameof(CanEdit));
        }

        /// <summary>
        /// Handle property changes from the distribution board model
        /// </summary>
        private void OnDistributionBoardModelPropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // Reload from model when it changes
            LoadFromModel();
        }

        #endregion

        #region Property Change Handlers

        /// <summary>
        /// Handle IsLocked property change
        /// </summary>
        /// <param name="value">New locked state</param>
        partial void OnIsLockedChanged(bool value)
        {
            OnPropertyChanged(nameof(IsNotLocked));
            OnPropertyChanged(nameof(CanEdit));
            UpdateCalculatedProperties();
        }

        /// <summary>
        /// Handle Status property change
        /// </summary>
        /// <param name="value">New status</param>
        partial void OnStatusChanged(string value)
        {
            UpdateCalculatedProperties();
        }

        /// <summary>
        /// Handle TotalLoad property change
        /// </summary>
        /// <param name="value">New total load</param>
        partial void OnTotalLoadChanged(double value)
        {
            UpdateCalculatedProperties();
        }

        /// <summary>
        /// Handle HasErrors property change
        /// </summary>
        /// <param name="value">New error state</param>
        partial void OnHasErrorsChanged(bool value)
        {
            OnPropertyChanged(nameof(CanEdit));
            UpdateCalculatedProperties();
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Dispose of resources
        /// </summary>
        public void Dispose()
        {
            if (_distributionBoardModel != null)
            {
                _distributionBoardModel.PropertyChanged -= OnDistributionBoardModelPropertyChanged;
            }
        }

        #endregion
    }
}
