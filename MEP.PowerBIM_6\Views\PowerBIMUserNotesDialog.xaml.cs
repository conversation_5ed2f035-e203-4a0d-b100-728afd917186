﻿using MEP.PowerBIM_6.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using MessageBox = System.Windows.MessageBox;

namespace MEP.PowerBIM_6.Views
{
    /// <summary>
    /// Interaction logic for PowerBIMUserNotesDialog.xaml
    /// </summary>
    public partial class PowerBIMUserNotesDialog : Window, INotifyPropertyChanged
    {
        #region Private Fields

        private string _userNotes;
        private string _dbName;
        private string _originalNotes;

        #endregion

        #region Public Properties

        public string UserNotes
        {
            get => _userNotes;
            set
            {
                _userNotes = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasChanges));
            }
        }

        public string DBName
        {
            get => _dbName;
            set
            {
                _dbName = value;
                OnPropertyChanged();
            }
        }

        public bool HasChanges => _userNotes != _originalNotes;

        public bool NotesEmpty => string.IsNullOrWhiteSpace(_userNotes);

        #endregion

        public PowerBIMUserNotesDialog(/*PowerBIMDBDataModelEnhanced dbData*/)
        {
            InitializeComponent();

            //DBName = dbData.Schedule_DB_Name;
            //UserNotes = dbData.User_Notes ?? string.Empty;
            //_originalNotes = UserNotes;

            //DataContext = this;

            //// Set dialog result to false initially
            //DialogResult = false;

            //// Focus on the text box
            //txtNotes.Focus();
            //txtNotes.SelectAll();
        }

        #region Event Handlers

        private void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Set dialog result to true to indicate success
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"Error saving notes: {ex.Message}",
                    "PowerBIM Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            // Check if there are unsaved changes
            if (HasChanges)
            {
                var result = MessageBox.Show(
                    "You have unsaved changes. Are you sure you want to cancel?",
                    "Unsaved Changes",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                    return;
            }

            // Set dialog result to false and close
            DialogResult = false;
            Close();
        }

        private void ClearAll_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "Are you sure you want to clear all notes?",
                "Clear Notes",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                UserNotes = string.Empty;
                txtNotes.Focus();
            }
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            // If dialog result is null (closed via X button), check for changes
            if (DialogResult == null && HasChanges)
            {
                var result = MessageBox.Show(
                    "You have unsaved changes. Do you want to save them?",
                    "Unsaved Changes",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question);

                switch (result)
                {
                    case MessageBoxResult.Yes:
                        DialogResult = true;
                        break;
                    case MessageBoxResult.No:
                        DialogResult = false;
                        break;
                    case MessageBoxResult.Cancel:
                        e.Cancel = true;
                        return;
                }
            }

            base.OnClosing(e);
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region Static Methods

        ///// <summary>
        ///// Shows the user notes dialog for the specified distribution board
        ///// </summary>
        ///// <param name="owner">Owner window</param>
        ///// <param name="dbData">Distribution board data</param>
        ///// <returns>True if the user saved changes, false if cancelled</returns>
        //public static bool ShowDialog(Window owner, PowerBIMDBDataModelEnhanced dbData)
        //{
        //    var dialog = new PowerBIMUserNotesDialog(dbData)
        //    {
        //        Owner = owner
        //    };

        //    var result = dialog.ShowDialog() == true;

        //    if (result)
        //    {
        //        // Update the DB data with the new notes
        //        dbData.User_Notes = dialog.UserNotes;
        //    }

        //    return result;
        //}

        #endregion
    }
}
