using System;
using System.Linq;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Models.Enums;
using MEP.PowerBIM_6.Services.Interfaces;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Electrical calculation service for PowerBIM 6
    /// Based on PowerBIM_Calculations from legacy PowerBIM_1.5
    /// All methods are REVIT-SAFE (synchronous execution)
    /// </summary>
    public class CalculationService : ICalculationService
    {
        #region Fields

        private readonly BecaActivityLoggerData _logger;

        #endregion

        #region Constructor

        public CalculationService(BecaActivityLoggerData logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #endregion

        #region Distribution Board Calculations

        public bool RecalculateDistributionBoard(PowerBIM_DBData distributionBoard)
        {
            try
            {
                if (distributionBoard?.CCTs == null)
                {
                    _logger?.Log("Distribution board or circuits collection is null", LogType.Warning);
                    return false;
                }

                // Recalculate all circuits in the distribution board
                foreach (var circuit in distributionBoard.CCTs)
                {
                    RunCircuitCalculations(circuit);
                }

                // Update distribution board summary
                UpdateDistributionBoardSummary(distributionBoard);

                // Calculate diversified loads
                CalculateDiversifiedLoads(distributionBoard);

                _logger?.Log($"Recalculated distribution board: {distributionBoard.Schedule_DB_Name}", LogType.Information);
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to recalculate distribution board: {ex.Message}", LogType.Error);
                return false;
            }
        }

        public bool CalculateDiversifiedLoads(PowerBIM_DBData distributionBoard)
        {
            try
            {
                if (distributionBoard?.CCTs == null) return false;

                double totalPhaseA = 0, totalPhaseB = 0, totalPhaseC = 0;

                foreach (var circuit in distributionBoard.CCTs.Where(c => !c.IsSpareOrSpace))
                {
                    var diversifiedLoad = CalculateDiversifiedCircuitLoad(circuit);
                    
                    // Add to appropriate phase based on circuit number
                    if (circuit.CCT_Number.Contains("R") || circuit.Number_Of_Poles == 1)
                        totalPhaseA += diversifiedLoad;
                    if (circuit.CCT_Number.Contains("W") || circuit.Number_Of_Poles >= 2)
                        totalPhaseB += diversifiedLoad;
                    if (circuit.CCT_Number.Contains("B") || circuit.Number_Of_Poles == 3)
                        totalPhaseC += diversifiedLoad;
                }

                distributionBoard.Diversified_Total_Phase_Current_R = totalPhaseA;
                distributionBoard.Diversified_Total_Phase_Current_B = totalPhaseB;
                distributionBoard.Diversified_Total_Phase_Current_W = totalPhaseC;

                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to calculate diversified loads: {ex.Message}", LogType.Error);
                return false;
            }
        }

        public bool UpdateDistributionBoardSummary(PowerBIM_DBData distributionBoard)
        {
            try
            {
                if (distributionBoard?.CCTs == null) return false;

                int passCount = 0, warningCount = 0, failCount = 0;

                foreach (var circuit in distributionBoard.CCTs.Where(c => !c.IsSpareOrSpace))
                {
                    if (circuit.Check_Pass)
                        passCount++;
                    else
                        failCount++;

                    if (circuit.Warning_Count > 0)
                        warningCount++;
                }

                distributionBoard.PassCount = passCount;
                distributionBoard.WarningCount = warningCount;
                distributionBoard.FailCount = failCount;
                distributionBoard.DB_All_Circuits_Pass = failCount == 0;

                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to update distribution board summary: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region Circuit Calculations

        public bool RunCircuitCalculations(PowerBIM_CircuitData circuit)
        {
            try
            {
                if (circuit == null) return false;

                // Update circuit properties
                UpdateCircuitProperties(circuit);

                // Calculate PowerBIM current
                circuit.CCT_PowerBIM_Current = CalculatePowerBIMCurrent(circuit);

                // Determine clearing time
                circuit.CCT_Clearing_Time = DetermineClearingTime(circuit);

                // Calculate voltage drop
                var vdRule = circuit.CCT_Is_Lighting ? VoltDropCalculation.LinearDeprecating : 
                            circuit.CCT_Is_Power ? VoltDropCalculation.LinearDeprecating : 
                            VoltDropCalculation.LinearDeprecating;

                circuit.CalcRes_Final_Circuit_VD = CalculateFinalCircuitVoltDrop(circuit, vdRule);
                circuit.CalcRes_Final_Circuit_VD_Percentage = CalculateFinalCircuitVoltDropPercentage(
                    circuit.CalcRes_Final_Circuit_VD, circuit.Number_Of_Poles);
                circuit.VoltageDropPercent = circuit.CalcRes_Final_Circuit_VD_Percentage;

                // Calculate EFLI
                CalculateAndValidateEFLI(circuit);

                // Run all validation checks
                RunAllCircuitChecks(circuit);

                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to run circuit calculations: {ex.Message}", LogType.Error);
                return false;
            }
        }

        public bool UpdateCircuitProperties(PowerBIM_CircuitData circuit)
        {
            try
            {
                if (circuit == null) return false;

                // Update cable data
                circuit.Cable_To_First?.UpdateCableData();
                circuit.Cable_To_Final?.UpdateCableData();

                // Update breaker data
                circuit.Breaker?.UpdateBreakerData();

                // Determine circuit type
                circuit.CCT_Is_Power = IsCircuitPower(circuit);
                circuit.CCT_Is_Lighting = IsCircuitLighting(circuit);
                circuit.CCT_Is_Spare_Or_Space = IsCircuitSpareOrSpace(circuit);

                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to update circuit properties: {ex.Message}", LogType.Error);
                return false;
            }
        }

        public double CalculatePowerBIMCurrent(PowerBIM_CircuitData circuit)
        {
            try
            {
                if (circuit == null) return 0;

                double calculatedCurrent = circuit.Revit_Current;

                // If manual current is enabled, use that
                if (circuit.ManualCurrent)
                {
                    return circuit.Manual_PowerBim_User_Current;
                }

                // If GPO is present, calculate based on project settings
                if (circuit.CCT_GPO_Present && circuit.projInfo != null)
                {
                    var gpoCalcPercent = circuit.projInfo.GPO_Calc_Integer;
                    if (gpoCalcPercent >= 0 && gpoCalcPercent <= 100)
                    {
                        calculatedCurrent = circuit.Breaker.Schedule_Trip_Rating * gpoCalcPercent / 100.0;
                    }
                }

                return calculatedCurrent;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to calculate PowerBIM current: {ex.Message}", LogType.Error);
                return 0;
            }
        }

        #endregion

        #region Voltage Drop Calculations

        public double CalculateFinalCircuitVoltDrop(PowerBIM_CircuitData circuit, VoltDropCalculation vdRule)
        {
            try
            {
                if (circuit?.Cable_To_First == null || circuit.Cable_To_Final == null)
                    return 0;

                var current = circuit.GetCurrent();
                var lengthToFirst = circuit.CCT_Length_To_First_Element;
                var totalLength = circuit.CCT_Length;

                if (vdRule == VoltDropCalculation.LumpLoad)
                {
                    // Use lump load calculation
                    return (circuit.Cable_To_First.Z_Operating_Active * lengthToFirst * 0.000001 * 2 * current) +
                           (circuit.Cable_To_Final.Z_Operating_Active * (totalLength - lengthToFirst) * 0.000001 * 2 * current);
                }
                else
                {
                    // Use linear depreciating calculation (default)
                    return (circuit.Cable_To_First.Z_Operating_Active * lengthToFirst * 0.000001 * 2 * current) +
                           ((circuit.Cable_To_Final.Z_Operating_Active * (totalLength - lengthToFirst) * 0.000001 * 2 * current) / 2);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to calculate voltage drop: {ex.Message}", LogType.Error);
                return 0;
            }
        }

        public double CalculateFinalCircuitVoltDropPercentage(double voltDrop, int numberOfPoles)
        {
            try
            {
                if (numberOfPoles == 3)
                    return voltDrop * 0.866 / 400; // Three phase
                else
                    return voltDrop / 230; // Single phase
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to calculate voltage drop percentage: {ex.Message}", LogType.Error);
                return 0;
            }
        }

        public bool ValidateCircuitVoltDrop(double calculatedVD, double maxVD)
        {
            return calculatedVD <= maxVD;
        }

        public bool ValidateSystemVoltDrop(double circuitVD, double dbVD, double systemMaxVD)
        {
            return (circuitVD + dbVD) <= systemMaxVD;
        }

        #endregion

        #region EFLI Calculations

        public bool CalculateAndValidateEFLI(PowerBIM_CircuitData circuit)
        {
            try
            {
                if (circuit?.Breaker == null || circuit.DB == null) return false;

                // Find EFLI Max based on clearing time
                if (circuit.CCT_Clearing_Time == 400) // 0.4s clearing time
                    circuit.Breaker.Find_EFLIMax(false);
                else if (circuit.CCT_Clearing_Time == 5000) // 5s clearing time
                    circuit.Breaker.Find_EFLIMax(true);
                else
                    circuit.Breaker.EFLI_Max = 0;

                // Calculate circuit EFLI
                if (circuit.Breaker.Schedule_Protective_Device == "MCB" &&
                    (circuit.Schedule_RCD?.ToUpper().Contains("LOCAL") ?? false))
                {
                    circuit.CalcRes_Total_EFLi = CalculateEFLI_ToFirst(circuit, circuit.DB.EFLI_R, circuit.DB.EFLI_X);
                }
                else
                {
                    circuit.CalcRes_Total_EFLi = CalculateEFLI(circuit, circuit.DB.EFLI_R, circuit.DB.EFLI_X);
                }

                // Validate EFLI
                return circuit.CalcRes_Total_EFLi <= circuit.Breaker.EFLI_Max;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to calculate and validate EFLI: {ex.Message}", LogType.Error);
                return false;
            }
        }

        public double CalculateEFLI(PowerBIM_CircuitData circuit, double dbEFLI_R, double dbEFLI_X)
        {
            try
            {
                if (circuit?.Cable_To_First == null || circuit.Cable_To_Final == null)
                    return 0;

                // EFLI = DB EFLI + Cable EFLI
                var cableEFLI_R = (circuit.Cable_To_First.R_Operating_Earth * circuit.CCT_Length_To_First_Element / 1000) +
                                 (circuit.Cable_To_Final.R_Operating_Earth * (circuit.CCT_Length - circuit.CCT_Length_To_First_Element) / 1000);

                var cableEFLI_X = (circuit.Cable_To_First.X_Max_Earth * circuit.CCT_Length_To_First_Element / 1000) +
                                 (circuit.Cable_To_Final.X_Max_Earth * (circuit.CCT_Length - circuit.CCT_Length_To_First_Element) / 1000);

                var totalR = dbEFLI_R + cableEFLI_R;
                var totalX = dbEFLI_X + cableEFLI_X;

                return Math.Sqrt((totalR * totalR) + (totalX * totalX));
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to calculate EFLI: {ex.Message}", LogType.Error);
                return 0;
            }
        }

        public double CalculateEFLI_ToFirst(PowerBIM_CircuitData circuit, double dbEFLI_R, double dbEFLI_X)
        {
            try
            {
                if (circuit?.Cable_To_First == null) return 0;

                // EFLI to first element only (for local RCD protection)
                var cableEFLI_R = circuit.Cable_To_First.R_Operating_Earth * circuit.CCT_Length_To_First_Element / 1000;
                var cableEFLI_X = circuit.Cable_To_First.X_Max_Earth * circuit.CCT_Length_To_First_Element / 1000;

                var totalR = dbEFLI_R + cableEFLI_R;
                var totalX = dbEFLI_X + cableEFLI_X;

                return Math.Sqrt((totalR * totalR) + (totalX * totalX));
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to calculate EFLI to first: {ex.Message}", LogType.Error);
                return 0;
            }
        }

        #endregion

        #region Circuit Validation

        public bool RunAllCircuitChecks(PowerBIM_CircuitData circuit)
        {
            try
            {
                if (circuit == null) return false;

                int passCount = CountPassingChecks(circuit);
                circuit.Check_Pass = passCount >= 6; // Assuming 8 total checks, need at least 6 to pass

                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to run circuit checks: {ex.Message}", LogType.Error);
                return false;
            }
        }

        public int CountPassingChecks(PowerBIM_CircuitData circuit)
        {
            try
            {
                if (circuit == null) return 0;

                int count = 0;

                // Basic data check
                count += (!string.IsNullOrEmpty(circuit.CCT_Number) && !string.IsNullOrEmpty(circuit.Schedule_Description)) ? 1 : 0;

                // Cable to first check
                count += (circuit.Cable_To_First?.Current_Carrying_Capacity >= circuit.GetCurrent()) ? 1 : 0;

                // Cable to final check
                count += (circuit.Cable_To_Final?.Current_Carrying_Capacity >= circuit.GetCurrent()) ? 1 : 0;

                // Voltage drop check
                count += ValidateCircuitVoltDrop(circuit.VoltageDropPercent, 5.0) ? 1 : 0;

                // EFLI check
                count += (circuit.CalcRes_Total_EFLi <= circuit.Breaker?.EFLI_Max) ? 1 : 0;

                // Overload current check
                count += (circuit.Breaker?.Schedule_Trip_Rating >= circuit.GetCurrent()) ? 1 : 0;

                return count;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to count passing checks: {ex.Message}", LogType.Error);
                return 0;
            }
        }

        #endregion

        #region Cable and Breaker Calculations

        public bool UpdateCableCalculations(PowerBIM_CableData cable, double current, double length)
        {
            try
            {
                if (cable == null) return false;

                cable.CalculateVoltageDrop(current, length);
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to update cable calculations: {ex.Message}", LogType.Error);
                return false;
            }
        }

        public bool UpdateBreakerCalculations(PowerBIM_BreakerData breaker)
        {
            try
            {
                if (breaker == null) return false;

                breaker.UpdateBreakerData();
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to update breaker calculations: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region Timing and Diversity Calculations

        public double DetermineClearingTime(PowerBIM_CircuitData circuit)
        {
            try
            {
                if (circuit == null) return 400; // Default to 0.4s

                // Logic based on circuit type and project settings
                if (circuit.CCT_Is_Lighting)
                {
                    return circuit.projInfo?.Clearing_Time_Lighting ?? 400;
                }
                else if (circuit.CCT_Is_Power)
                {
                    return circuit.projInfo?.Clearing_Time_Power ?? 400;
                }
                else
                {
                    return 400; // Default for other circuits
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to determine clearing time: {ex.Message}", LogType.Error);
                return 400;
            }
        }

        public double CalculateDiversifiedCircuitLoad(PowerBIM_CircuitData circuit)
        {
            try
            {
                if (circuit == null) return 0;

                var baseCurrent = circuit.GetCurrent();
                var diversityFactor = circuit.Schedule_Diversity;

                return baseCurrent * diversityFactor;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to calculate diversified circuit load: {ex.Message}", LogType.Error);
                return 0;
            }
        }

        #endregion

        #region Private Helper Methods

        private bool IsCircuitPower(PowerBIM_CircuitData circuit)
        {
            if (circuit?.Schedule_Description == null) return false;
            var desc = circuit.Schedule_Description.ToLower();
            return desc.Contains("power") || desc.Contains("socket") || desc.Contains("gpo");
        }

        private bool IsCircuitLighting(PowerBIM_CircuitData circuit)
        {
            if (circuit?.Schedule_Description == null) return false;
            var desc = circuit.Schedule_Description.ToLower();
            return desc.Contains("light") || desc.Contains("lamp");
        }

        private bool IsCircuitSpareOrSpace(PowerBIM_CircuitData circuit)
        {
            if (circuit?.Schedule_Description == null) return false;
            var desc = circuit.Schedule_Description.ToLower();
            return desc.Contains("spare") || desc.Contains("space");
        }

        #endregion
    }
}
