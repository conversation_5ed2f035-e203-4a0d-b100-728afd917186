using System;
using System.Linq;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Independent PowerBIM Circuit Data class for MEP.PowerBIM_6
    /// Contains all circuit information and electrical calculations
    /// </summary>
    public class PowerBIM_CircuitData
    {
        #region Properties

        /// <summary>
        /// Project information reference
        /// </summary>
        public PowerBIM_ProjectInfo projInfo { get; set; }

        /// <summary>
        /// Distribution board reference
        /// </summary>
        public PowerBIM_DBData DB { get; set; }

        /// <summary>
        /// Revit electrical system element
        /// </summary>
        public ElectricalSystem CCT_Electrical_System { get; set; }

        /// <summary>
        /// Circuit element
        /// </summary>
        public Element CCT_Element { get; set; }

        /// <summary>
        /// Circuit number
        /// </summary>
        public string CCT_Number { get; set; }

        /// <summary>
        /// Circuit description from schedule
        /// </summary>
        public string Schedule_Description { get; set; }

        /// <summary>
        /// Current from Revit
        /// </summary>
        public double Revit_Current { get; set; }

        /// <summary>
        /// Indicates if current is manually set
        /// </summary>
        public bool ManualCurrent { get; set; }

        /// <summary>
        /// Manual current value set by user
        /// </summary>
        public double Manual_PowerBim_User_Current { get; set; }

        /// <summary>
        /// PowerBIM calculated current
        /// </summary>
        public double CCT_PowerBIM_Current { get; set; }

        /// <summary>
        /// Number of poles (1 for single phase, 3 for three phase)
        /// </summary>
        public int Number_Of_Poles { get; set; }

        /// <summary>
        /// Number of elements in the circuit
        /// </summary>
        public int Number_Of_Elements { get; set; }

        /// <summary>
        /// Device rating from schedule
        /// </summary>
        public string Schedule_Device_Rating { get; set; }

        /// <summary>
        /// Device curve type from schedule
        /// </summary>
        public string Schedule_Device_Curve_Type { get; set; }

        /// <summary>
        /// Protection device type from schedule
        /// </summary>
        public string Schedule_Protection_Device { get; set; }

        /// <summary>
        /// RCD protection from schedule
        /// </summary>
        public string Schedule_RCD_Protection { get; set; }

        /// <summary>
        /// RCD protection (alias for Schedule_RCD_Protection)
        /// Based on legacy Schedule_RCD property
        /// </summary>
        public string Schedule_RCD { get; set; }

        /// <summary>
        /// Other controls from schedule
        /// </summary>
        public string Schedule_Other_Controls { get; set; }

        /// <summary>
        /// Cable to first element from schedule
        /// </summary>
        public string Schedule_Cable_To_First { get; set; }

        /// <summary>
        /// Cable to final element from schedule
        /// </summary>
        public string Schedule_Cable_To_Final { get; set; }

        /// <summary>
        /// Cable installation method from schedule
        /// </summary>
        public string Schedule_Cable_Installation_Method { get; set; }

        /// <summary>
        /// Derating factor from schedule
        /// </summary>
        public double Schedule_Derating_Factor { get; set; }

        /// <summary>
        /// Diversity factor from schedule
        /// </summary>
        public double Schedule_Diversity { get; set; }

        /// <summary>
        /// Diversity factor (alias for Schedule_Diversity)
        /// </summary>
        public double Diversity => Schedule_Diversity;

        /// <summary>
        /// Circuit length to first element
        /// </summary>
        public double CCT_Length_To_First_Element { get; set; }

        /// <summary>
        /// Total circuit length
        /// </summary>
        public double CCT_Length { get; set; }

        /// <summary>
        /// Indicates if circuit is spare or space
        /// </summary>
        public bool CCT_Is_Spare_Or_Space { get; set; }

        /// <summary>
        /// Alias for CCT_Is_Spare_Or_Space
        /// </summary>
        public bool IsSpareOrSpace => CCT_Is_Spare_Or_Space;

        /// <summary>
        /// Indicates if circuit is power type
        /// </summary>
        public bool CCT_Is_Power { get; set; }

        /// <summary>
        /// Indicates if circuit is lighting type
        /// </summary>
        public bool CCT_Is_Lighting { get; set; }

        /// <summary>
        /// Indicates if circuit has GPO
        /// </summary>
        public bool CCT_GPO_Present { get; set; }

        /// <summary>
        /// Indicates if circuit is locked
        /// </summary>
        public bool IsLocked { get; set; }

        /// <summary>
        /// Indicates if circuit is manually overridden
        /// </summary>
        public bool Manual { get; set; }

        /// <summary>
        /// Load value for calculations
        /// </summary>
        public double Load { get; set; }

        /// <summary>
        /// Indicates if circuit passes validation
        /// </summary>
        public bool Check_Pass { get; set; }

        /// <summary>
        /// Number of warnings for this circuit
        /// </summary>
        public int Warning_Count { get; set; }

        /// <summary>
        /// Trip rating check result
        /// </summary>
        public string Check_Trip_Rating { get; set; }

        /// <summary>
        /// Cable 1 validation result
        /// </summary>
        public string Cable_1_Valid { get; set; }

        /// <summary>
        /// Cable 2 validation result
        /// </summary>
        public string Cable_2_Valid { get; set; }

        /// <summary>
        /// CPD discrimination check result
        /// </summary>
        public string Check_CPD_Descriminates { get; set; }

        /// <summary>
        /// Load current check result
        /// </summary>
        public string Check_Load_Current { get; set; }

        /// <summary>
        /// Cable 1 current check result
        /// </summary>
        public string Check_Cable_1_Current { get; set; }

        /// <summary>
        /// Cable 2 current check result
        /// </summary>
        public string Check_Cable_2_Current { get; set; }

        /// <summary>
        /// Cable 1 voltage drop percentage check result
        /// </summary>
        public string Check_Cable_1_Voltage_Drop_Percent { get; set; }

        /// <summary>
        /// Cable 2 voltage drop percentage check result
        /// </summary>
        public string Check_Cable_2_Voltage_Drop_Percent { get; set; }

        /// <summary>
        /// Cable 1 short circuit withstand check result
        /// </summary>
        public string Check_Cable_1_SC_Withstand { get; set; }

        /// <summary>
        /// Cable 2 short circuit withstand check result
        /// </summary>
        public string Check_Cable_2_SC_Withstand { get; set; }

        /// <summary>
        /// Circuit check summary
        /// </summary>
        public string Circuit_Check_Summary { get; set; }

        /// <summary>
        /// Circuit check result
        /// </summary>
        public string Circuit_Check_Result { get; set; }

        /// <summary>
        /// Circuit revision information
        /// </summary>
        public string Circuit_Revision { get; set; }

        /// <summary>
        /// Indicates if circuit length is manually set
        /// </summary>
        public bool CircuitLengthIsManual { get; set; }

        /// <summary>
        /// Circuit path mode (Automatic, Manual, etc.)
        /// Based on Revit's ElectricalCircuitPathMode
        /// </summary>
        public string Circuit_Path_Mode { get; set; }

        /// <summary>
        /// Install method index
        /// </summary>
        public int Install_Method_Index { get; set; }

        /// <summary>
        /// Voltage drop percentage
        /// </summary>
        public double VoltageDropPercent { get; set; }

        /// <summary>
        /// Calculated final circuit voltage drop (in volts)
        /// Based on legacy CalcRes_Final_Circuit_VD
        /// </summary>
        public double CalcRes_Final_Circuit_VD { get; set; }

        /// <summary>
        /// Calculated final circuit voltage drop percentage
        /// Based on legacy CalcRes_Final_Circuit_VD_Percentage
        /// </summary>
        public double CalcRes_Final_Circuit_VD_Percentage { get; set; }

        /// <summary>
        /// Calculated total EFLI (Earth Fault Loop Impedance)
        /// Based on legacy CalcRes_Total_EFLi
        /// </summary>
        public double CalcRes_Total_EFLi { get; set; }

        /// <summary>
        /// Undiversified current phase A
        /// </summary>
        public double CCT_Undiversified_Current_Phase_A { get; set; }

        /// <summary>
        /// Undiversified current phase B
        /// </summary>
        public double CCT_Undiversified_Current_Phase_B { get; set; }

        /// <summary>
        /// Undiversified current phase C
        /// </summary>
        public double CCT_Undiversified_Current_Phase_C { get; set; }

        /// <summary>
        /// Indicates if RCD element is present
        /// </summary>
        public bool CCT_RCD_ElementIsPresent { get; set; }

        /// <summary>
        /// RCD name
        /// </summary>
        public string CCT_RCD_Name { get; set; }

        /// <summary>
        /// GPO count
        /// </summary>
        public int GPO_Count { get; set; }

        /// <summary>
        /// Circuit clearing time
        /// </summary>
        public double CCT_Clearing_Time { get; set; }

        /// <summary>
        /// Indicates if data is good
        /// </summary>
        public bool Data_Good { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        public string Error_Message { get; set; }

        /// <summary>
        /// Indicates if parameters are good
        /// </summary>
        public bool Parameters_Good { get; set; }

        /// <summary>
        /// Indicates if values are missing
        /// </summary>
        public bool Values_Missing { get; set; }

        /// <summary>
        /// Cable to first element
        /// </summary>
        public PowerBIM_CableData Cable_To_First { get; set; }

        /// <summary>
        /// Cable to final element
        /// </summary>
        public PowerBIM_CableData Cable_To_Final { get; set; }

        /// <summary>
        /// Breaker data
        /// </summary>
        public PowerBIM_BreakerData Breaker { get; set; }

        /// <summary>
        /// Indicates if there are unsaved changes
        /// </summary>
        public bool HasUnsavedChanges { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor
        /// </summary>
        public PowerBIM_CircuitData()
        {
            InitializeDefaults();
        }

        /// <summary>
        /// Constructor with project info, distribution board, and electrical system
        /// </summary>
        /// <param name="projectInfo">Project information</param>
        /// <param name="distributionBoard">Distribution board</param>
        /// <param name="electricalSystem">Electrical system</param>
        public PowerBIM_CircuitData(PowerBIM_ProjectInfo projectInfo, PowerBIM_DBData distributionBoard, ElectricalSystem electricalSystem)
        {
            projInfo = projectInfo;
            DB = distributionBoard;
            CCT_Electrical_System = electricalSystem;
            
            InitializeDefaults();
            LoadCircuitData();
        }

        #endregion

        #region Methods

        /// <summary>
        /// Initialize default values
        /// </summary>
        private void InitializeDefaults()
        {
            CCT_Number = "1";
            Schedule_Description = "Circuit";
            Schedule_Device_Rating = "16A";
            Schedule_Device_Curve_Type = "C";
            Schedule_Protection_Device = "MCB";
            Schedule_RCD_Protection = "None";
            Schedule_RCD = "None";
            Schedule_Other_Controls = string.Empty;
            Schedule_Cable_To_First = "2.5mm²";
            Schedule_Cable_To_Final = "2.5mm²";
            Schedule_Cable_Installation_Method = "Clipped Direct";
            Schedule_Derating_Factor = 1.0;
            Schedule_Diversity = 1.0;
            
            Number_Of_Poles = 1;
            Number_Of_Elements = 1;
            
            Revit_Current = 0;
            Manual_PowerBim_User_Current = 0;
            CCT_PowerBIM_Current = 0;
            CCT_Length_To_First_Element = 0;
            CCT_Length = 0;
            Load = 0;
            
            ManualCurrent = false;
            CCT_Is_Spare_Or_Space = false;
            CCT_Is_Power = false;
            CCT_Is_Lighting = false;
            CCT_GPO_Present = false;
            IsLocked = false;
            Manual = false;
            Check_Pass = false;
            Warning_Count = 0;
            
            // Initialize check results
            Check_Trip_Rating = string.Empty;
            Cable_1_Valid = string.Empty;
            Cable_2_Valid = string.Empty;
            Check_CPD_Descriminates = string.Empty;
            Check_Load_Current = string.Empty;
            Check_Cable_1_Current = string.Empty;
            Check_Cable_2_Current = string.Empty;
            Check_Cable_1_Voltage_Drop_Percent = string.Empty;
            Check_Cable_2_Voltage_Drop_Percent = string.Empty;
            Check_Cable_1_SC_Withstand = string.Empty;
            Check_Cable_2_SC_Withstand = string.Empty;
            Circuit_Check_Summary = string.Empty;
            Circuit_Check_Result = "PENDING";
            Circuit_Revision = string.Empty;

            // Initialize additional properties
            CircuitLengthIsManual = false;
            Circuit_Path_Mode = "Automatic"; // Default path mode
            Install_Method_Index = 0;
            VoltageDropPercent = 0.0;
            CalcRes_Final_Circuit_VD = 0.0;
            CalcRes_Final_Circuit_VD_Percentage = 0.0;
            CalcRes_Total_EFLi = 0.0;
            CCT_Undiversified_Current_Phase_A = 0.0;
            CCT_Undiversified_Current_Phase_B = 0.0;
            CCT_Undiversified_Current_Phase_C = 0.0;
            CCT_RCD_ElementIsPresent = false;
            CCT_RCD_Name = string.Empty;
            GPO_Count = 0;
            CCT_Clearing_Time = 0.0;
            Data_Good = false;
            Error_Message = string.Empty;
            Parameters_Good = false;
            Values_Missing = false;

            // Initialize cable and breaker objects
            Cable_To_First = new PowerBIM_CableData(Schedule_Cable_To_First);
            Cable_To_Final = new PowerBIM_CableData(Schedule_Cable_To_Final);
            Breaker = new PowerBIM_BreakerData();
        }

        /// <summary>
        /// Load circuit data from Revit electrical system
        /// </summary>
        private void LoadCircuitData()
        {
            if (CCT_Electrical_System == null) return;

            try
            {
                // Get basic circuit information
                CCT_Number = GetParameterValue(BuiltInParameter.RBS_ELEC_CIRCUIT_NUMBER) ?? "1";
                Schedule_Description = GetParameterValue(BuiltInParameter.ALL_MODEL_DESCRIPTION) ?? "Circuit";
                
                // Get electrical properties
                if (double.TryParse(GetParameterValue(BuiltInParameter.RBS_ELEC_APPARENT_CURRENT_PARAM), out double current))
                {
                    Revit_Current = current;
                    CCT_PowerBIM_Current = current;
                }

                if (double.TryParse(GetParameterValue(BuiltInParameter.RBS_ELEC_APPARENT_LOAD), out double load))
                {
                    Load = load;
                }

                // Get number of poles
                if (int.TryParse(GetParameterValue(BuiltInParameter.RBS_ELEC_NUMBER_OF_POLES), out int poles))
                {
                    Number_Of_Poles = poles;
                }

                // Get circuit length
                if (double.TryParse(GetParameterValue(BuiltInParameter.RBS_ELEC_CIRCUIT_LENGTH_PARAM), out double length))
                {
                    CCT_Length = length;
                    CCT_Length_To_First_Element = length; // Simplified for now
                }

                // Get circuit path mode from electrical system
                if (CCT_Electrical_System != null)
                {
                    Circuit_Path_Mode = CCT_Electrical_System.CircuitPathMode.ToString();
                }

                // Load schedule parameters
                LoadScheduleParameters();

                // Load additional circuit properties
                LoadAdditionalProperties();

                // Determine circuit type
                DetermineCircuitType();

                // Run initial PowerBIM check
                RunPowerBIMCheck();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading circuit data: {ex.Message}");
            }
        }

        /// <summary>
        /// Load additional circuit properties
        /// </summary>
        private void LoadAdditionalProperties()
        {
            try
            {
                // Load circuit length manual flag
                string manualLength = GetParameterValue("Beca_Circuit_Length_Manual");
                CircuitLengthIsManual = manualLength == "1" || manualLength?.ToLower() == "true";

                // Load install method index
                if (int.TryParse(GetParameterValue("Install Method Index"), out int installIndex))
                {
                    Install_Method_Index = installIndex;
                }

                // Load voltage drop percentage
                if (double.TryParse(GetParameterValue("Voltage Drop Percent"), out double vdPercent))
                {
                    VoltageDropPercent = vdPercent;
                }

                // Load undiversified currents
                if (double.TryParse(GetParameterValue("Undiversified Current Phase A"), out double currentA))
                {
                    CCT_Undiversified_Current_Phase_A = currentA;
                }

                if (double.TryParse(GetParameterValue("Undiversified Current Phase B"), out double currentB))
                {
                    CCT_Undiversified_Current_Phase_B = currentB;
                }

                if (double.TryParse(GetParameterValue("Undiversified Current Phase C"), out double currentC))
                {
                    CCT_Undiversified_Current_Phase_C = currentC;
                }

                // Load RCD information
                string rcdPresent = GetParameterValue("RCD Present");
                CCT_RCD_ElementIsPresent = rcdPresent == "1" || rcdPresent?.ToLower() == "true";
                CCT_RCD_Name = GetParameterValue("RCD Name") ?? string.Empty;

                // Load GPO count
                if (int.TryParse(GetParameterValue("GPO Count"), out int gpoCount))
                {
                    GPO_Count = gpoCount;
                }

                // Load clearing time
                if (double.TryParse(GetParameterValue("Clearing Time"), out double clearingTime))
                {
                    CCT_Clearing_Time = clearingTime;
                }

                // Set data quality flags
                Data_Good = true;
                Parameters_Good = true;
                Values_Missing = false;
                Error_Message = string.Empty;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading additional properties: {ex.Message}");
                Data_Good = false;
                Parameters_Good = false;
                Values_Missing = true;
                Error_Message = ex.Message;
            }
        }

        /// <summary>
        /// Load schedule parameters from circuit
        /// </summary>
        private void LoadScheduleParameters()
        {
            try
            {
                // Load custom schedule parameters if they exist
                Schedule_Device_Rating = GetParameterValue("Device Rating") ?? Schedule_Device_Rating;
                Schedule_Device_Curve_Type = GetParameterValue("Device Curve Type") ?? Schedule_Device_Curve_Type;
                Schedule_Protection_Device = GetParameterValue("Protection Device") ?? Schedule_Protection_Device;
                Schedule_RCD_Protection = GetParameterValue("RCD Protection") ?? Schedule_RCD_Protection;
                Schedule_RCD = Schedule_RCD_Protection; // Keep both properties in sync
                Schedule_Other_Controls = GetParameterValue("Other Controls") ?? Schedule_Other_Controls;
                Schedule_Cable_To_First = GetParameterValue("Cable To First") ?? Schedule_Cable_To_First;
                Schedule_Cable_To_Final = GetParameterValue("Cable To Final") ?? Schedule_Cable_To_Final;
                Schedule_Cable_Installation_Method = GetParameterValue("Installation Method") ?? Schedule_Cable_Installation_Method;
                
                if (double.TryParse(GetParameterValue("Derating Factor"), out double derating))
                {
                    Schedule_Derating_Factor = derating;
                }
                
                if (double.TryParse(GetParameterValue("Diversity"), out double diversity))
                {
                    Schedule_Diversity = diversity;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading schedule parameters: {ex.Message}");
            }
        }

        /// <summary>
        /// Get parameter value from electrical system
        /// </summary>
        /// <param name="paramId">Built-in parameter ID</param>
        /// <returns>Parameter value as string</returns>
        private string GetParameterValue(BuiltInParameter paramId)
        {
            try
            {
                Parameter param = CCT_Electrical_System?.get_Parameter(paramId);
                if (param != null && param.HasValue)
                {
                    switch (param.StorageType)
                    {
                        case StorageType.String:
                            return param.AsString();
                        case StorageType.Integer:
                            return param.AsInteger().ToString();
                        case StorageType.Double:
                            return param.AsDouble().ToString();
                        default:
                            return param.AsValueString();
                    }
                }
            }
            catch
            {
                // Ignore parameter read errors
            }
            
            return null;
        }

        /// <summary>
        /// Get parameter value by name from electrical system
        /// </summary>
        /// <param name="parameterName">Parameter name</param>
        /// <returns>Parameter value as string</returns>
        private string GetParameterValue(string parameterName)
        {
            try
            {
                Parameter param = CCT_Electrical_System?.LookupParameter(parameterName);
                if (param != null && param.HasValue)
                {
                    switch (param.StorageType)
                    {
                        case StorageType.String:
                            return param.AsString();
                        case StorageType.Integer:
                            return param.AsInteger().ToString();
                        case StorageType.Double:
                            return param.AsDouble().ToString();
                        default:
                            return param.AsValueString();
                    }
                }
            }
            catch
            {
                // Ignore parameter read errors
            }
            
            return null;
        }

        /// <summary>
        /// Determine circuit type based on connected elements
        /// </summary>
        private void DetermineCircuitType()
        {
            try
            {
                if (CCT_Electrical_System?.Elements != null)
                {
                    var elements = CCT_Electrical_System.Elements.Cast<ElementId>()
                        .Select(id => projInfo.Document.GetElement(id))
                        .Where(elem => elem != null);

                    Number_Of_Elements = elements.Count();

                    // Determine if lighting or power based on connected elements
                    foreach (var element in elements)
                    {
                        if (element.Category?.Name?.ToLower().Contains("lighting") == true)
                        {
                            CCT_Is_Lighting = true;
                        }
                        else if (element.Category?.Name?.ToLower().Contains("electrical") == true)
                        {
                            CCT_Is_Power = true;
                        }
                    }

                    // Check for spare/space
                    if (Schedule_Description?.ToLower().Contains("spare") == true ||
                        Schedule_Description?.ToLower().Contains("space") == true)
                    {
                        CCT_Is_Spare_Or_Space = true;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error determining circuit type: {ex.Message}");
            }
        }

        /// <summary>
        /// Get current value for calculations
        /// </summary>
        /// <returns>Current value</returns>
        public double GetCurrent()
        {
            if (ManualCurrent)
            {
                return Manual_PowerBim_User_Current;
            }
            else if (CCT_PowerBIM_Current > 0)
            {
                return CCT_PowerBIM_Current;
            }
            else
            {
                return Revit_Current;
            }
        }

        /// <summary>
        /// Run PowerBIM validation check
        /// </summary>
        public void RunPowerBIMCheck()
        {
            try
            {
                Warning_Count = 0;
                Check_Pass = true;

                // Basic validation checks
                if (CCT_Is_Spare_Or_Space)
                {
                    Circuit_Check_Result = "SPARE/SPACE";
                    Circuit_Check_Summary = "Spare or space circuit";
                    return;
                }

                // Check current
                double current = GetCurrent();
                if (current <= 0)
                {
                    Check_Load_Current = "FAIL - No current";
                    Check_Pass = false;
                }
                else
                {
                    Check_Load_Current = "PASS";
                }

                // Check device rating vs current
                if (double.TryParse(Schedule_Device_Rating?.Replace("A", ""), out double deviceRating))
                {
                    if (current > deviceRating)
                    {
                        Check_Trip_Rating = "FAIL - Current exceeds device rating";
                        Check_Pass = false;
                    }
                    else
                    {
                        Check_Trip_Rating = "PASS";
                    }
                }

                // Check cables
                if (!string.IsNullOrEmpty(Schedule_Cable_To_First))
                {
                    Cable_1_Valid = "PASS"; // Simplified validation
                }

                if (!string.IsNullOrEmpty(Schedule_Cable_To_Final))
                {
                    Cable_2_Valid = "PASS"; // Simplified validation
                }

                // Set overall result
                if (Check_Pass)
                {
                    Circuit_Check_Result = "PASS";
                    Circuit_Check_Summary = "All checks passed";
                }
                else
                {
                    Circuit_Check_Result = "FAIL";
                    Circuit_Check_Summary = "One or more checks failed";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error running PowerBIM check: {ex.Message}");
                Circuit_Check_Result = "ERROR";
                Circuit_Check_Summary = "Error during validation";
                Check_Pass = false;
            }
        }

        /// <summary>
        /// Refresh derived circuit properties
        /// </summary>
        public void Refresh_DerrivedCircuitProperties()
        {
            // Recalculate derived properties
            CCT_PowerBIM_Current = GetCurrent();
            
            // Re-run validation
            RunPowerBIMCheck();
        }

        /// <summary>
        /// Set circuit length to Revit (placeholder for future implementation)
        /// </summary>
        public void SetCircuitLengthToRevit()
        {
            // Placeholder - would update Revit parameters in real implementation
        }

        /// <summary>
        /// Commit circuit data to Revit
        /// </summary>
        /// <returns>True if successful</returns>
        public bool CommitToRevit()
        {
            try
            {
                // In a real implementation, this would commit all circuit data to Revit parameters
                // For now, just mark as committed and clear unsaved changes flag
                HasUnsavedChanges = false;
                Data_Good = true;
                Error_Message = string.Empty;

                return true;
            }
            catch (Exception ex)
            {
                Error_Message = $"Error committing circuit data to Revit: {ex.Message}";
                Data_Good = false;
                return false;
            }
        }

        #endregion
    }
}
