using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Services;
using MEP.PowerBIM_6.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace MEP.PowerBIM_6.Handlers
{
    /// <summary>
    /// Enhanced request handler for PowerBIM 6 WPF application
    /// Implements IExternalEventHandler to safely execute Revit API operations
    /// Uses dependency injection for clean service access
    /// </summary>
    public class RequestHandler_PB6 : IExternalEventHandler
    {
        #region Fields

        private readonly IServiceProvider _serviceProvider;
        private readonly BecaActivityLoggerData _logger;
        private readonly Request_PB6_Configure _request;

        // Specialized handlers for complex operations (to be implemented)
        // private readonly EditCircuitPathClicker_WPF _circuitPathClicker;
        // private readonly EditDBPathClicker_WPF _dbPathClicker;

        #endregion

        #region Properties

        /// <summary>
        /// Get the request configuration object
        /// </summary>
        public Request_PB6_Configure Request => _request;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the request handler with dependency injection
        /// </summary>
        /// <param name="serviceProvider">Service provider for accessing services</param>
        /// <param name="logger">Activity logger for tracking operations</param>
        public RequestHandler_PB6(IServiceProvider serviceProvider, BecaActivityLoggerData logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _request = new Request_PB6_Configure();

            // TODO: Initialize specialized handlers
            // var uiDocument = serviceProvider.GetRequiredService<UIDocument>();
            // _circuitPathClicker = new EditCircuitPathClicker_WPF(uiDocument.Application);
            // _dbPathClicker = new EditDBPathClicker_WPF(uiDocument.Application);

            _logger?.Log("RequestHandler_PB6 initialized", LogType.Information);
        }

        #endregion

        #region IExternalEventHandler Implementation

        /// <summary>
        /// Get the name of this external event handler
        /// </summary>
        /// <returns>Handler name</returns>
        public string GetName()
        {
            return "PowerBIM 6 Request Handler";
        }

        /// <summary>
        /// Execute the pending request in the Revit API context
        /// This method is called by Revit when the ExternalEvent is raised
        /// </summary>
        /// <param name="uiapp">UI application from Revit</param>
        public void Execute(UIApplication uiapp)
        {
            try
            {
                var requestId = _request.Take();
                _logger?.Log($"Executing request: {requestId}", LogType.Information);

                switch (requestId)
                {
                    case RequestId_PB6.None:
                        return; // No request to process

                    // Project Operations
                    case RequestId_PB6.SaveProject:
                        HandleSaveProject();
                        break;

                    case RequestId_PB6.SaveSettings:
                        HandleSaveSettings();
                        break;

                    case RequestId_PB6.CommitProjectInfo:
                        HandleCommitProjectInfo();
                        break;

                    case RequestId_PB6.LoadProjectInfo:
                        HandleLoadProjectInfo();
                        break;

                    // Distribution Boards Operations
                    case RequestId_PB6.LoadDistributionBoards:
                        HandleLoadDistributionBoards();
                        break;

                    case RequestId_PB6.SaveDistributionBoard:
                        HandleSaveDistributionBoards();
                        break;

                    case RequestId_PB6.RefreshDistributionBoardSummary:
                        HandleRefreshDistributionBoardSummary();
                        break;

                    case RequestId_PB6.UpdaterRequired_All:
                        HandleUpdaterRequiredAll();
                        break;

                    // Circuit Operations
                    case RequestId_PB6.UpdateCircuits:
                        HandleUpdateCircuits();
                        break;

                    case RequestId_PB6.RecalculateCircuits:
                        HandleRecalculateCircuits();
                        break;

                    case RequestId_PB6.BulkEditLighting:
                        HandleBulkEditLighting();
                        break;

                    case RequestId_PB6.BulkEditPower:
                        HandleBulkEditPower();
                        break;

                    case RequestId_PB6.BulkEditOther:
                        HandleBulkEditOther();
                        break;

                    case RequestId_PB6.SaveCircuitData:
                        HandleSaveCircuitData();
                        break;

                    case RequestId_PB6.RecalculateAllCircuits:
                        HandleRecalculateAllCircuits();
                        break;

                    case RequestId_PB6.RunCircuitCheckManual:
                        HandleRunCircuitCheckManual();
                        break;

                    case RequestId_PB6.AutoCalc:
                        HandleAutoCalc();
                        break;

                    // Circuit Editing Enhanced Operations
                    case RequestId_PB6.SaveCircuitEditEnhanced:
                        HandleSaveCircuitEditEnhanced();
                        break;

                    case RequestId_PB6.RecalcAndRefreshCircuitToForm:
                        HandleRecalcAndRefreshCircuitToForm();
                        break;

                    case RequestId_PB6.RecalcAndRefreshAllCircuitsToForm:
                        HandleRecalcAndRefreshAllCircuitsToForm();
                        break;

                    case RequestId_PB6.RecalcAndRefreshLengthToFormClick:
                        HandleRecalcAndRefreshLengthToFormClick();
                        break;

                    case RequestId_PB6.Refresh_DerrivedCircuitProperties:
                        HandleRefreshDerivedCircuitProperties();
                        break;

                    case RequestId_PB6.WriteLengthAfterUserInputToCircuitParameter:
                        HandleWriteLengthAfterUserInputToCircuitParameter();
                        break;

                    // Path Editing Operations
                    case RequestId_PB6.OpenPathCustomizing:
                        HandleOpenPathCustomizing();
                        break;

                    case RequestId_PB6.OpenPathCustomizingForDB:
                        HandleOpenPathCustomizingForDB();
                        break;

                    case RequestId_PB6.ActivatePathEditView:
                        HandleActivatePathEditView();
                        break;

                    case RequestId_PB6.CheckViewCreation:
                        HandleCheckViewCreation();
                        break;

                    case RequestId_PB6.SetCircuitLengthManual:
                        HandleSetCircuitLengthManual();
                        break;

                    case RequestId_PB6.SetDbLengthManual:
                        HandleSetDbLengthManual();
                        break;

                    case RequestId_PB6.SetFirstLengthManual:
                        HandleSetFirstLengthManual();
                        break;

                    case RequestId_PB6.SetTotalLengthManual:
                        HandleSetTotalLengthManual();
                        break;

                    case RequestId_PB6.RecalcAndRefreshLengthToDbForm:
                        HandleRecalcAndRefreshLengthToDbForm();
                        break;

                    // Export Operations
                    case RequestId_PB6.ExportData:
                        HandleExportData();
                        break;

                    case RequestId_PB6.ExportCircuitImages:
                        HandleExportCircuitImages();
                        break;

                    case RequestId_PB6.ExportCircuitPathImages:
                        HandleExportCircuitPathImages();
                        break;

                    // Import Operations
                    case RequestId_PB6.ImportFromCsv:
                        HandleImportFromCsv();
                        break;

                    case RequestId_PB6.ProcessImportSettings:
                        HandleProcessImportSettings();
                        break;

                    // Advanced Settings Operations
                    case RequestId_PB6.CommitAdvancedSettings:
                        HandleCommitAdvancedSettings();
                        break;

                    case RequestId_PB6.CommitDistributionBoardSettings:
                        HandleCommitDistributionBoardSettings();
                        break;

                    case RequestId_PB6.RevertToOldSave:
                        HandleRevertToOldSave();
                        break;

                    // Distribution Board Operations (Additional)
                    case RequestId_PB6.ImportDistributionBoardSettings:
                        HandleImportDistributionBoardSettings();
                        break;

                    // UI Operations
                    case RequestId_PB6.WakeFormUp:
                    case RequestId_PB6.RefreshData:
                        HandleWakeFormUp();
                        break;

                    case RequestId_PB6.WakeFormUpDistributionBoardEdit:
                        HandleWakeFormUpDistributionBoardEdit();
                        break;

                    case RequestId_PB6.WakeFormUpCircuitEditEnhanced:
                        HandleWakeFormUpCircuitEditEnhanced();
                        break;

                    case RequestId_PB6.WakeFormUpStartForm:
                        HandleWakeFormUpStartForm();
                        break;

                    // Manual Operations
                    case RequestId_PB6.SetManualLock:
                        HandleSetManualLock();
                        break;

                    case RequestId_PB6.SaveUserNotes:
                        HandleSaveUserNotes();
                        break;

                    // Initialization
                    case RequestId_PB6.InitializeAllCircuits:
                        HandleInitializeAllCircuits();
                        break;

                    default:
                        _logger?.Log($"Unhandled request: {requestId}", LogType.Warning);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Request execution failed: {ex.Message}", LogType.Error);
            }
            finally
            {
                // Always wake up the UI
                WakeUpMainWindow();
            }
        }

        #endregion

        #region Request Handlers

        /// <summary>
        /// Handle save project request
        /// </summary>
        private void HandleSaveProject()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var projectInfo = ModelessMainWindowHandler.ProjectInfo;

                if (projectInfo != null)
                {
                    // REVIT-SAFE: Execute synchronously in Revit context
                    projectInfo.CommitToRevit();
                    _logger?.Log("Project information saved successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to save project: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle save settings request
        /// </summary>
        private void HandleSaveSettings()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var projectInfo = ModelessMainWindowHandler.ProjectInfo;

                if (projectInfo != null)
                {
                    // Save settings and commit to Revit
                    projectInfo.CommitProjectInfo();
                    _logger?.Log("Settings saved successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to save settings: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle commit project info request
        /// </summary>
        private void HandleCommitProjectInfo()
        {
            try
            {
                var projectInfo = ModelessMainWindowHandler.ProjectInfo;
                if (projectInfo != null)
                {
                    projectInfo.CommitProjectInfo();
                    _logger?.Log("Project info committed successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to commit project info: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle load project info request
        /// </summary>
        private void HandleLoadProjectInfo()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var projectInfoModel = revitService.LoadProjectInfo();

                if (projectInfoModel != null)
                {
                    // Update the static reference for cross-form communication
                    ModelessMainWindowHandler.ProjectInfo = projectInfoModel.OriginalData;
                    _logger?.Log("Project info loaded successfully", LogType.Information);
                }
                else
                {
                    _logger?.Log("Project info could not be loaded", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to load project info: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle load distribution boards request
        /// </summary>
        private void HandleLoadDistributionBoards()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoardModels = revitService.LoadDistributionBoards();

                if (distributionBoardModels?.Any() == true)
                {
                    // Convert models back to original data for cross-form communication
                    var originalDistributionBoards = distributionBoardModels
                        .Select(model => model.OriginalData)
                        .Where(data => data != null)
                        .ToList();

                    ModelessMainWindowHandler.AllDistributionBoards = originalDistributionBoards;
                    _logger?.Log($"Loaded {originalDistributionBoards.Count} distribution boards successfully", LogType.Information);
                }
                else
                {
                    _logger?.Log("No distribution boards found in the model", LogType.Warning);
                    ModelessMainWindowHandler.AllDistributionBoards = new List<PowerBIM_DBData>();
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to load distribution boards: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle save distribution board request
        /// </summary>
        private void HandleSaveDistributionBoards()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    int savedCount = 0;
                    foreach (var distributionBoard in distributionBoards)
                    {
                        var distributionBoardModel = new DistributionBoardModel(distributionBoard);
                        if (revitService.SaveDistributionBoard(distributionBoardModel))
                        {
                            savedCount++;
                        }
                    }

                    _logger?.Log($"Saved {savedCount} of {distributionBoards.Count} distribution boards successfully", LogType.Information);
                }
                else
                {
                    _logger?.Log("No distribution boards available to save", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to save distribution board: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle refresh distribution board summary request
        /// </summary>
        private void HandleRefreshDistributionBoardSummary()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    // Convert to models for processing
                    var distributionBoardModels = distributionBoards
                        .Select(db => new DistributionBoardModel(db))
                        .ToList();

                    // Refresh summary information
                    var refreshedModels = revitService.RefreshDistributionBoardSummary(distributionBoardModels);

                    // Update the static reference with refreshed data
                    ModelessMainWindowHandler.AllDistributionBoards = refreshedModels
                        .Select(model => model.OriginalData)
                        .Where(data => data != null)
                        .ToList();

                    _logger?.Log($"Distribution board summary refreshed for {refreshedModels.Count} distribution boards", LogType.Information);
                }
                else
                {
                    _logger?.Log("No distribution boards available to refresh", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to refresh distribution board summary: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle updater required all request
        /// </summary>
        private void HandleUpdaterRequiredAll()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    // Convert to models for processing
                    var distributionBoardModels = distributionBoards
                        .Select(db => new DistributionBoardModel(db))
                        .ToList();

                    // Update all circuits that require updates
                    bool success = revitService.UpdateCircuits(distributionBoardModels);

                    if (success)
                    {
                        _logger?.Log($"Updater required all processed successfully for {distributionBoardModels.Count} distribution boards", LogType.Information);
                    }
                    else
                    {
                        _logger?.Log("Updater required all completed with warnings", LogType.Warning);
                    }
                }
                else
                {
                    _logger?.Log("No distribution boards available for updater required all", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to process updater required all: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle update circuits request
        /// </summary>
        private void HandleUpdateCircuits()
        {
            try
            {
                var calculationService = _serviceProvider.GetRequiredService<ICalculationService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards.Where(d => !d.IsManuallyLocked))
                    {
                        // REVIT-SAFE: Execute synchronously in Revit context
                        calculationService.RecalculateDistributionBoard(distributionBoard);
                    }
                    _logger?.Log("Circuits updated successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to update circuits: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle recalculate circuits request
        /// </summary>
        private void HandleRecalculateCircuits()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    int recalculatedCount = 0;
                    foreach (var distributionBoard in distributionBoards.Where(db => !db.IsManuallyLocked))
                    {
                        var distributionBoardModel = new DistributionBoardModel(distributionBoard);
                        if (revitService.RecalculateCircuits(distributionBoardModel))
                        {
                            recalculatedCount++;
                        }
                    }

                    _logger?.Log($"Recalculated circuits for {recalculatedCount} distribution boards", LogType.Information);
                }
                else
                {
                    _logger?.Log("No distribution boards available for circuit recalculation", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to recalculate circuits: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle bulk edit lighting request
        /// </summary>
        private void HandleBulkEditLighting()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    int totalUpdated = 0;
                    var bulkEditSettings = new BulkEditSettings(); // Default settings

                    foreach (var distributionBoard in distributionBoards.Where(db => !db.IsManuallyLocked))
                    {
                        var distributionBoardModel = new DistributionBoardModel(distributionBoard);
                        int updated = revitService.BulkEditLighting(distributionBoardModel, bulkEditSettings);
                        totalUpdated += updated;
                    }

                    _logger?.Log($"Bulk edit lighting completed - updated {totalUpdated} circuits", LogType.Information);
                }
                else
                {
                    _logger?.Log("No distribution boards available for bulk edit lighting", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to bulk edit lighting: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle bulk edit power request
        /// </summary>
        private void HandleBulkEditPower()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    int totalUpdated = 0;
                    var bulkEditSettings = new BulkEditSettings(); // Default settings

                    foreach (var distributionBoard in distributionBoards.Where(db => !db.IsManuallyLocked))
                    {
                        var distributionBoardModel = new DistributionBoardModel(distributionBoard);
                        int updated = revitService.BulkEditPower(distributionBoardModel, bulkEditSettings);
                        totalUpdated += updated;
                    }

                    _logger?.Log($"Bulk edit power completed - updated {totalUpdated} circuits", LogType.Information);
                }
                else
                {
                    _logger?.Log("No distribution boards available for bulk edit power", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to bulk edit power: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle open path customizing request
        /// </summary>
        private void HandleOpenPathCustomizing()
        {
            try
            {
                var pathEditingService = _serviceProvider.GetRequiredService<IPathEditingService>();
                // REVIT-SAFE: Execute synchronously in Revit context
                pathEditingService.OpenPathCustomizingView();
                _logger?.Log("Path customizing view opened", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to open path customizing view: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle activate path edit view request
        /// </summary>
        private void HandleActivatePathEditView()
        {
            try
            {
                var pathEditingService = _serviceProvider.GetRequiredService<IPathEditingService>();
                // REVIT-SAFE: Execute synchronously in Revit context
                pathEditingService.ActivatePathEditView();
                _logger?.Log("Path edit view activated", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to activate path edit view: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle export data request
        /// </summary>
        private void HandleExportData()
        {
            try
            {
                var exportService = _serviceProvider.GetRequiredService<IExportService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    // Create default export settings
                    var exportSettings = new ExportSettingsModel();
                    exportSettings.ResetToDefaults();

                    // Export to Excel by default
                    bool success = exportService.ExportToExcel(exportSettings);

                    if (success)
                    {
                        _logger?.Log($"Data exported successfully for {distributionBoards.Count} distribution boards", LogType.Information);
                    }
                    else
                    {
                        _logger?.Log("Data export completed with warnings", LogType.Warning);
                    }
                }
                else
                {
                    _logger?.Log("No distribution board data available to export", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to export data: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle export circuit images request
        /// </summary>
        private void HandleExportCircuitImages()
        {
            try
            {
                var exportService = _serviceProvider.GetRequiredService<IExportService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    // Collect all circuits from all distribution boards
                    var allCircuits = new List<CircuitModel>();
                    foreach (var distributionBoard in distributionBoards)
                    {
                        if (distributionBoard.CCTs?.Any() == true)
                        {
                            var circuitModels = distributionBoard.CCTs
                                .Select(circuit => new CircuitModel(circuit))
                                .ToList();
                            allCircuits.AddRange(circuitModels);
                        }
                    }

                    if (allCircuits.Any())
                    {
                        // Export circuit path images to default location
                        string outputPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                        bool success = exportService.ExportCircuitPathImages(allCircuits, outputPath);

                        if (success)
                        {
                            _logger?.Log($"Circuit images exported successfully for {allCircuits.Count} circuits", LogType.Information);
                        }
                        else
                        {
                            _logger?.Log("Circuit image export completed with warnings", LogType.Warning);
                        }
                    }
                    else
                    {
                        _logger?.Log("No circuits found to export images for", LogType.Warning);
                    }
                }
                else
                {
                    _logger?.Log("No distribution boards available for circuit image export", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to export circuit images: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle wake form up request
        /// </summary>
        private void HandleWakeFormUp()
        {
            // This is handled in the finally block
            _logger?.Log("Wake form up request processed", LogType.Information);
        }

        /// <summary>
        /// Wake up the main window after request processing
        /// </summary>
        private void WakeUpMainWindow()
        {
            try
            {
                ModelessMainWindowHandler.WakeUpMainWindow();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to wake up main window: {ex.Message}", LogType.Error);
            }
        }

        #region Missing Circuit Operations

        /// <summary>
        /// Handle bulk edit other request
        /// </summary>
        private void HandleBulkEditOther()
        {
            try
            {
                var calculationService = _serviceProvider.GetRequiredService<ICalculationService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards.Where(d => !d.IsManuallyLocked))
                    {
                        var otherCircuits = distributionBoard.CCTs.Where(c => !c.CCT_Is_Power && !c.CCT_Is_Lighting);
                        foreach (var circuit in otherCircuits)
                        {
                            calculationService.RunCircuitCalculations(circuit);
                        }
                    }
                    _logger?.Log("Bulk edit other circuits completed", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to bulk edit other circuits: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle save circuit data request
        /// </summary>
        private void HandleSaveCircuitData()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards)
                    {
                        foreach (var circuit in distributionBoard.CCTs)
                        {
                            // Save circuit data to Revit parameters
                            circuit.CommitToRevit();
                        }
                    }
                    _logger?.Log("Circuit data saved successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to save circuit data: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle recalculate all circuits request
        /// </summary>
        private void HandleRecalculateAllCircuits()
        {
            try
            {
                var calculationService = _serviceProvider.GetRequiredService<ICalculationService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards)
                    {
                        calculationService.RecalculateDistributionBoard(distributionBoard);
                    }
                    _logger?.Log("All circuits recalculated successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to recalculate all circuits: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle run circuit check manual request
        /// </summary>
        private void HandleRunCircuitCheckManual()
        {
            try
            {
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards)
                    {
                        foreach (var circuit in distributionBoard.CCTs)
                        {
                            circuit.RunPowerBIMCheck();
                        }
                    }
                    _logger?.Log("Manual circuit checks completed", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to run manual circuit checks: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle auto calc request
        /// </summary>
        private void HandleAutoCalc()
        {
            try
            {
                var calculationService = _serviceProvider.GetRequiredService<ICalculationService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards.Where(d => !d.IsManuallyLocked))
                    {
                        calculationService.RecalculateDistributionBoard(distributionBoard);
                        calculationService.UpdateDistributionBoardSummary(distributionBoard);
                    }
                    _logger?.Log("Auto calculation completed", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to perform auto calculation: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region Circuit Editing Enhanced Operations

        /// <summary>
        /// Handle save circuit edit enhanced request
        /// </summary>
        private void HandleSaveCircuitEditEnhanced()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards)
                    {
                        foreach (var circuit in distributionBoard.CCTs.Where(c => c.HasUnsavedChanges))
                        {
                            // Save enhanced circuit editing changes
                            circuit.CommitToRevit();
                            circuit.HasUnsavedChanges = false;
                        }
                    }
                    _logger?.Log("Circuit edit enhanced data saved successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to save circuit edit enhanced data: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle recalc and refresh circuit to form request
        /// </summary>
        private void HandleRecalcAndRefreshCircuitToForm()
        {
            try
            {
                var calculationService = _serviceProvider.GetRequiredService<ICalculationService>();
                // This would typically work with a specific circuit passed via context
                // For now, we'll recalculate the currently selected circuit

                _logger?.Log("Circuit recalculated and refreshed to form", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to recalc and refresh circuit to form: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle recalc and refresh all circuits to form request
        /// </summary>
        private void HandleRecalcAndRefreshAllCircuitsToForm()
        {
            try
            {
                var calculationService = _serviceProvider.GetRequiredService<ICalculationService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards)
                    {
                        calculationService.RecalculateDistributionBoard(distributionBoard);
                    }
                    _logger?.Log("All circuits recalculated and refreshed to form", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to recalc and refresh all circuits to form: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle recalc and refresh length to form click request
        /// </summary>
        private void HandleRecalcAndRefreshLengthToFormClick()
        {
            try
            {
                var calculationService = _serviceProvider.GetRequiredService<ICalculationService>();
                // This would typically work with a specific circuit and recalculate lengths

                _logger?.Log("Length recalculated and refreshed to form", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to recalc and refresh length to form: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle refresh derived circuit properties request
        /// </summary>
        private void HandleRefreshDerivedCircuitProperties()
        {
            try
            {
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards)
                    {
                        foreach (var circuit in distributionBoard.CCTs)
                        {
                            circuit.Refresh_DerrivedCircuitProperties();
                        }
                    }
                    _logger?.Log("Derived circuit properties refreshed", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to refresh derived circuit properties: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle write length after user input to circuit parameter request
        /// </summary>
        private void HandleWriteLengthAfterUserInputToCircuitParameter()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                // This would typically work with a specific circuit and length value

                _logger?.Log("Length written to circuit parameter after user input", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write length to circuit parameter: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region Path Editing Operations

        /// <summary>
        /// Handle open path customizing for DB request
        /// </summary>
        private void HandleOpenPathCustomizingForDB()
        {
            try
            {
                var pathEditingService = _serviceProvider.GetRequiredService<IPathEditingService>();
                // REVIT-SAFE: Execute synchronously in Revit context
                pathEditingService.OpenPathCustomizingView();
                _logger?.Log("Path customizing for DB opened", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to open path customizing for DB: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle check view creation request
        /// </summary>
        private void HandleCheckViewCreation()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                // Check if required views exist for path editing

                _logger?.Log("View creation checked", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to check view creation: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle set circuit length manual request
        /// </summary>
        private void HandleSetCircuitLengthManual()
        {
            try
            {
                var pathEditingService = _serviceProvider.GetRequiredService<IPathEditingService>();
                // This would typically work with a specific circuit and length value

                _logger?.Log("Circuit length set manually", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to set circuit length manually: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle set DB length manual request
        /// </summary>
        private void HandleSetDbLengthManual()
        {
            try
            {
                var pathEditingService = _serviceProvider.GetRequiredService<IPathEditingService>();
                // This would typically work with a specific DB and length value

                _logger?.Log("DB length set manually", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to set DB length manually: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle set first length manual request
        /// </summary>
        private void HandleSetFirstLengthManual()
        {
            try
            {
                var pathEditingService = _serviceProvider.GetRequiredService<IPathEditingService>();
                // This would typically work with a specific circuit and first cable length

                _logger?.Log("First length set manually", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to set first length manually: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle set total length manual request
        /// </summary>
        private void HandleSetTotalLengthManual()
        {
            try
            {
                var pathEditingService = _serviceProvider.GetRequiredService<IPathEditingService>();
                // This would typically work with a specific circuit and total length

                _logger?.Log("Total length set manually", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to set total length manually: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle recalc and refresh length to DB form request
        /// </summary>
        private void HandleRecalcAndRefreshLengthToDbForm()
        {
            try
            {
                var calculationService = _serviceProvider.GetRequiredService<ICalculationService>();
                // This would typically recalculate lengths for a specific DB

                _logger?.Log("Length recalculated and refreshed to DB form", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to recalc and refresh length to DB form: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region Export Operations

        /// <summary>
        /// Handle export circuit path images request
        /// </summary>
        private void HandleExportCircuitPathImages()
        {
            try
            {
                var exportService = _serviceProvider.GetRequiredService<IExportService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    // Create export settings for circuit path images
                    var exportSettings = new ExportSettingsModel();
                    exportSettings.ResetToDefaults();
                    exportSettings.IncludeCircuitPaths = true;

                    bool success = exportService.ExportCircuitPathImages(exportSettings);

                    if (success)
                    {
                        _logger?.Log("Circuit path images exported successfully", LogType.Information);
                    }
                    else
                    {
                        _logger?.Log("Circuit path images export completed with warnings", LogType.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to export circuit path images: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region Import Operations

        /// <summary>
        /// Handle import from CSV request
        /// </summary>
        private void HandleImportFromCsv()
        {
            try
            {
                var importService = _serviceProvider.GetRequiredService<IImportService>();
                // This would typically open a file dialog and import data

                _logger?.Log("CSV import initiated", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to import from CSV: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle process import settings request
        /// </summary>
        private void HandleProcessImportSettings()
        {
            try
            {
                var importService = _serviceProvider.GetRequiredService<IImportService>();
                // This would typically process import settings and apply them

                _logger?.Log("Import settings processed", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to process import settings: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region Advanced Settings Operations

        /// <summary>
        /// Handle commit advanced settings request
        /// </summary>
        private void HandleCommitAdvancedSettings()
        {
            try
            {
                var dataService = _serviceProvider.GetRequiredService<IDataService>();
                var projectInfo = ModelessMainWindowHandler.ProjectInfo;

                if (projectInfo != null)
                {
                    // Commit advanced settings to Revit
                    projectInfo.CommitToRevit();
                    _logger?.Log("Advanced settings committed successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to commit advanced settings: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle commit distribution board settings request
        /// </summary>
        private void HandleCommitDistributionBoardSettings()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards)
                    {
                        distributionBoard.CommitToRevit();
                    }
                    _logger?.Log("Distribution board settings committed successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to commit distribution board settings: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle revert to old save request
        /// </summary>
        private void HandleRevertToOldSave()
        {
            try
            {
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards)
                    {
                        // Revert changes by reloading from Revit
                        distributionBoard.LoadFromRevit();
                    }
                    _logger?.Log("Reverted to old save successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to revert to old save: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle import distribution board settings request
        /// </summary>
        private void HandleImportDistributionBoardSettings()
        {
            try
            {
                var importService = _serviceProvider.GetRequiredService<IImportService>();
                // This would typically import DB settings from a file

                _logger?.Log("Distribution board settings import initiated", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to import distribution board settings: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region UI Operations

        /// <summary>
        /// Handle wake form up distribution board edit request
        /// </summary>
        private void HandleWakeFormUpDistributionBoardEdit()
        {
            try
            {
                // Wake up the distribution board edit form
                WakeUpMainWindow();
                _logger?.Log("Distribution board edit form woken up", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to wake up distribution board edit form: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle wake form up circuit edit enhanced request
        /// </summary>
        private void HandleWakeFormUpCircuitEditEnhanced()
        {
            try
            {
                // Wake up the circuit edit enhanced form
                WakeUpMainWindow();
                _logger?.Log("Circuit edit enhanced form woken up", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to wake up circuit edit enhanced form: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle wake form up start form request
        /// </summary>
        private void HandleWakeFormUpStartForm()
        {
            try
            {
                // Wake up the start form
                WakeUpMainWindow();
                _logger?.Log("Start form woken up", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to wake up start form: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region Manual Operations

        /// <summary>
        /// Handle set manual lock request
        /// </summary>
        private void HandleSetManualLock()
        {
            try
            {
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    // This would typically work with a specific circuit or DB to lock/unlock
                    // For now, we'll log the operation
                    _logger?.Log("Manual lock status updated", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to set manual lock: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle save user notes request
        /// </summary>
        private void HandleSaveUserNotes()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards)
                    {
                        // Save user notes to Revit parameters
                        if (!string.IsNullOrEmpty(distributionBoard.User_Notes))
                        {
                            distributionBoard.CommitToRevit();
                        }
                    }
                    _logger?.Log("User notes saved successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to save user notes: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region Initialization Operations

        /// <summary>
        /// Handle initialize all circuits request
        /// </summary>
        private void HandleInitializeAllCircuits()
        {
            try
            {
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards)
                    {
                        distributionBoard.Initialise_AllCircuits();
                    }
                    _logger?.Log("All circuits initialized successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to initialize all circuits: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #endregion
    }
}
