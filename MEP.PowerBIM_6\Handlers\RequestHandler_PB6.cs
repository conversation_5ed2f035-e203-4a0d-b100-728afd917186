using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Services;
using MEP.PowerBIM_6.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace MEP.PowerBIM_6.Handlers
{
    /// <summary>
    /// Enhanced request handler for PowerBIM 6 WPF application
    /// Implements IExternalEventHandler to safely execute Revit API operations
    /// Uses dependency injection for clean service access
    /// </summary>
    public class RequestHandler_PB6 : IExternalEventHandler
    {
        #region Fields

        private readonly IServiceProvider _serviceProvider;
        private readonly BecaActivityLoggerData _logger;
        private readonly Request_PB6_Configure _request;

        // Specialized handlers for complex operations (to be implemented)
        // private readonly EditCircuitPathClicker_WPF _circuitPathClicker;
        // private readonly EditDBPathClicker_WPF _dbPathClicker;

        #endregion

        #region Properties

        /// <summary>
        /// Get the request configuration object
        /// </summary>
        public Request_PB6_Configure Request => _request;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the request handler with dependency injection
        /// </summary>
        /// <param name="serviceProvider">Service provider for accessing services</param>
        /// <param name="logger">Activity logger for tracking operations</param>
        public RequestHandler_PB6(IServiceProvider serviceProvider, BecaActivityLoggerData logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _request = new Request_PB6_Configure();

            // TODO: Initialize specialized handlers
            // var uiDocument = serviceProvider.GetRequiredService<UIDocument>();
            // _circuitPathClicker = new EditCircuitPathClicker_WPF(uiDocument.Application);
            // _dbPathClicker = new EditDBPathClicker_WPF(uiDocument.Application);

            _logger?.Log("RequestHandler_PB6 initialized", LogType.Information);
        }

        #endregion

        #region IExternalEventHandler Implementation

        /// <summary>
        /// Get the name of this external event handler
        /// </summary>
        /// <returns>Handler name</returns>
        public string GetName()
        {
            return "PowerBIM 6 Request Handler";
        }

        /// <summary>
        /// Execute the pending request in the Revit API context
        /// This method is called by Revit when the ExternalEvent is raised
        /// </summary>
        /// <param name="uiapp">UI application from Revit</param>
        public void Execute(UIApplication uiapp)
        {
            try
            {
                var requestId = _request.Take();
                _logger?.Log($"Executing request: {requestId}", LogType.Information);

                switch (requestId)
                {
                    case RequestId_PB6.None:
                        return; // No request to process

                    // Project Operations
                    case RequestId_PB6.SaveProject:
                        HandleSaveProject();
                        break;

                    case RequestId_PB6.SaveSettings:
                        HandleSaveSettings();
                        break;

                    case RequestId_PB6.CommitProjectInfo:
                        HandleCommitProjectInfo();
                        break;

                    case RequestId_PB6.LoadProjectInfo:
                        HandleLoadProjectInfo();
                        break;

                    // Distribution Boards Operations
                    case RequestId_PB6.LoadDistributionBoards:
                        HandleLoadDistributionBoards();
                        break;

                    case RequestId_PB6.SaveDistributionBoard:
                        HandleSaveDistributionBoards();
                        break;

                    case RequestId_PB6.RefreshDistributionBoardSummary:
                        HandleRefreshDistributionBoardSummary();
                        break;

                    case RequestId_PB6.UpdaterRequired_All:
                        HandleUpdaterRequiredAll();
                        break;

                    // Circuit Operations
                    case RequestId_PB6.UpdateCircuits:
                        HandleUpdateCircuits();
                        break;

                    case RequestId_PB6.RecalculateCircuits:
                        HandleRecalculateCircuits();
                        break;

                    case RequestId_PB6.BulkEditLighting:
                        HandleBulkEditLighting();
                        break;

                    case RequestId_PB6.BulkEditPower:
                        HandleBulkEditPower();
                        break;

                    // Path Editing Operations
                    case RequestId_PB6.OpenPathCustomizing:
                        HandleOpenPathCustomizing();
                        break;

                    case RequestId_PB6.ActivatePathEditView:
                        HandleActivatePathEditView();
                        break;

                    // Export Operations
                    case RequestId_PB6.ExportData:
                        HandleExportData();
                        break;

                    case RequestId_PB6.ExportCircuitImages:
                        HandleExportCircuitImages();
                        break;

                    // UI Operations
                    case RequestId_PB6.WakeFormUp:
                    case RequestId_PB6.RefreshData:
                        HandleWakeFormUp();
                        break;

                    default:
                        _logger?.Log($"Unhandled request: {requestId}", LogType.Warning);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Request execution failed: {ex.Message}", LogType.Error);
            }
            finally
            {
                // Always wake up the UI
                WakeUpMainWindow();
            }
        }

        #endregion

        #region Request Handlers

        /// <summary>
        /// Handle save project request
        /// </summary>
        private void HandleSaveProject()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var projectInfo = ModelessMainWindowHandler.ProjectInfo;

                if (projectInfo != null)
                {
                    // REVIT-SAFE: Execute synchronously in Revit context
                    projectInfo.CommitToRevit();
                    _logger?.Log("Project information saved successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to save project: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle save settings request
        /// </summary>
        private void HandleSaveSettings()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var projectInfo = ModelessMainWindowHandler.ProjectInfo;

                if (projectInfo != null)
                {
                    // Save settings and commit to Revit
                    projectInfo.CommitProjectInfo();
                    _logger?.Log("Settings saved successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to save settings: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle commit project info request
        /// </summary>
        private void HandleCommitProjectInfo()
        {
            try
            {
                var projectInfo = ModelessMainWindowHandler.ProjectInfo;
                if (projectInfo != null)
                {
                    projectInfo.CommitProjectInfo();
                    _logger?.Log("Project info committed successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to commit project info: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle load project info request
        /// </summary>
        private void HandleLoadProjectInfo()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var projectInfoModel = revitService.LoadProjectInfo();

                if (projectInfoModel != null)
                {
                    // Update the static reference for cross-form communication
                    ModelessMainWindowHandler.ProjectInfo = projectInfoModel.OriginalData;
                    _logger?.Log("Project info loaded successfully", LogType.Information);
                }
                else
                {
                    _logger?.Log("Project info could not be loaded", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to load project info: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle load distribution boards request
        /// </summary>
        private void HandleLoadDistributionBoards()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoardModels = revitService.LoadDistributionBoards();

                if (distributionBoardModels?.Any() == true)
                {
                    // Convert models back to original data for cross-form communication
                    var originalDistributionBoards = distributionBoardModels
                        .Select(model => model.OriginalData)
                        .Where(data => data != null)
                        .ToList();

                    ModelessMainWindowHandler.AllDistributionBoards = originalDistributionBoards;
                    _logger?.Log($"Loaded {originalDistributionBoards.Count} distribution boards successfully", LogType.Information);
                }
                else
                {
                    _logger?.Log("No distribution boards found in the model", LogType.Warning);
                    ModelessMainWindowHandler.AllDistributionBoards = new List<PowerBIM_DBData>();
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to load distribution boards: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle save distribution board request
        /// </summary>
        private void HandleSaveDistributionBoards()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    int savedCount = 0;
                    foreach (var distributionBoard in distributionBoards)
                    {
                        var distributionBoardModel = new DistributionBoardModel(distributionBoard);
                        if (revitService.SaveDistributionBoard(distributionBoardModel))
                        {
                            savedCount++;
                        }
                    }

                    _logger?.Log($"Saved {savedCount} of {distributionBoards.Count} distribution boards successfully", LogType.Information);
                }
                else
                {
                    _logger?.Log("No distribution boards available to save", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to save distribution board: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle refresh distribution board summary request
        /// </summary>
        private void HandleRefreshDistributionBoardSummary()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    // Convert to models for processing
                    var distributionBoardModels = distributionBoards
                        .Select(db => new DistributionBoardModel(db))
                        .ToList();

                    // Refresh summary information
                    var refreshedModels = revitService.RefreshDistributionBoardSummary(distributionBoardModels);

                    // Update the static reference with refreshed data
                    ModelessMainWindowHandler.AllDistributionBoards = refreshedModels
                        .Select(model => model.OriginalData)
                        .Where(data => data != null)
                        .ToList();

                    _logger?.Log($"Distribution board summary refreshed for {refreshedModels.Count} distribution boards", LogType.Information);
                }
                else
                {
                    _logger?.Log("No distribution boards available to refresh", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to refresh distribution board summary: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle updater required all request
        /// </summary>
        private void HandleUpdaterRequiredAll()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    // Convert to models for processing
                    var distributionBoardModels = distributionBoards
                        .Select(db => new DistributionBoardModel(db))
                        .ToList();

                    // Update all circuits that require updates
                    bool success = revitService.UpdateCircuits(distributionBoardModels);

                    if (success)
                    {
                        _logger?.Log($"Updater required all processed successfully for {distributionBoardModels.Count} distribution boards", LogType.Information);
                    }
                    else
                    {
                        _logger?.Log("Updater required all completed with warnings", LogType.Warning);
                    }
                }
                else
                {
                    _logger?.Log("No distribution boards available for updater required all", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to process updater required all: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle update circuits request
        /// </summary>
        private void HandleUpdateCircuits()
        {
            try
            {
                var calculationService = _serviceProvider.GetRequiredService<ICalculationService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards != null)
                {
                    foreach (var distributionBoard in distributionBoards.Where(d => !d.IsManuallyLocked))
                    {
                        // REVIT-SAFE: Execute synchronously in Revit context
                        calculationService.RecalculateDistributionBoard(distributionBoard);
                    }
                    _logger?.Log("Circuits updated successfully", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to update circuits: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle recalculate circuits request
        /// </summary>
        private void HandleRecalculateCircuits()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    int recalculatedCount = 0;
                    foreach (var distributionBoard in distributionBoards.Where(db => !db.IsManuallyLocked))
                    {
                        var distributionBoardModel = new DistributionBoardModel(distributionBoard);
                        if (revitService.RecalculateCircuits(distributionBoardModel))
                        {
                            recalculatedCount++;
                        }
                    }

                    _logger?.Log($"Recalculated circuits for {recalculatedCount} distribution boards", LogType.Information);
                }
                else
                {
                    _logger?.Log("No distribution boards available for circuit recalculation", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to recalculate circuits: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle bulk edit lighting request
        /// </summary>
        private void HandleBulkEditLighting()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    int totalUpdated = 0;
                    var bulkEditSettings = new BulkEditSettings(); // Default settings

                    foreach (var distributionBoard in distributionBoards.Where(db => !db.IsManuallyLocked))
                    {
                        var distributionBoardModel = new DistributionBoardModel(distributionBoard);
                        int updated = revitService.BulkEditLighting(distributionBoardModel, bulkEditSettings);
                        totalUpdated += updated;
                    }

                    _logger?.Log($"Bulk edit lighting completed - updated {totalUpdated} circuits", LogType.Information);
                }
                else
                {
                    _logger?.Log("No distribution boards available for bulk edit lighting", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to bulk edit lighting: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle bulk edit power request
        /// </summary>
        private void HandleBulkEditPower()
        {
            try
            {
                var revitService = _serviceProvider.GetRequiredService<IRevitService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    int totalUpdated = 0;
                    var bulkEditSettings = new BulkEditSettings(); // Default settings

                    foreach (var distributionBoard in distributionBoards.Where(db => !db.IsManuallyLocked))
                    {
                        var distributionBoardModel = new DistributionBoardModel(distributionBoard);
                        int updated = revitService.BulkEditPower(distributionBoardModel, bulkEditSettings);
                        totalUpdated += updated;
                    }

                    _logger?.Log($"Bulk edit power completed - updated {totalUpdated} circuits", LogType.Information);
                }
                else
                {
                    _logger?.Log("No distribution boards available for bulk edit power", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to bulk edit power: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle open path customizing request
        /// </summary>
        private void HandleOpenPathCustomizing()
        {
            try
            {
                var pathEditingService = _serviceProvider.GetRequiredService<IPathEditingService>();
                // REVIT-SAFE: Execute synchronously in Revit context
                pathEditingService.OpenPathCustomizingView();
                _logger?.Log("Path customizing view opened", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to open path customizing view: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle activate path edit view request
        /// </summary>
        private void HandleActivatePathEditView()
        {
            try
            {
                var pathEditingService = _serviceProvider.GetRequiredService<IPathEditingService>();
                // REVIT-SAFE: Execute synchronously in Revit context
                pathEditingService.ActivatePathEditView();
                _logger?.Log("Path edit view activated", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to activate path edit view: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle export data request
        /// </summary>
        private void HandleExportData()
        {
            try
            {
                var exportService = _serviceProvider.GetRequiredService<IExportService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    // Create default export settings
                    var exportSettings = new ExportSettingsModel();
                    exportSettings.ResetToDefaults();

                    // Export to Excel by default
                    bool success = exportService.ExportToExcel(exportSettings);

                    if (success)
                    {
                        _logger?.Log($"Data exported successfully for {distributionBoards.Count} distribution boards", LogType.Information);
                    }
                    else
                    {
                        _logger?.Log("Data export completed with warnings", LogType.Warning);
                    }
                }
                else
                {
                    _logger?.Log("No distribution board data available to export", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to export data: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle export circuit images request
        /// </summary>
        private void HandleExportCircuitImages()
        {
            try
            {
                var exportService = _serviceProvider.GetRequiredService<IExportService>();
                var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

                if (distributionBoards?.Any() == true)
                {
                    // Collect all circuits from all distribution boards
                    var allCircuits = new List<CircuitModel>();
                    foreach (var distributionBoard in distributionBoards)
                    {
                        if (distributionBoard.CCTs?.Any() == true)
                        {
                            var circuitModels = distributionBoard.CCTs
                                .Select(circuit => new CircuitModel(circuit))
                                .ToList();
                            allCircuits.AddRange(circuitModels);
                        }
                    }

                    if (allCircuits.Any())
                    {
                        // Export circuit path images to default location
                        string outputPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                        bool success = exportService.ExportCircuitPathImages(allCircuits, outputPath);

                        if (success)
                        {
                            _logger?.Log($"Circuit images exported successfully for {allCircuits.Count} circuits", LogType.Information);
                        }
                        else
                        {
                            _logger?.Log("Circuit image export completed with warnings", LogType.Warning);
                        }
                    }
                    else
                    {
                        _logger?.Log("No circuits found to export images for", LogType.Warning);
                    }
                }
                else
                {
                    _logger?.Log("No distribution boards available for circuit image export", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to export circuit images: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Handle wake form up request
        /// </summary>
        private void HandleWakeFormUp()
        {
            // This is handled in the finally block
            _logger?.Log("Wake form up request processed", LogType.Information);
        }

        /// <summary>
        /// Wake up the main window after request processing
        /// </summary>
        private void WakeUpMainWindow()
        {
            try
            {
                ModelessMainWindowHandler.WakeUpMainWindow();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to wake up main window: {ex.Message}", LogType.Error);
            }
        }

        #endregion
    }
}
