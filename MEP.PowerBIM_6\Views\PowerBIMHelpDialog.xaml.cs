﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using MessageBox = System.Windows.MessageBox;

namespace MEP.PowerBIM_6.Views
{
    /// <summary>
    /// Interaction logic for PowerBIMHelpDialog.xaml
    /// </summary>
    public partial class PowerBIMHelpDialog : Window
    {
        public PowerBIMHelpDialog()
        {
            InitializeComponent();
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void VisitWebsite_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo("https://www.beca.com")
                {
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Unable to open website. Error: {ex.Message}",
                    "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void OpenSharePoint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Check domain to determine which help to show
                if (Environment.UserDomainName == "BECAMAIL")
                {
                    Process.Start(new ProcessStartInfo("https://becagroup.sharepoint.com/sites/BIMBrilliance/SitePages/PowerBIM-5.aspx")
                    {
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show(
                        "SharePoint access is only available for Beca domain users.",
                        "Access Restricted",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Unable to open SharePoint. Error: {ex.Message}",
                    "Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Shows the help dialog
        /// </summary>
        /// <param name="owner">Owner window</param>
        public static void ShowDialog(Window owner = null)
        {
            var dialog = new PowerBIMHelpDialog();

            if (owner != null)
                dialog.Owner = owner;

            ((Window)dialog).ShowDialog();
        }
    }
}
