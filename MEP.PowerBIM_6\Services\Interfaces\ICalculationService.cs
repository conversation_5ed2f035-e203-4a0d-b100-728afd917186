using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Models.Enums;

namespace MEP.PowerBIM_6.Services.Interfaces
{
    /// <summary>
    /// Service interface for electrical calculation operations (REVIT-SAFE: All synchronous)
    /// Based on PowerBIM_Calculations from legacy PowerBIM_1.5
    /// Handles all electrical engineering calculations including voltage drop, EFLI, and circuit validation
    /// </summary>
    public interface ICalculationService
    {
        #region Distribution Board Calculations

        /// <summary>
        /// Recalculate all circuits in a distribution board
        /// </summary>
        /// <param name="distributionBoard">Distribution board to recalculate</param>
        /// <returns>True if successful</returns>
        bool RecalculateDistributionBoard(PowerBIM_DBData distributionBoard);

        /// <summary>
        /// Calculate diversified loads for a distribution board
        /// </summary>
        /// <param name="distributionBoard">Distribution board to calculate</param>
        /// <returns>True if successful</returns>
        bool CalculateDiversifiedLoads(PowerBIM_DBData distributionBoard);

        /// <summary>
        /// Update distribution board summary calculations
        /// </summary>
        /// <param name="distributionBoard">Distribution board to update</param>
        /// <returns>True if successful</returns>
        bool UpdateDistributionBoardSummary(PowerBIM_DBData distributionBoard);

        #endregion

        #region Circuit Calculations

        /// <summary>
        /// Run all calculations for a specific circuit
        /// </summary>
        /// <param name="circuit">Circuit to calculate</param>
        /// <returns>True if successful</returns>
        bool RunCircuitCalculations(PowerBIM_CircuitData circuit);

        /// <summary>
        /// Update circuit properties based on calculations
        /// </summary>
        /// <param name="circuit">Circuit to update</param>
        /// <returns>True if successful</returns>
        bool UpdateCircuitProperties(PowerBIM_CircuitData circuit);

        /// <summary>
        /// Calculate PowerBIM current for a circuit
        /// </summary>
        /// <param name="circuit">Circuit to calculate current for</param>
        /// <returns>Calculated current in Amps</returns>
        double CalculatePowerBIMCurrent(PowerBIM_CircuitData circuit);

        #endregion

        #region Voltage Drop Calculations

        /// <summary>
        /// Calculate final circuit voltage drop
        /// </summary>
        /// <param name="circuit">Circuit to calculate</param>
        /// <param name="vdRule">Voltage drop calculation rule</param>
        /// <returns>Voltage drop in Volts</returns>
        double CalculateFinalCircuitVoltDrop(PowerBIM_CircuitData circuit, VoltDropCalculation vdRule);

        /// <summary>
        /// Calculate voltage drop percentage
        /// </summary>
        /// <param name="voltDrop">Voltage drop in Volts</param>
        /// <param name="numberOfPoles">Number of poles (1 or 3)</param>
        /// <returns>Voltage drop percentage</returns>
        double CalculateFinalCircuitVoltDropPercentage(double voltDrop, int numberOfPoles);

        /// <summary>
        /// Validate circuit voltage drop against limits
        /// </summary>
        /// <param name="calculatedVD">Calculated voltage drop percentage</param>
        /// <param name="maxVD">Maximum allowed voltage drop percentage</param>
        /// <returns>True if within limits</returns>
        bool ValidateCircuitVoltDrop(double calculatedVD, double maxVD);

        /// <summary>
        /// Validate system voltage drop (circuit + DB)
        /// </summary>
        /// <param name="circuitVD">Circuit voltage drop percentage</param>
        /// <param name="dbVD">Distribution board voltage drop percentage</param>
        /// <param name="systemMaxVD">System maximum voltage drop percentage</param>
        /// <returns>True if within limits</returns>
        bool ValidateSystemVoltDrop(double circuitVD, double dbVD, double systemMaxVD);

        #endregion

        #region EFLI (Earth Fault Loop Impedance) Calculations

        /// <summary>
        /// Calculate and validate EFLI for a circuit
        /// </summary>
        /// <param name="circuit">Circuit to calculate EFLI for</param>
        /// <returns>True if EFLI is within acceptable limits</returns>
        bool CalculateAndValidateEFLI(PowerBIM_CircuitData circuit);

        /// <summary>
        /// Calculate EFLI for a circuit
        /// </summary>
        /// <param name="circuit">Circuit to calculate</param>
        /// <param name="dbEFLI_R">Distribution board EFLI resistance</param>
        /// <param name="dbEFLI_X">Distribution board EFLI reactance</param>
        /// <returns>Total EFLI value</returns>
        double CalculateEFLI(PowerBIM_CircuitData circuit, double dbEFLI_R, double dbEFLI_X);

        /// <summary>
        /// Calculate EFLI to first point of circuit
        /// </summary>
        /// <param name="circuit">Circuit to calculate</param>
        /// <param name="dbEFLI_R">Distribution board EFLI resistance</param>
        /// <param name="dbEFLI_X">Distribution board EFLI reactance</param>
        /// <returns>EFLI to first point</returns>
        double CalculateEFLI_ToFirst(PowerBIM_CircuitData circuit, double dbEFLI_R, double dbEFLI_X);

        #endregion

        #region Circuit Validation Checks

        /// <summary>
        /// Run all validation checks for a circuit
        /// </summary>
        /// <param name="circuit">Circuit to validate</param>
        /// <returns>True if all checks pass</returns>
        bool RunAllCircuitChecks(PowerBIM_CircuitData circuit);

        /// <summary>
        /// Count the number of passing validation checks
        /// </summary>
        /// <param name="circuit">Circuit to check</param>
        /// <returns>Number of passing checks</returns>
        int CountPassingChecks(PowerBIM_CircuitData circuit);

        #endregion

        #region Cable and Breaker Calculations

        /// <summary>
        /// Update cable calculations for current and length
        /// </summary>
        /// <param name="cable">Cable to update</param>
        /// <param name="current">Design current</param>
        /// <param name="length">Cable length</param>
        /// <returns>True if successful</returns>
        bool UpdateCableCalculations(PowerBIM_CableData cable, double current, double length);

        /// <summary>
        /// Update breaker calculations and validation
        /// </summary>
        /// <param name="breaker">Breaker to update</param>
        /// <returns>True if successful</returns>
        bool UpdateBreakerCalculations(PowerBIM_BreakerData breaker);

        #endregion

        #region Clearing Time Calculations

        /// <summary>
        /// Determine clearing time for a circuit based on load type
        /// </summary>
        /// <param name="circuit">Circuit to determine clearing time for</param>
        /// <returns>Clearing time in seconds</returns>
        double DetermineClearingTime(PowerBIM_CircuitData circuit);

        #endregion

        #region Diversity Calculations

        /// <summary>
        /// Calculate diversified circuit load
        /// </summary>
        /// <param name="circuit">Circuit to calculate diversified load for</param>
        /// <returns>Diversified load value</returns>
        double CalculateDiversifiedCircuitLoad(PowerBIM_CircuitData circuit);

        #endregion
    }
}
