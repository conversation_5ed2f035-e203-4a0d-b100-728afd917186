﻿<Page
    x:Class="MEP.PowerBIM_6.Views.PowerBIMResultsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Results"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="16">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <materialDesign:PackIcon
                Width="64"
                Height="64"
                Margin="0,0,0,16"
                HorizontalAlignment="Center"
                Kind="ChartLine" />
            <TextBlock
                Margin="0,0,0,8"
                HorizontalAlignment="Center"
                Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                Text="Results and Analysis" />
            <TextBlock
                HorizontalAlignment="Center"
                Foreground="{DynamicResource MaterialDesignBodyLight}"
                Text="Detailed analysis results and reports will be displayed here."
                TextAlignment="Center" />
            <TextBlock
                Margin="0,16,0,0"
                HorizontalAlignment="Center"
                FontStyle="Italic"
                Text="This page will be implemented in Phase 4."
                TextAlignment="Center" />
        </StackPanel>
    </Grid>
</Page>
