﻿using MEP.PowerBIM_6.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using MessageBox = System.Windows.MessageBox;

namespace MEP.PowerBIM_6.Views
{
    /// <summary>
    /// Interaction logic for PowerBIMDBEditDialog.xaml
    /// </summary>
    public partial class PowerBIMDBEditDialog : Window
    {
        //public PowerBIMDBDataModelEnhanced DBData { get; private set; }
        public bool WasCalculated { get; private set; }

        public PowerBIMDBEditDialog(/*PowerBIMDBDataModelEnhanced dbData*/)
        {
            InitializeComponent();

            //DBData = dbData;
            //DataContext = DBData;

            // Set dialog result to false initially
            DialogResult = false;
        }

        private void Calculate_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // TODO: Implement calculation logic
                // This would trigger the calculation service for this specific DB

                WasCalculated = true;

                // Show success message
                MessageBox.Show(
                    "Calculations completed successfully.",
                    "PowerBIM",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error during calculation: {ex.Message}",
                    "PowerBIM Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate input data
                if (!ValidateInput())
                    return;

                // Set dialog result to true to indicate success
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error saving data: {ex.Message}",
                    "PowerBIM Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            // Set dialog result to false and close
            DialogResult = false;
            Close();
        }

        private bool ValidateInput()
        {
            //// Validate upstream device rating
            //if (DBData.UpstreamDeviceRating <= 0)
            //{
            //    MessageBox.Show(
            //        "Upstream Device Rating must be greater than zero.",
            //        "Validation Error",
            //        MessageBoxButton.OK,
            //        MessageBoxImage.Warning);
            //    return false;
            //}

            //// Validate device kA rating
            //if (DBData.DeviceKARating <= 0)
            //{
            //    MessageBox.Show(
            //        "Device kA Rating must be greater than zero.",
            //        "Validation Error",
            //        MessageBoxButton.OK,
            //        MessageBoxImage.Warning);
            //    return false;
            //}

            //// Validate EFLI values
            //if (DBData.EFLI_R < 0 || DBData.EFLI_X < 0)
            //{
            //    MessageBox.Show(
            //        "EFLI values cannot be negative.",
            //        "Validation Error",
            //        MessageBoxButton.OK,
            //        MessageBoxImage.Warning);
            //    return false;
            //}

            //// Validate voltage drop
            //if (DBData.DBVD < 0 || DBData.DBVD > 100)
            //{
            //    MessageBox.Show(
            //        "DB Voltage Drop must be between 0 and 100%.",
            //        "Validation Error",
            //        MessageBoxButton.OK,
            //        MessageBoxImage.Warning);
            //    return false;
            //}

            //// Validate PSCC
            //if (DBData.PSCC <= 0)
            //{
            //    MessageBox.Show(
            //        "PSCC must be greater than zero.",
            //        "Validation Error",
            //        MessageBoxButton.OK,
            //        MessageBoxImage.Warning);
            //    return false;
            //}

            return true;
        }

        ///// <summary>
        ///// Shows the DB edit dialog for the specified distribution board
        ///// </summary>
        ///// <param name="owner">Owner window</param>
        ///// <param name="dbData">Distribution board data to edit</param>
        ///// <returns>True if the user saved changes, false if cancelled</returns>
        //public static bool ShowDialog(Window owner, PowerBIMDBDataModelEnhanced dbData)
        //{
        //    var dialog = new PowerBIMDBEditDialog(dbData)
        //    {
        //        Owner = owner
        //    };

        //    return dialog.ShowDialog() == true;
        //}
    }
}
