﻿<Page
    x:Class="MEP.PowerBIM_6.Views.PowerBIMSettingsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Settings"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

        </ResourceDictionary>

    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="16">
            <!--  Header  -->
            <StackPanel Margin="0,0,0,24" Orientation="Horizontal">
                <materialDesign:PackIcon
                    Width="24"
                    Height="24"
                    Margin="0,0,8,0"
                    VerticalAlignment="Center"
                    Kind="Settings" />
                <TextBlock
                    VerticalAlignment="Center"
                    Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                    Text="PowerBIM Settings" />
            </StackPanel>

            <!--  Calculation Settings  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="16">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Calculation Settings" />

                    <!--  Voltage Drop Settings  -->
                    <GroupBox Margin="0,0,0,16" Header="Voltage Drop Settings">
                        <StackPanel Margin="8">
                            <TextBlock Margin="0,0,0,8" Text="System Maximum Voltage Drop:" />
                            <StackPanel Orientation="Horizontal">
                                <RadioButton
                                    Margin="0,0,16,0"
                                    Content="5%"
                                    IsChecked="{Binding SystemVD5Percent}" />
                                <RadioButton Content="7%" IsChecked="{Binding SystemVD7Percent}" />
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>

                    <!--  Cable Selection  -->
                    <GroupBox Margin="0,0,0,16" Header="Cable Selection Standard">
                        <StackPanel Margin="8">
                            <TextBlock Margin="0,0,0,8" Text="Ambient Temperature Standard:" />
                            <StackPanel Orientation="Horizontal">
                                <RadioButton
                                    Margin="0,0,16,0"
                                    Content="New Zealand (30°C)"
                                    IsChecked="{Binding NZCableSelection}" />
                                <RadioButton Content="Australia (40°C)" IsChecked="{Binding AUSCableSelection}" />
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>

                    <!--  Advanced Settings  -->
                    <GroupBox Header="Advanced Calculation Settings">
                        <Grid Margin="8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="Discrimination Test Multiplier:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="0,0,0,8"
                                Text="{Binding ProjectInfo.DiscriminationTestMultiplier}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,8,8"
                                VerticalAlignment="Center"
                                Text="Ambient Temperature (°C):" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0,0,0,8"
                                Text="{Binding ProjectInfo.AmbientTemp}" />

                            <CheckBox
                                Grid.Row="2"
                                Grid.Column="0"
                                Grid.ColumnSpan="2"
                                Content="GPO Calculation at 80%"
                                IsChecked="{Binding ProjectInfo.GPOCalc80Perc}" />
                        </Grid>
                    </GroupBox>
                </StackPanel>
            </materialDesign:Card>

            <!--  Project Information  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="16">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Project Information" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="0"
                            Margin="0,0,8,8"
                            VerticalAlignment="Center"
                            Text="Job Name:" />
                        <TextBox
                            Grid.Row="0"
                            Grid.Column="1"
                            Margin="0,0,0,8"
                            Text="{Binding ProjectInfo.JobName}" />

                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="0"
                            Margin="0,0,8,8"
                            VerticalAlignment="Center"
                            Text="Job Number:" />
                        <TextBox
                            Grid.Row="1"
                            Grid.Column="1"
                            Margin="0,0,0,8"
                            Text="{Binding ProjectInfo.JobNumber}" />

                        <TextBlock
                            Grid.Row="2"
                            Grid.Column="0"
                            Margin="0,0,8,8"
                            VerticalAlignment="Center"
                            Text="Engineer:" />
                        <TextBox
                            Grid.Row="2"
                            Grid.Column="1"
                            Margin="0,0,0,8"
                            Text="{Binding ProjectInfo.Engineer}" />

                        <TextBlock
                            Grid.Row="3"
                            Grid.Column="0"
                            Margin="0,0,8,8"
                            VerticalAlignment="Center"
                            Text="Verifier:" />
                        <TextBox
                            Grid.Row="3"
                            Grid.Column="1"
                            Margin="0,0,0,8"
                            Text="{Binding ProjectInfo.Verifier}" />

                        <TextBlock
                            Grid.Row="4"
                            Grid.Column="0"
                            Margin="0,0,8,0"
                            VerticalAlignment="Center"
                            Text="Revision:" />
                        <TextBox
                            Grid.Row="4"
                            Grid.Column="1"
                            Text="{Binding ProjectInfo.Revision}" />
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Database Settings  -->
            <materialDesign:Card Padding="16">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Database Settings" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <TextBlock
                            Grid.Column="0"
                            Margin="0,0,8,0"
                            VerticalAlignment="Center"
                            Text="Database Path:" />
                        <TextBox
                            Grid.Column="1"
                            Margin="0,0,8,0"
                            IsReadOnly="True"
                            Text="{Binding ProjectInfo.DatabasePath}" />
                        <Button Grid.Column="2" Content="Browse..." />
                    </Grid>

                    <TextBlock
                        Margin="0,16,0,0"
                        FontStyle="Italic"
                        Foreground="{DynamicResource MaterialDesignBodyLight}"
                        Text="Database status and information will be displayed here." />
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </ScrollViewer>
</Page>
