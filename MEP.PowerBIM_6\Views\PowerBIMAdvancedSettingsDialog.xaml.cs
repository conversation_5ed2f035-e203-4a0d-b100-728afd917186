﻿using MEP.PowerBIM_6.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using MessageBox = System.Windows.MessageBox;

namespace MEP.PowerBIM_6.Views
{
    /// <summary>
    /// Interaction logic for PowerBIMAdvancedSettingsDialog.xaml
    /// </summary>
    public partial class PowerBIMAdvancedSettingsDialog : Window, INotifyPropertyChanged
    {
        #region Private Fields

        //private PowerBIMProjectInfoModel _projectInfo;
        //private PowerBIMProjectInfoModel _originalProjectInfo;

        // GPO Calculation Settings
        private bool _gpoCalc80Percent;
        private bool _gpoActualLoad = true;
        private bool _gpo1000Plus100;

        // Clearing Time Settings
        private bool _clearingTimePower04 = true;
        private bool _clearingTimePower5;
        private bool _clearingTimeLighting04 = true;
        private bool _clearingTimeLighting5;

        // Database Settings
        private string _databasePath;

        // Calculation Parameters
        private double _discriminationTestMultiplier = 1.25;
        private double _ambientTemperature = 30.0;
        private double _nodeCircuitPathTolerance = 100.0;
        private bool _enableAdvancedPathFinding = true;

        // Safety Factors
        private double _powerSafetyFactor = 1.25;
        private double _lightingSafetyFactor = 1.0;
        private double _otherSafetyFactor = 1.25;

        #endregion

        #region Public Properties

        public bool GPOCalc80Percent
        {
            get => _gpoCalc80Percent;
            set
            {
                _gpoCalc80Percent = value;
                OnPropertyChanged();
                if (value) { GPOActualLoad = false; GPO1000Plus100 = false; }
            }
        }

        public bool GPOActualLoad
        {
            get => _gpoActualLoad;
            set
            {
                _gpoActualLoad = value;
                OnPropertyChanged();
                if (value) { GPOCalc80Percent = false; GPO1000Plus100 = false; }
            }
        }

        public bool GPO1000Plus100
        {
            get => _gpo1000Plus100;
            set
            {
                _gpo1000Plus100 = value;
                OnPropertyChanged();
                if (value) { GPOCalc80Percent = false; GPOActualLoad = false; }
            }
        }

        public bool ClearingTimePower04
        {
            get => _clearingTimePower04;
            set
            {
                _clearingTimePower04 = value;
                OnPropertyChanged();
                if (value) ClearingTimePower5 = false;
            }
        }

        public bool ClearingTimePower5
        {
            get => _clearingTimePower5;
            set
            {
                _clearingTimePower5 = value;
                OnPropertyChanged();
                if (value) ClearingTimePower04 = false;
            }
        }

        public bool ClearingTimeLighting04
        {
            get => _clearingTimeLighting04;
            set
            {
                _clearingTimeLighting04 = value;
                OnPropertyChanged();
                if (value) ClearingTimeLighting5 = false;
            }
        }

        public bool ClearingTimeLighting5
        {
            get => _clearingTimeLighting5;
            set
            {
                _clearingTimeLighting5 = value;
                OnPropertyChanged();
                if (value) ClearingTimeLighting04 = false;
            }
        }

        public string DatabasePath
        {
            get => _databasePath;
            set
            {
                _databasePath = value;
                OnPropertyChanged();
            }
        }

        public double DiscriminationTestMultiplier
        {
            get => _discriminationTestMultiplier;
            set
            {
                _discriminationTestMultiplier = value;
                OnPropertyChanged();
            }
        }

        public double AmbientTemperature
        {
            get => _ambientTemperature;
            set
            {
                _ambientTemperature = value;
                OnPropertyChanged();
            }
        }

        public double NodeCircuitPathTolerance
        {
            get => _nodeCircuitPathTolerance;
            set
            {
                _nodeCircuitPathTolerance = value;
                OnPropertyChanged();
            }
        }

        public bool EnableAdvancedPathFinding
        {
            get => _enableAdvancedPathFinding;
            set
            {
                _enableAdvancedPathFinding = value;
                OnPropertyChanged();
            }
        }

        public double PowerSafetyFactor
        {
            get => _powerSafetyFactor;
            set
            {
                _powerSafetyFactor = value;
                OnPropertyChanged();
            }
        }

        public double LightingSafetyFactor
        {
            get => _lightingSafetyFactor;
            set
            {
                _lightingSafetyFactor = value;
                OnPropertyChanged();
            }
        }

        public double OtherSafetyFactor
        {
            get => _otherSafetyFactor;
            set
            {
                _otherSafetyFactor = value;
                OnPropertyChanged();
            }
        }

        #endregion

        public PowerBIMAdvancedSettingsDialog(/*PowerBIMProjectInfoModel projectInfo*/)
        {
            InitializeComponent();

            //_projectInfo = projectInfo;
            //_originalProjectInfo = projectInfo.Clone(); // Assume Clone method exists

            //LoadSettingsFromProjectInfo();

            //DataContext = this;

            //// Set dialog result to false initially
            //DialogResult = false;
        }

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region Static Methods

        ///// <summary>
        ///// Shows the advanced settings dialog
        ///// </summary>
        ///// <param name="owner">Owner window</param>
        ///// <param name="projectInfo">Project information model</param>
        ///// <returns>True if the user saved changes, false if cancelled</returns>
        //public static bool ShowDialog(Window owner, PowerBIMProjectInfoModel projectInfo)
        //{
        //    var dialog = new PowerBIMAdvancedSettingsDialog(projectInfo)
        //    {
        //        Owner = owner
        //    };

        //    return dialog.ShowDialog() == true;
        //}

        #endregion

        #region Private Methods

        private void LoadSettingsFromProjectInfo()
        {
            //// Load settings from project info
            //DatabasePath = _projectInfo.DatabasePath ?? string.Empty;
            //DiscriminationTestMultiplier = _projectInfo.DiscriminationTestMultiplier;
            //AmbientTemperature = _projectInfo.AmbientTemp;

            //// Load other settings with defaults
            //GPOActualLoad = _projectInfo.GPOCalc80Perc != true;
            //GPOCalc80Percent = _projectInfo.GPOCalc80Perc == true;
        }

        private void SaveSettingsToProjectInfo()
        {
            //_projectInfo.DatabasePath = DatabasePath;
            //_projectInfo.DiscriminationTestMultiplier = DiscriminationTestMultiplier;
            //_projectInfo.AmbientTemp = AmbientTemperature;
            //_projectInfo.GPOCalc80Perc = GPOCalc80Percent;

            //// Set other calculated properties
            //_projectInfo.ParametersChanged = true;
        }

        private void ResetToDefaults()
        {
            GPOActualLoad = true;
            GPOCalc80Percent = false;
            GPO1000Plus100 = false;

            ClearingTimePower04 = true;
            ClearingTimePower5 = false;
            ClearingTimeLighting04 = true;
            ClearingTimeLighting5 = false;

            DiscriminationTestMultiplier = 1.25;
            AmbientTemperature = 30.0;
            NodeCircuitPathTolerance = 100.0;
            EnableAdvancedPathFinding = true;

            PowerSafetyFactor = 1.25;
            LightingSafetyFactor = 1.0;
            OtherSafetyFactor = 1.25;
        }

        #endregion

        #region Event Handlers

        private void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveSettingsToProjectInfo();

                // Set dialog result to true to indicate success
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Error saving settings: {ex.Message}",
                    "PowerBIM Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            //// Restore original settings
            //_projectInfo.CopyFrom(_originalProjectInfo);

            // Set dialog result to false and close
            DialogResult = false;
            Close();
        }

        private void ResetDefaults_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "Are you sure you want to reset all settings to their default values?",
                "Reset to Defaults",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ResetToDefaults();
            }
        }

        private void BrowseDatabase_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx|All Files (*.*)|*.*",
                Title = "Select Cable Database File",
                InitialDirectory = Path.GetDirectoryName(DatabasePath) ?? Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
            };

            if (openFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                DatabasePath = openFileDialog.FileName;
            }
        }

        #endregion

    }
}
