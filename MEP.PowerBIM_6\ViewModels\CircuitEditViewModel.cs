using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Data;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Handlers;
using MEP.PowerBIM_6.Extensions;
using Microsoft.Extensions.DependencyInjection;
using MessageBox = System.Windows.MessageBox;
using BecaActivityLogger.CoreLogic.Data;

namespace MEP.PowerBIM_6.ViewModels
{
    /// <summary>
    /// Enhanced Circuit Edit ViewModel for PowerBIM 6 WPF application
    /// Manages the most critical form where users perform electrical calculations
    /// Preserves all functionality from FrmPowerBIM_CircuitEditEnhanced
    /// </summary>
    public partial class CircuitEditViewModel : BaseViewModel
    {
        #region Fields

        private PowerBIM_DBData _distributionBoardData;
        private PowerBIM_DBData _originalDistributionBoardData; // For undo functionality
        private PowerBIM_ProjectInfo _projectInfo;
        private ICollectionView _circuitDataView;
        private bool _autoCalculate = true;
        private bool _isReadOnly = false;

        #endregion

        #region Observable Properties

        /// <summary>
        /// Distribution board name for display
        /// </summary>
        [ObservableProperty]
        private string _distributionBoardName = "Unknown Distribution Board";

        /// <summary>
        /// Collection of circuit data for DataGrid binding
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<CircuitItemViewModel> _circuitData;

        /// <summary>
        /// Currently selected circuit
        /// </summary>
        [ObservableProperty]
        private CircuitItemViewModel _selectedCircuit;

        /// <summary>
        /// Search text for filtering circuits
        /// </summary>
        [ObservableProperty]
        private string _searchText = string.Empty;

        /// <summary>
        /// Auto-calculation toggle state
        /// </summary>
        [ObservableProperty]
        private bool _autoCalculateEnabled = true;

        /// <summary>
        /// Auto-calculation button text
        /// </summary>
        [ObservableProperty]
        private string _autoCalculateButtonText = "Auto Calc [ON]";

        /// <summary>
        /// Indicates if path editing is active
        /// </summary>
        [ObservableProperty]
        private bool _isPathEditingActive = false;

        /// <summary>
        /// Diversified phase loading data
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<PhaseLoadingData> _diversifiedPhaseData;

        /// <summary>
        /// Un-diversified phase loading data
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<PhaseLoadingData> _unDiversifiedPhaseData;

        /// <summary>
        /// Device ratings for ComboBox binding
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _deviceRatings;

        /// <summary>
        /// Curve types for ComboBox binding
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _curveTypes;

        /// <summary>
        /// Protection devices for ComboBox binding
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _protectionDevices;

        /// <summary>
        /// RCD protection options for ComboBox binding
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _rcdProtectionOptions;

        /// <summary>
        /// Cable types for ComboBox binding
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _cableTypes;

        /// <summary>
        /// Installation methods for ComboBox binding
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _installationMethods;

        /// <summary>
        /// Path modes for ComboBox binding
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> _pathModes;

        /// <summary>
        /// Tips text for user guidance
        /// </summary>
        [ObservableProperty]
        private string _tipsText = "Tips: Left-click to select, right-click and drag to copy data like Excel. Unsaved changes will be lost when closing.";

        #endregion

        #region Constructor

        public CircuitEditViewModel(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _logger?.Log("CircuitEditViewModel initialized", LogType.Information);

            // Initialize collections
            CircuitData = new ObservableCollection<CircuitItemViewModel>();
            DiversifiedPhaseData = new ObservableCollection<PhaseLoadingData>();
            UnDiversifiedPhaseData = new ObservableCollection<PhaseLoadingData>();
            
            // Initialize ComboBox data sources
            InitializeComboBoxDataSources();

            // Set up collection view for filtering
            _circuitDataView = CollectionViewSource.GetDefaultView(CircuitData);
            _circuitDataView.Filter = FilterCircuits;
        }

        #endregion

        #region Initialization Methods

        /// <summary>
        /// Initialize the ViewModel with distribution board data
        /// </summary>
        /// <param name="distributionBoardData">The distribution board data</param>
        /// <param name="projectInfo">The project information</param>
        public void Initialize(PowerBIM_DBData distributionBoardData, PowerBIM_ProjectInfo projectInfo)
        {
            try
            {
                _distributionBoardData = distributionBoardData;
                _projectInfo = projectInfo;
                _isReadOnly = distributionBoardData.IsManuallyLocked;

                DistributionBoardName = distributionBoardData.Schedule_DB_Name;

                // Store original state for undo functionality
                StoreOriginalState();

                // Load circuit data
                LoadCircuitData();

                // Load phase loading data
                LoadPhaseLoadingData();

                _logger?.Log($"CircuitEditViewModel initialized for DB: {DistributionBoardName}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log("Failed to initialize CircuitEditViewModel", LogType.Error);
                StatusMessage = $"Error initializing circuit editor: {ex.Message}";
            }
        }

        /// <summary>
        /// Initialize ComboBox data sources
        /// </summary>
        private void InitializeComboBoxDataSources()
        {
            // These will be populated from the actual data when Initialize is called
            DeviceRatings = new ObservableCollection<string>();
            CurveTypes = new ObservableCollection<string>();
            ProtectionDevices = new ObservableCollection<string>();
            RcdProtectionOptions = new ObservableCollection<string>();
            CableTypes = new ObservableCollection<string>();
            InstallationMethods = new ObservableCollection<string>();
            PathModes = new ObservableCollection<string>();
        }

        #endregion

        #region Data Loading Methods

        /// <summary>
        /// Load circuit data from distribution board
        /// </summary>
        private void LoadCircuitData()
        {
            if (_distributionBoardData?.CCTs == null) return;

            SetBusyState(true, "Loading circuit data...");

            try
            {
                CircuitData.Clear();

                var orderedCircuits = _distributionBoardData.CCTs
                    .OrderBy(cd => double.Parse(cd.CCT_Number.ExtractNumber()))
                    .ThenBy(cdata => GetCircuitNumberPriority(cdata.CCT_Number));

                foreach (var circuitData in orderedCircuits)
                {
                    // Run PowerBIM check for each circuit
                    circuitData.RunPowerBIMCheck();

                    // Create ViewModel wrapper
                    var circuitViewModel = new CircuitItemViewModel(circuitData, _projectInfo);
                    CircuitData.Add(circuitViewModel);
                }

                // Populate ComboBox data sources from actual data
                PopulateComboBoxDataSources();

                _logger?.Log($"Loaded {CircuitData.Count} circuits", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log("Failed to load circuit data", LogType.Error);
                StatusMessage = $"Error loading circuits: {ex.Message}";
            }
            finally
            {
                SetBusyState(false);
            }
        }

        /// <summary>
        /// Load phase loading summary data
        /// </summary>
        private void LoadPhaseLoadingData()
        {
            try
            {
                // TODO: Implement phase loading calculation
                // This would populate DiversifiedPhaseData and UnDiversifiedPhaseData
                // Based on the original PopulateDiversiviedUndiversifiedLoadTables method
                
                _logger?.Log("Phase loading data loaded", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log("Failed to load phase loading data", LogType.Error);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Store original state for undo functionality
        /// </summary>
        private void StoreOriginalState()
        {
            if (_distributionBoardData != null && _projectInfo != null)
            {
                _originalDistributionBoardData = new PowerBIM_DBData(_projectInfo, _distributionBoardData.DB_Element);
                _originalDistributionBoardData.Initialise_AllCircuits();
            }
        }

        /// <summary>
        /// Get circuit number priority for sorting
        /// </summary>
        private int GetCircuitNumberPriority(string circuitNumber)
        {
            // Implementation from original form
            if (string.IsNullOrEmpty(circuitNumber)) return int.MaxValue;

            // Extract non-numeric suffix for priority sorting
            var numericPart = circuitNumber.ExtractNumber();
            var suffix = circuitNumber.Replace(numericPart, "").Trim();

            return suffix switch
            {
                "" => 0,
                "A" => 1,
                "B" => 2,
                "C" => 3,
                _ => 999
            };
        }

        /// <summary>
        /// Populate ComboBox data sources from loaded circuit data
        /// </summary>
        private void PopulateComboBoxDataSources()
        {
            // Device Ratings
            DeviceRatings.Clear();
            var ratings = new[] { "6A", "10A", "16A", "20A", "25A", "32A", "40A", "50A", "63A", "80A", "100A", "125A" };
            foreach (var rating in ratings) DeviceRatings.Add(rating);

            // Curve Types
            CurveTypes.Clear();
            var curves = new[] { "B", "C", "D", "K" };
            foreach (var curve in curves) CurveTypes.Add(curve);

            // Protection Devices
            ProtectionDevices.Clear();
            var devices = new[] { "MCB", "MCCB", "RCD", "RCBO" };
            foreach (var device in devices) ProtectionDevices.Add(device);

            // RCD Protection
            RcdProtectionOptions.Clear();
            var rcdOptions = new[] { "None", "30mA", "100mA", "300mA" };
            foreach (var option in rcdOptions) RcdProtectionOptions.Add(option);

            // Cable Types
            CableTypes.Clear();
            var cables = new[] { "1.5mm²", "2.5mm²", "4mm²", "6mm²", "10mm²", "16mm²", "25mm²", "35mm²", "50mm²" };
            foreach (var cable in cables) CableTypes.Add(cable);

            // Installation Methods
            InstallationMethods.Clear();
            var methods = new[] { "Clipped Direct", "In Conduit", "In Trunking", "Underground", "Overhead" };
            foreach (var method in methods) InstallationMethods.Add(method);

            // Path Modes
            PathModes.Clear();
            var modes = new[] { "Automatic", "Manual", "Custom" };
            foreach (var mode in modes) PathModes.Add(mode);
        }

        /// <summary>
        /// Filter circuits based on search text
        /// </summary>
        private bool FilterCircuits(object item)
        {
            if (string.IsNullOrEmpty(SearchText)) return true;

            if (item is CircuitItemViewModel circuit)
            {
                // Explicitly specify the namespace to resolve ambiguity  
                return StringExtensions.Contains(circuit.CircuitNumber, SearchText, StringComparison.OrdinalIgnoreCase) ||
                       StringExtensions.Contains(circuit.Description, SearchText, StringComparison.OrdinalIgnoreCase);
            }

            return false;
        }

        /// <summary>
        /// Recalculate and refresh all circuit data
        /// </summary>
        private void RecalculateAllCircuits()
        {
            if (!_autoCalculate) return;

            SetBusyState(true, "Recalculating circuits...");

            try
            {
                foreach (var circuitViewModel in CircuitData)
                {
                    var circuitData = circuitViewModel.OriginalData;
                    circuitData.Refresh_DerrivedCircuitProperties();
                    circuitData.RunPowerBIMCheck();

                    // Update ViewModel from recalculated data
                    circuitViewModel.RefreshFromOriginalData();
                }

                _logger?.Log("All circuits recalculated", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log("Failed to recalculate circuits", LogType.Error);
                StatusMessage = $"Error recalculating: {ex.Message}";
            }
            finally
            {
                SetBusyState(false);
            }
        }

        #endregion

        #region Property Change Handlers

        /// <summary>
        /// Handle search text changes
        /// </summary>
        partial void OnSearchTextChanged(string value)
        {
            _circuitDataView?.Refresh();
        }

        /// <summary>
        /// Handle auto-calculate toggle changes
        /// </summary>
        partial void OnAutoCalculateEnabledChanged(bool value)
        {
            _autoCalculate = value;
            AutoCalculateButtonText = value ? "Auto Calc [ON]" : "Auto Calc [OFF]";

            if (value)
            {
                // Recalculate all when turning auto-calc back on
                RecalculateAllCircuits();
            }
        }

        /// <summary>
        /// Handle selected circuit changes
        /// </summary>
        partial void OnSelectedCircuitChanged(CircuitItemViewModel value)
        {
            if (value != null && _autoCalculate)
            {
                // Trigger recalculation for the selected circuit
                var circuitData = value.OriginalData;
                circuitData.Refresh_DerrivedCircuitProperties();
                circuitData.RunPowerBIMCheck();
                value.RefreshFromOriginalData();
            }
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to save circuit changes
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanSave))]
        private void Save()
        {
            try
            {
                SetBusyState(true, "Saving circuit data...");

                // Save all circuit changes back to original data
                foreach (var circuitViewModel in CircuitData)
                {
                    circuitViewModel.SaveToOriginalData();
                }

                // Commit to Revit via ExternalEvent
                MakeRequest(RequestId_PB6.SaveCircuitEditEnhanced);

                HasUnsavedChanges = false;
                _logger?.Log("Circuit data saved successfully", LogType.Information);
                StatusMessage = "Circuit data saved successfully";
            }
            catch (Exception ex)
            {
                _logger?.Log("Failed to save circuit data", LogType.Error);
                StatusMessage = $"Error saving: {ex.Message}";
            }
            finally
            {
                SetBusyState(false);
            }
        }

        private bool CanSave() => HasUnsavedChanges && !IsBusy && !_isReadOnly;

        /// <summary>
        /// Command to cancel changes and close window
        /// </summary>
        [RelayCommand]
        private void Cancel()
        {
            try
            {
                if (HasUnsavedChanges)
                {
                    var result = MessageBox.Show(
                        "You have unsaved changes. Are you sure you want to cancel?",
                        "Unsaved Changes",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result != MessageBoxResult.Yes)
                        return;
                }

                // Revert to original state if needed
                if (HasUnsavedChanges)
                {
                    RevertToOriginalState();
                }

                _logger?.Log("Circuit edit cancelled", LogType.Information);

                // Close window (handled by view)
                RequestClose?.Invoke();
            }
            catch (Exception ex)
            {
                _logger?.Log("Error during cancel operation", LogType.Error);
            }
        }

        /// <summary>
        /// Command to toggle auto-calculation
        /// </summary>
        [RelayCommand]
        private void ToggleAutoCalculate()
        {
            AutoCalculateEnabled = !AutoCalculateEnabled;
            _logger?.Log($"Auto-calculate toggled: {AutoCalculateEnabled}", LogType.Information);
        }

        /// <summary>
        /// Command to activate path editing mode
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanActivatePathEdit))]
        private void ActivatePathEdit()
        {
            try
            {
                IsPathEditingActive = true;
                MakeRequest(RequestId_PB6.ActivatePathEditView);
                _logger?.Log("Path editing activated", LogType.Information);
                StatusMessage = "Path editing mode activated";
            }
            catch (Exception ex)
            {
                _logger?.Log("Failed to activate path editing", LogType.Error);
                StatusMessage = $"Error activating path edit: {ex.Message}";
            }
        }

        private bool CanActivatePathEdit() => !IsBusy && !_isReadOnly;

        /// <summary>
        /// Command to set path for specific circuit
        /// </summary>
        [RelayCommand]
        private void SetPath(CircuitItemViewModel circuit)
        {
            if (circuit == null) return;

            try
            {
                SelectedCircuit = circuit;

                // Request path editing for specific circuit via ExternalEvent
                MakeRequest(RequestId_PB6.OpenPathCustomizing);

                _logger?.Log($"Setting path for circuit: {circuit.CircuitNumber}", LogType.Information);
                StatusMessage = $"Setting path for circuit {circuit.CircuitNumber}";
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to set path for circuit {circuit.CircuitNumber}", LogType.Error);
                StatusMessage = $"Error setting path: {ex.Message}";
            }
        }

        /// <summary>
        /// Command to clear search filter
        /// </summary>
        [RelayCommand]
        private void ClearSearch()
        {
            SearchText = string.Empty;
        }

        /// <summary>
        /// Command to recalculate all circuits manually
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanRecalculate))]
        private void Recalculate()
        {
            RecalculateAllCircuits();
        }

        private bool CanRecalculate() => !IsBusy && !_isReadOnly;

        /// <summary>
        /// Command to refresh data from Revit
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanRefresh))]
        private void RefreshFromRevit()
        {
            try
            {
                MakeRequest(RequestId_PB6.RefreshData);
                _logger?.Log("Refreshing data from Revit", LogType.Information);
                StatusMessage = "Refreshing data from Revit...";
            }
            catch (Exception ex)
            {
                _logger?.Log("Failed to refresh from Revit", LogType.Error);
                StatusMessage = $"Error refreshing: {ex.Message}";
            }
        }

        private bool CanRefresh() => !IsBusy;

        #endregion

        #region Public Methods

        /// <summary>
        /// Revert all changes to original state
        /// </summary>
        public void RevertToOriginalState()
        {
            if (_originalDistributionBoardData?.CCTs == null) return;

            try
            {
                // Restore original circuit data
                for (int i = 0; i < _distributionBoardData.CCTs.Count && i < _originalDistributionBoardData.CCTs.Count; i++)
                {
                    _distributionBoardData.CCTs[i] = _originalDistributionBoardData.CCTs[i];
                    _distributionBoardData.CCTs[i].Refresh_DerrivedCircuitProperties();
                    _distributionBoardData.CCTs[i].SetCircuitLengthToRevit();
                }

                // Reload UI data
                LoadCircuitData();
                HasUnsavedChanges = false;

                _logger?.Log("Reverted to original state", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log("Failed to revert to original state", LogType.Error);
            }
        }

        /// <summary>
        /// Event for requesting window close
        /// </summary>
        public event Action RequestClose;

        #endregion
    }
}
