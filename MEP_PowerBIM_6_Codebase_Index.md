# MEP.PowerBIM_6 Codebase Index

## Directory Structure

```
MEP.PowerBIM_6/
├── Converters/                 # WPF Value Converters
├── Extensions/                 # Extension Methods
├── Handlers/                   # Request Handlers & Window Management
├── Models/                     # Data Models & Core Logic
│   ├── CoreData/              # PowerBIM Core Data Structures
│   └── Enums/                 # Enumeration Types
├── RevitCommands/             # Revit Command Entry Points
├── Services/                  # Business Logic Services
│   └── Interfaces/            # Service Contracts
├── Stubs/                     # Stub Implementations
├── ViewModels/                # MVVM ViewModels
└── Views/                     # WPF Views & Windows
```

## Core Components

### Entry Points
- **PowerBIM_6_Command.cs** - Main Revit command entry point
- **ModelessMainWindowHandler.cs** - Window lifecycle management

### Dependency Injection
- **ServiceConfiguration.cs** - DI container configuration
- **Services/Interfaces/** - Service contracts
- **Services/** - Service implementations

### MVVM Architecture
- **BaseViewModel.cs** - Base class for all ViewModels
- **MainViewModel.cs** - Primary application ViewModel
- **ViewModels/** - Specialized ViewModels
- **Views/** - WPF Views and Windows

### Data Layer
- **Models/CoreData/** - PowerBIM core data structures
- **Models/** - WPF binding models
- **Models/Enums/** - Type definitions

## File Inventory

### Converters (2 files)
- `StatusColorConverter.cs` - Status to color conversion
- `ValidationConverters.cs` - Validation result converters

### Extensions (1 file)
- `StringExtensions.cs` - String utility methods

### Handlers (3 files)
- `ModelessMainWindowHandler.cs` - Main window lifecycle (429 lines)
- `RequestHandler_PB6.cs` - ExternalEvent request handling
- `Request_PB6_Configure.cs` - Request configuration

### Models (6 files + CoreData + Enums)
- `BreakerModel.cs` - Breaker data model
- `CableModel.cs` - Cable data model  
- `CircuitModel.cs` - Circuit data model
- `DistributionBoardModel.cs` - Distribution board model
- `ExportSettingsModel.cs` - Export configuration model
- `ProjectInfoModel.cs` - Project information model
- `SettingsModel.cs` - Application settings model

#### Models/CoreData (6 files)
- `PhaseLoadingData.cs` - Phase loading calculations
- `PowerBIM_BreakerData.cs` - Breaker core data
- `PowerBIM_CableData.cs` - Cable core data
- `PowerBIM_CircuitData.cs` - Circuit core data
- `PowerBIM_DBData.cs` - Distribution board core data
- `PowerBIM_ProjectInfo.cs` - Project information core data (603 lines)

#### Models/Enums
- Various enumeration types for PowerBIM operations

### RevitCommands (1 file)
- `PowerBIM_6_Command.cs` - Main command implementation (274 lines)

### Services (6 files)
- `CalculationService.cs` - Electrical calculations
- `DataService.cs` - Data transformation (382 lines)
- `NavigationMessage.cs` - Navigation messaging
- `NavigationService.cs` - Page navigation (432 lines)
- `RevitService.cs` - Revit API operations
- `ServiceConfiguration.cs` - DI configuration (234 lines)
- `StubServices.cs` - Placeholder implementations (308 lines)

#### Services/Interfaces (4 files)
- `IDataService.cs` - Data service contract (237 lines)
- `IExportService.cs` - Export service contract (184 lines)
- `INavigationService.cs` - Navigation service contract (88 lines)
- `IRevitService.cs` - Revit service contract (198 lines)

### Stubs (1 file)
- `StubClasses.cs` - Development stubs

### ViewModels (6 files)
- `BaseViewModel.cs` - Base ViewModel class (325 lines)
- `CircuitEditViewModel.cs` - Circuit editing ViewModel
- `CircuitItemViewModel.cs` - Circuit item ViewModel
- `DistributionBoardItemViewModel.cs` - DB item ViewModel
- `MainViewModel.cs` - Main application ViewModel (403 lines)
- `StubViewModels.cs` - Placeholder ViewModels

### Views (40+ files)
Comprehensive set of WPF views including:

#### Main Windows
- `MainWindow.xaml/.cs` - Original main window
- `MainWindowEnhanced.xaml/.cs` - Enhanced main window

#### Pages (Navigation-based)
- `HomePage.xaml/.cs` - Application home page
- `DistributionBoardsPage.xaml/.cs` - DB management page
- `CircuitsPage.xaml/.cs` - Circuit management page
- `BulkOperationsPage.xaml/.cs` - Bulk operations page
- `ProjectSettingsPage.xaml/.cs` - Project settings page
- `ExportPage.xaml/.cs` - Export functionality page
- `AboutPage.xaml/.cs` - About information page

#### Dialog Windows
- `CircuitEditWindow.xaml/.cs` - Circuit editing dialog
- `DbEditWindow.xaml/.cs` - DB editing dialog
- `AdvancedSettingsWindow.xaml/.cs` - Settings dialog
- `ExportWindow.xaml/.cs` - Export dialog

#### PowerBIM-Specific Views
- `PowerBIMCircuitEditPage.xaml/.cs` - Circuit editing
- `PowerBIMDistributionBoardsPage.xaml/.cs` - DB management
- `PowerBIMSettingsPage.xaml/.cs` - Settings management
- `PowerBIMResultsPage.xaml/.cs` - Results display
- `PowerBIMStartPage.xaml/.cs` - Start page
- Various dialog windows for specialized operations

## Key Architectural Patterns

### 1. Dependency Injection
- Microsoft.Extensions.DependencyInjection
- Service registration in ServiceConfiguration
- Constructor injection throughout

### 2. MVVM Pattern
- CommunityToolkit.Mvvm for ViewModels
- ObservableObject base classes
- RelayCommand implementations
- Property change notifications

### 3. Navigation Pattern
- Page-based navigation via NavigationService
- Frame-based navigation in MainWindow
- ViewModel injection into pages
- Navigation parameter passing

### 4. ExternalEvent Pattern
- RequestHandler_PB6 for Revit API safety
- Synchronous operations only
- Thread-safe UI updates

### 5. Service Layer Pattern
- Clear separation of concerns
- Interface-based service contracts
- Stub implementations for development

## Code Quality Indicators

### Documentation
- Comprehensive XML documentation
- Clear method and class descriptions
- Parameter and return value documentation

### Error Handling
- Try-catch blocks in critical operations
- Logging integration throughout
- Graceful error recovery

### Thread Safety
- REVIT-SAFE comments throughout
- No async/await patterns
- Proper UI thread marshaling

### Testability
- Interface-based design
- Dependency injection support
- Clear separation of concerns

## Development Status

### Completed Components
- ✅ Core DI infrastructure
- ✅ Navigation system
- ✅ Base MVVM architecture
- ✅ Service interfaces
- ✅ Data transformation layer
- ✅ Window management

### In Development
- 🔄 Service implementations
- 🔄 View implementations
- 🔄 Business logic integration

### Planned
- 📋 Full Revit API integration
- 📋 Electrical calculations
- 📋 Export/Import functionality
- 📋 Advanced UI features

## Integration Points

### Legacy PowerBIM Integration
- PowerBIM_DBData structures preserved
- PowerBIM_ProjectInfo maintained
- Gradual migration strategy

### Revit API Integration
- UIDocument/Document injection
- ExternalEvent architecture
- Thread-safe operations

### Beca Infrastructure Integration
- BecaActivityLoggerData logging
- BecaBaseCommand inheritance
- Beca naming conventions

This codebase represents a well-architected, modern WPF application with proper separation of concerns, comprehensive dependency injection, and Revit API compatibility.
