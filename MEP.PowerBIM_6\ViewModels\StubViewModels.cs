using System;
using BecaActivityLogger.CoreLogic.Data;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MEP.PowerBIM_6.Handlers;

namespace MEP.PowerBIM_6.ViewModels
{
    /// <summary>
    /// Stub ViewModels for windows that will be fully implemented in later phases
    /// These provide basic functionality to test the infrastructure
    /// </summary>

    // CircuitEditViewModel moved to separate file - see CircuitEditViewModel.cs

    public partial class DbEditViewModel : BaseViewModel
    {
        private readonly BecaActivityLoggerData _logger;

        [ObservableProperty]
        private string _distributionBoardName = "Unknown Distribution Board";

        [ObservableProperty]
        private string _importFilePath = string.Empty;

        public DbEditViewModel(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _logger?.Log("DbEditViewModel initialized (stub)", LogType.Information);
        }

        [RelayCommand]
        private void Save()
        {
            _logger?.Log("Save command executed (stub)", LogType.Information);
            MakeRequest(RequestId_PB6.SaveDistributionBoard);
        }

        [RelayCommand]
        private void Cancel()
        {
            _logger?.Log("Cancel command executed (stub)", LogType.Information);
            // TODO: Close window
        }

        [RelayCommand]
        private void ImportCsv()
        {
            _logger?.Log("Import CSV command executed (stub)", LogType.Information);
            MakeRequest(RequestId_PB6.ImportFromCsv);
        }

        [RelayCommand]
        private void ActivatePathEdit()
        {
            _logger?.Log("Activate path edit command executed (stub)", LogType.Information);
            MakeRequest(RequestId_PB6.ActivatePathEditView);
        }
    }

    public partial class AdvancedSettingsViewModel : BaseViewModel
    {
        private readonly BecaActivityLoggerData _logger;

        [ObservableProperty]
        private bool _autoCalculate = true;

        [ObservableProperty]
        private double _voltageDropLimit = 5.0;

        [ObservableProperty]
        private int _ambientTemperature = 30;

        public AdvancedSettingsViewModel(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _logger?.Log("AdvancedSettingsViewModel initialized (stub)", LogType.Information);
        }

        [RelayCommand]
        private void Apply()
        {
            _logger?.Log("Apply command executed (stub)", LogType.Information);
            MakeRequest(RequestId_PB6.CommitAdvancedSettings);
        }

        [RelayCommand]
        private void Cancel()
        {
            _logger?.Log("Cancel command executed (stub)", LogType.Information);
            // TODO: Close window
        }

        [RelayCommand]
        private void ResetToDefaults()
        {
            _logger?.Log("Reset to defaults command executed (stub)", LogType.Information);
            AutoCalculate = true;
            VoltageDropLimit = 5.0;
            AmbientTemperature = 30;
        }
    }

    public partial class DbSettingsViewModel : BaseViewModel
    {
        private readonly BecaActivityLoggerData _logger;

        [ObservableProperty]
        private string _distributionBoardName = "Unknown Distribution Board";

        [ObservableProperty]
        private bool _isLocked;

        [ObservableProperty]
        private string _description = string.Empty;

        public DbSettingsViewModel(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _logger?.Log("DbSettingsViewModel initialized (stub)", LogType.Information);
        }

        [RelayCommand]
        private void Apply()
        {
            _logger?.Log("Apply command executed (stub)", LogType.Information);
            MakeRequest(RequestId_PB6.CommitDistributionBoardSettings);
        }

        [RelayCommand]
        private void Cancel()
        {
            _logger?.Log("Cancel command executed (stub)", LogType.Information);
            // TODO: Close window
        }
    }

    public partial class ExportViewModel : BaseViewModel
    {
        private readonly BecaActivityLoggerData _logger;

        [ObservableProperty]
        private string _outputPath = string.Empty;

        [ObservableProperty]
        private bool _includeImages;

        [ObservableProperty]
        private bool _includeCalculations = true;

        [ObservableProperty]
        private string _selectedFormat = "Excel";

        public ExportViewModel(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _logger?.Log("ExportViewModel initialized (stub)", LogType.Information);
        }

        [RelayCommand]
        private void Export()
        {
            _logger?.Log("Export command executed (stub)", LogType.Information);
            MakeRequest(RequestId_PB6.ExportData);
        }

        [RelayCommand]
        private void Cancel()
        {
            _logger?.Log("Cancel command executed (stub)", LogType.Information);
            // TODO: Close window
        }

        [RelayCommand]
        private void BrowseOutputPath()
        {
            _logger?.Log("Browse output path command executed (stub)", LogType.Information);
            // TODO: Show file dialog
        }
    }

    public partial class ImportSettingsViewModel : BaseViewModel
    {
        private readonly BecaActivityLoggerData _logger;

        [ObservableProperty]
        private string _filePath = string.Empty;

        [ObservableProperty]
        private bool _hasHeaders = true;

        [ObservableProperty]
        private string _delimiter = ",";

        [ObservableProperty]
        private bool _overwriteExisting;

        public ImportSettingsViewModel(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _logger?.Log("ImportSettingsViewModel initialized (stub)", LogType.Information);
        }
 
        [RelayCommand]
        private void Import()
        {
            _logger?.Log("Import command executed (stub)", LogType.Information);
            MakeRequest(RequestId_PB6.ProcessImportSettings);
        }

        [RelayCommand]
        private void Cancel()
        {
            _logger?.Log("Cancel command executed (stub)", LogType.Information);
            // TODO: Close window
        }

        [RelayCommand]
        private void BrowseFile()
        {
            _logger?.Log("Browse file command executed (stub)", LogType.Information);
            // TODO: Show file dialog
        }
    }

    public partial class ProjectInfoViewModel : BaseViewModel
    {
        private readonly BecaActivityLoggerData _logger;

        [ObservableProperty]
        private string _jobName = string.Empty;

        [ObservableProperty]
        private string _engineer = string.Empty;

        [ObservableProperty]
        private double _systemVoltageDropMax = 5.0;

        [ObservableProperty]
        private int _ambientTemperature = 30;

        public ProjectInfoViewModel(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _logger?.Log("ProjectInfoViewModel initialized (stub)", LogType.Information);
        }

        [RelayCommand]
        private void Save()
        {
            _logger?.Log("Save command executed (stub)", LogType.Information);
            MakeRequest(RequestId_PB6.CommitProjectInfo);
        }

        [RelayCommand]
        private void Reset()
        {
            _logger?.Log("Reset command executed (stub)", LogType.Information);
            SystemVoltageDropMax = 5.0;
            AmbientTemperature = 30;
        }
    }
}
