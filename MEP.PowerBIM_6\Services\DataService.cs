using System;
using System.Collections.Generic;
using System.Linq;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Services.Interfaces;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Implementation of IDataService for PowerBIM 6
    /// Handles data transformation and business logic operations
    /// </summary>
    public class DataService : IDataService
    {
        #region Fields

        private readonly BecaActivityLoggerData _logger;
        private readonly Dictionary<string, object> _cache;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the DataService
        /// </summary>
        /// <param name="logger">Beca activity logger for tracking operations</param>
        public DataService(BecaActivityLoggerData logger)
        {
            _logger = logger;
            _cache = new Dictionary<string, object>();
        }

        #endregion

        #region Data Transformation

        /// <summary>
        /// Convert PowerBIM_ProjectInfo to ProjectInfoModel
        /// </summary>
        /// <param name="projectInfo">Core project info</param>
        /// <returns>Project info model for WPF binding</returns>
        public ProjectInfoModel ConvertToModel(PowerBIM_ProjectInfo projectInfo)
        {
            try
            {
                if (projectInfo == null)
                    return new ProjectInfoModel(new PowerBIM_ProjectInfo());

                return new ProjectInfoModel(projectInfo);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to convert PowerBIM_ProjectInfo to model: {ex.Message}", LogType.Error);
                return new ProjectInfoModel(new PowerBIM_ProjectInfo());
            }
        }

        /// <summary>
        /// Convert ProjectInfoModel back to PowerBIM_ProjectInfo
        /// </summary>
        /// <param name="model">Project info model</param>
        /// <param name="originalProjectInfo">Original project info to update</param>
        /// <returns>Updated core project info</returns>
        public PowerBIM_ProjectInfo ConvertFromModel(ProjectInfoModel model, PowerBIM_ProjectInfo originalProjectInfo)
        {
            try
            {
                if (model == null || originalProjectInfo == null)
                    return originalProjectInfo;

                originalProjectInfo.JobName = model.JobName;
                originalProjectInfo.Engineer = model.Engineer;
                originalProjectInfo.SystemVoltageDropMax = model.SystemVoltageDropMax;
                originalProjectInfo.AmbientTemperature = model.AmbientTemperature;
                // Update other properties as needed

                return originalProjectInfo;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to convert ProjectInfoModel to PowerBIM_ProjectInfo: {ex.Message}", LogType.Error);
                return originalProjectInfo;
            }
        }

        /// <summary>
        /// Convert PowerBIM_DBData to DistributionBoardModel
        /// </summary>
        /// <param name="dbData">Core distribution board data</param>
        /// <returns>Distribution board model for WPF binding</returns>
        public DistributionBoardModel ConvertToModel(PowerBIM_DBData dbData)
        {
            try
            {
                return new DistributionBoardModel(dbData);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to convert PowerBIM_DBData to model: {ex.Message}", LogType.Error);
                throw;
            }
        }

        /// <summary>
        /// Convert PowerBIM_CircuitData to CircuitModel
        /// </summary>
        /// <param name="circuitData">Core circuit data</param>
        /// <returns>Circuit model for WPF binding</returns>
        public CircuitModel ConvertToModel(PowerBIM_CircuitData circuitData)
        {
            try
            {
                // TODO: Implement CircuitModel conversion
                // return new CircuitModel(circuitData);
                throw new NotImplementedException("CircuitModel conversion will be implemented in Phase 3");
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to convert PowerBIM_CircuitData to model: {ex.Message}", LogType.Error);
                throw;
            }
        }

        /// <summary>
        /// Convert list of PowerBIM_DBData to list of DistributionBoardModel
        /// </summary>
        /// <param name="dbDataList">List of core distribution board data</param>
        /// <returns>List of distribution board models</returns>
        public List<DistributionBoardModel> ConvertToModels(List<PowerBIM_DBData> dbDataList)
        {
            try
            {
                if (dbDataList == null)
                    return new List<DistributionBoardModel>();

                return dbDataList.Select(ConvertToModel).ToList();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to convert distribution board list to models: {ex.Message}", LogType.Error);
                return new List<DistributionBoardModel>();
            }
        }

        #endregion

        #region Data Validation

        /// <summary>
        /// Validate project information
        /// </summary>
        /// <param name="projectInfo">Project info to validate</param>
        /// <returns>Validation result with errors if any</returns>
        public ValidationResult ValidateProjectInfo(ProjectInfoModel projectInfo)
        {
            var result = new ValidationResult();

            try
            {
                if (projectInfo == null)
                {
                    result.AddError("Project information is required");
                    return result;
                }

                if (string.IsNullOrWhiteSpace(projectInfo.JobName))
                {
                    result.AddError("Job name is required");
                }

                if (projectInfo.SystemVoltageDropMax <= 0 || projectInfo.SystemVoltageDropMax > 100)
                {
                    result.AddError("System voltage drop must be between 0 and 100%");
                }

                if (projectInfo.AmbientTemperature < -50 || projectInfo.AmbientTemperature > 100)
                {
                    result.AddWarning("Ambient temperature seems unusual");
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error validating project info: {ex.Message}", LogType.Error);
                result.AddError($"Validation error: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Validate distribution board information
        /// </summary>
        /// <param name="distributionBoard">Distribution board to validate</param>
        /// <returns>Validation result with errors if any</returns>
        public ValidationResult ValidateDistributionBoard(DistributionBoardModel distributionBoard)
        {
            var result = new ValidationResult();

            try
            {
                if (distributionBoard == null)
                {
                    result.AddError("Distribution board is required");
                    return result;
                }

                if (string.IsNullOrWhiteSpace(distributionBoard.Name))
                {
                    result.AddError("Distribution board name is required");
                }

                if (distributionBoard.CircuitCount == 0)
                {
                    result.AddWarning("Distribution board has no circuits");
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error validating Distribution board: {ex.Message}", LogType.Error);
                result.AddError($"Validation error: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Validate circuit information
        /// </summary>
        /// <param name="circuit">Circuit to validate</param>
        /// <returns>Validation result with errors if any</returns>
        public ValidationResult ValidateCircuit(CircuitModel circuit)
        {
            var result = new ValidationResult();

            try
            {
                // TODO: Implement circuit validation
                result.AddError("Circuit validation not yet implemented");
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error validating circuit: {ex.Message}", LogType.Error);
                result.AddError($"Validation error: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Validate bulk edit settings
        /// </summary>
        /// <param name="settings">Settings to validate</param>
        /// <returns>Validation result with errors if any</returns>
        public ValidationResult ValidateBulkEditSettings(BulkEditSettings settings)
        {
            var result = new ValidationResult();

            try
            {
                if (settings == null)
                {
                    result.AddError("Bulk edit settings are required");
                    return result;
                }

                if (string.IsNullOrWhiteSpace(settings.CircuitType))
                {
                    result.AddError("Circuit type is required");
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error validating bulk edit settings: {ex.Message}", LogType.Error);
                result.AddError($"Validation error: {ex.Message}");
            }

            return result;
        }

        #endregion

        #region Data Processing (Stub Implementations)

        public List<DistributionBoardModel> ProcessDistributionBoardSummary(List<DistributionBoardModel> distributionBoards)
        {
            // REVIT-SAFE: No async - executes synchronously
            // TODO: Implement Distribution board summary processing
            return distributionBoards;
        }

        public CircuitModel CalculateCircuitProperties(CircuitModel circuit)
        {
            // REVIT-SAFE: No async - executes synchronously
            // TODO: Implement circuit calculation
            return circuit;
        }

        public List<CircuitModel> ProcessCircuitValidation(List<CircuitModel> circuits)
        {
            // REVIT-SAFE: No async - executes synchronously
            // TODO: Implement circuit validation processing
            return circuits;
        }

        #endregion

        #region Filtering and Searching (Stub Implementations)

        public List<CircuitModel> FilterCircuits(List<CircuitModel> circuits, string searchText, FilterCriteria filterCriteria = null)
        {
            // TODO: Implement circuit filtering
            return circuits ?? new List<CircuitModel>();
        }

        public List<DistributionBoardModel> FilterDistributionBoards(List<DistributionBoardModel> distributionBoards, string searchText, FilterCriteria filterCriteria = null)
        {
            if (distributionBoards == null) return new List<DistributionBoardModel>();
            
            if (string.IsNullOrWhiteSpace(searchText))
                return distributionBoards;

            return distributionBoards.Where(db => 
                db.Name?.Contains(searchText, StringComparison.OrdinalIgnoreCase) == true ||
                db.Status?.Contains(searchText, StringComparison.OrdinalIgnoreCase) == true
            ).ToList();
        }

        #endregion

        #region Settings Management (Stub Implementations)

        public SettingsModel LoadSettings()
        {
            // REVIT-SAFE: No async - executes synchronously
            // TODO: Implement settings loading
            return new SettingsModel();
        }

        public bool SaveSettings(SettingsModel settings)
        {
            // REVIT-SAFE: No async - executes synchronously
            // TODO: Implement settings saving
            return true;
        }

        public SettingsModel GetDefaultSettings()
        {
            return new SettingsModel();
        }

        #endregion

        #region Data Caching

        public void ClearCache()
        {
            _cache.Clear();
        }

        public bool RefreshCache()
        {
            // REVIT-SAFE: No async - executes synchronously
            return true;
        }

        public T GetCachedData<T>(string key) where T : class
        {
            return _cache.TryGetValue(key, out var value) ? value as T : null;
        }

        public void SetCachedData<T>(string key, T data, TimeSpan? expiration = null) where T : class
        {
            _cache[key] = data;
            // TODO: Implement expiration logic
        }

        #endregion
    }
}
